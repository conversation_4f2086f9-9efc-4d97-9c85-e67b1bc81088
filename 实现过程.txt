===============================================================================
                    2025916750 数据采集与监测系统介绍文档
===============================================================================

项目概述
--------
本系统是基于GD32F470VET6微控制器的数据采集与监测系统，具备电压采集、数据存储、
实时监控、数据加密等功能。系统支持TF卡存储、OLED显示、串口通信等多种接口。

硬件平台
--------
- 主控芯片：GD32F470VET6
- 存储设备：SPI Flash + TF卡
- 显示设备：OLED屏幕
- 通信接口：UART串口
- 采集模块：ADC电压采集
- 指示设备：LED指示灯
- 时钟模块：RTC实时时钟
- 输入设备：按键控制（KEY1采样控制，KEY2-4周期设置）

开发环境
--------
- 开发工具：Keil MDK
- CMSIS版本：5.4.0
- 编译环境：GorgonMeducer
- 内存使用：RAM 13.04KB/192KB (6.79%)
- 代码大小：Flash 50.02KB/512KB (9.77%)

系统架构
--------
本系统采用裸机开发模式，不依赖任何实时操作系统(RTOS)。系统核心采用自研的轻量级
协作式任务调度器(scheduler.c)，通过时间片轮询的方式实现多任务管理：

调度器特性：
- 非抢占式调度：所有任务在主循环中按时间周期顺序执行
- 时间驱动：基于系统毫秒计时器，每个任务有独立的执行周期
- 零开销切换：无上下文切换开销，所有任务共享同一个栈空间
- 确定性执行：任务执行顺序和时间完全可预测
- 资源高效：相比RTOS节省大量RAM和Flash空间

任务列表：
- LED任务：1ms周期，负责LED状态更新
- OLED任务：500ms周期，负责屏幕显示刷新
- 按键任务：5ms周期，负责按键扫描和处理
- UART任务：5ms周期，负责串口数据处理
- RTC任务：500ms周期，负责实时时钟管理
- 采样任务：100ms周期，负责ADC数据采集
- 日志同步：100ms周期，负责数据持久化

这种设计适合对实时性要求不高但需要多任务协调的嵌入式应用场景。

系统功能特性
------------
1. 电压数据采集与显示
2. 可配置的变比和阈值设置
3. 超阈值报警功能
4. 数据加密存储
5. 操作日志记录
6. 配置文件读取
7. 实时时钟管理
8. 系统自检功能

===============================================================================
                              系统工作流程
===============================================================================

1. 系统上电初始化
-----------------
系统上电后自动执行以下初始化流程：

1.1 启动信息显示
   - 串口输出："====system init===="
   - 从Flash读取设备ID并显示："Device_ID:2025-CMIC-2025916750"
   - 串口输出："====system ready===="
   - OLED第一行显示："system idle"

1.2 配置加载
   - 自动从Flash加载ratio和limit配置参数
   - 自动从Flash加载采样周期设置
   - 如果系统已初始化，自动尝试挂载TF卡并创建日志文件

2. 时钟设置
-----------
2.1 RTC配置命令
   串口输入：RTC Config
   系统响应：Input Datatime

2.2 时间输入格式
   输入格式：YYYY MM DD HH:MM:SS
   示例输入：2025 01 01 12:00:30

2.3 配置确认
   成功响应：RTC Config success
   时间显示：Time:2025-1-1 12:00:30

2.4 时间查询
   串口输入：RTC now
   系统响应：Current Time 2025-1-1 12:00:30

3. 系统检测功能
---------------
3.1 自检命令
   串口输入：test

3.2 检测项目
   - Flash存储器检测：flash......OK / flashID: 0x......
   - TF卡检测：TF card......OK / TF card......error
   - TF卡容量显示：TF card memory: XXXX KB total, XXXX KB free
   - 当前时间显示：Current Time 2025-1-1 12:00:30
   - 完成标识：=====system selftest=====

3.3 日志文件创建
   第一次test成功：
   - 创建log0.txt：记录test信息和系统初始化信息，完成后关闭
   - 立即创建log1.txt：记录test成功后的所有后续操作

   后续上电：
   - 第一次重启：自动创建log2.txt
   - 第二次重启：自动创建log3.txt
   - 第三次重启：自动创建log4.txt...（递增序列）

4. 数据采集与参数设置
---------------------
4.1 变比设置
   串口输入：ratio
   系统显示：Ratio=1.99
   系统提示：Input value(0~100):
   输入范围：0.00-100.00（小数点后两位）

4.2 阈值设置
   串口输入：limit
   系统显示：Limit=10.11
   系统提示：Input value(0~200):
   输入范围：0.00-200.00（小数点后两位）

4.3 参数保存
   串口输入：config save
   系统响应：save parameters to flash

4.4 参数读取
   串口输入：config read
   系统响应：read parameters from flash
   显示内容：Ratio=1.99 / Limit=10.11

5. 数据采集控制
---------------
5.1 开始采集
   方式1 - 串口命令：
   串口输入：start
   系统响应：Sampling started

   方式2 - 按键控制：
   按下KEY1：开始/停止采样切换

   采集状态显示：
   OLED显示：第一行显示时间，第二行显示电压值
   串口输出：时间戳 + 电压值（如：2025-1-1 12:00:30 1.55V）

5.2 停止采集
   方式1 - 串口命令：
   串口输入：stop
   系统响应：Sampling stopped

   方式2 - 按键控制：
   再次按下KEY1：停止采样

   停止状态显示：
   OLED显示：system idle

5.3 采样周期控制
   支持周期：5秒、10秒、15秒三种采样周期

   按键控制：
   - KEY2：设置5秒采样周期
   - KEY3：设置10秒采样周期
   - KEY4：设置15秒采样周期

   设置特性：
   - 设置立即生效并保存到Flash
   - 系统重启后自动加载保存的周期设置
   - OLED刷新频率跟随采样周期变化

6. 超阈值监测
-------------
6.1 阈值比较
   - 实时比较采集电压与设定阈值
   - 电压计算公式：显示电压 = ratio × 测量电压

6.2 报警指示
   - 超限时LED2常亮
   - 串口输出电压值正常显示
   - OLED显示电压值正常显示

7. 数据加密功能
---------------
7.1 启用加密
   串口输入：hide
   系统响应：Hide mode ON - HEX output enabled

7.2 加密格式
   输出格式：TTTTTTTTIIIIFFFFS
   - T：Unix时间戳（8位HEX = 4字节，已减8小时时差）
   - I：电压整数部分（4位HEX = 2字节）
   - F：电压小数部分（4位HEX = 2字节，小数×65536）
   - S：超限标志（*表示超限，无则为空）

   电压总计：4字节HEX（整数2字节 + 小数2字节）
   小数计算：小数部分 × 65536，例如0.5 × 65536 = 32768 → 8000

   示例：6850C385002070A3*
   解析：时间戳6850C385 + 整数0020(32V) + 小数70A3(0.88×65536=57507) + 超限*

7.3 取消加密
   串口输入：unhide
   系统响应：Hide mode OFF - Normal output enabled

8. 数据存储功能
---------------
8.1 存储目录结构
   TF卡根目录需手动创建以下文件夹：
   - /log/        - 操作日志文件
   - /sample/     - 正常采样数据
   - /hideData/   - 加密模式数据
   - /overLimit/  - 超限数据

8.2 文件命名规则
   - 日志文件：log0.txt, log1.txt, log2.txt...
   - 采样文件：sampleDataYYYYMMDDHHMMSS.txt
   - 加密文件：hideDataYYYYMMDDHHMMSS.txt
   - 超限文件：overLimitYYYYMMDDHHMMSS.txt

8.3 文件内容格式
   - 每个文件最多存储10条记录
   - 超过10条自动创建新文件
   - 记录格式：时间戳 + 电压值

9. 操作审计功能
---------------
9.1 日志记录内容
   - 所有串口输入命令（格式：IN: 命令）
   - 所有串口输出内容（格式：OUT: 内容）
   - 系统操作事件（如启动、采样开始/停止等）

9.2 日志文件管理
   第一次test成功：
   - 创建log0.txt记录test和初始化信息
   - 关闭log0.txt后立即创建log1.txt记录后续操作

   文件内容分工：
   - log0.txt：仅记录test成功信息和系统初始化
   - log1.txt：记录test后所有操作（配置修改、start/stop、config.ini读取等）
   - log2.txt及以后：每次重新上电的操作记录

10. 配置文件读取
----------------
10.1 配置文件格式（config.ini）
    [Ratio]
    Ch0=1.99

    [Limit]
    Ch0=10.11

10.2 读取命令
    串口输入：conf
    成功响应：显示读取的Ratio和Limit值
    失败响应：提示创建配置文件的格式

===============================================================================
                              命令参考手册
===============================================================================

基础命令
--------
test          - 系统自检
start         - 开始数据采集（也可用KEY1）
stop          - 停止数据采集（也可用KEY1）
hide          - 启用数据加密
unhide        - 关闭数据加密

按键控制
--------
KEY1          - 采样开始/停止切换
KEY2          - 设置5秒采样周期
KEY3          - 设置10秒采样周期
KEY4          - 设置15秒采样周期

配置命令
--------
ratio         - 设置变比参数
limit         - 设置阈值参数
config save   - 保存参数到Flash
config read   - 从Flash读取参数
conf          - 从TF卡读取配置文件

时钟命令
--------
RTC Config    - 配置RTC时间
RTC now       - 显示当前时间

系统命令
--------
log reset     - 重置日志系统
format        - 格式化TF卡（慎用）

===============================================================================
                              注意事项
===============================================================================

1. TF卡准备
   - 使用前需手动创建log、sample、hideData、overLimit四个文件夹
   - 支持长文件名，建议使用FAT32格式

2. 首次使用
   - 第一次使用必须执行test命令初始化系统
   - test成功后系统自动创建log0.txt开始记录

3. 参数设置
   - ratio和limit参数支持小数点后两位精度
   - 参数修改后需执行"config save"保存到Flash
   - 系统重启自动加载Flash中保存的参数

4. 数据精度
   - 电压显示精度：0.01V
   - 时间精度：秒级
   - hide模式时间自动减8小时（时差修正）

5. 系统限制
   - 每个数据文件最多10条记录
   - 支持的电压范围：0-65.535V
   - 采样周期：5秒/10秒/15秒可选
   - 内存占用：仅使用6.79%的RAM资源
   - 代码大小：仅占用9.77%的Flash空间

6. 控制方式
   - 串口命令控制：完整的命令集支持
   - 按键物理控制：KEY1采样控制，KEY2-4周期设置
   - 双重控制保障：提供灵活的操作方式

===============================================================================
                              技术实现细节
===============================================================================

任务调度器实现原理
----------------
本系统的scheduler.c实现了一个轻量级的协作式任务调度器，其核心原理如下：

1. 数据结构设计
   typedef struct {
       void (*task_func)(void);    // 任务函数指针
       uint32_t rate_ms;           // 任务执行周期（毫秒）
       uint32_t last_run;          // 上次执行时间（毫秒）
   } task_t;

2. 调度算法
   - 时间轮询：主循环中不断检查每个任务的执行时间
   - 周期判断：当前时间 >= (上次执行时间 + 执行周期) 时触发任务
   - 顺序执行：按任务数组顺序依次检查和执行，无优先级概念
   - 非阻塞：所有任务必须快速返回，不能包含阻塞操作

3. 与RTOS的区别
   - 无上下文切换：所有任务在同一个栈中执行
   - 无优先级：任务按固定顺序轮询执行
   - 无信号量/互斥锁：通过全局变量和标志位进行任务间通信
   - 无内存管理：静态分配所有资源
   - 确定性：任务执行时间和顺序完全可预测

4. 适用场景
   - 对实时性要求不严格的应用
   - 资源受限的嵌入式系统
   - 任务执行时间较短且可预测
   - 不需要复杂任务间同步的场景

5. 性能特点
   - 内存开销极小：仅需任务数组的存储空间
   - CPU开销低：无上下文切换和调度算法开销
   - 响应时间：取决于所有任务的总执行时间
   - 可靠性高：无死锁、优先级反转等RTOS常见问题

===============================================================================
                              版本信息
===============================================================================

项目名称：2025916750数据采集系统
设备ID：2025-CMIC-2025916750
开发平台：Keil MDK + GD32F470VET6
开发环境：GorgonMeducer + CMSIS 5.4.0
文件系统：FATFS
存储设备：SPI Flash + TF卡

资源使用情况
------------
RAM使用：13.04KB / 192KB (6.79%)
Flash使用：50.02KB / 512KB (9.77%)
编译工具：Keil5_disp_size_bar显示资源占用
代码优化：高效的内存和存储空间利用

===============================================================================