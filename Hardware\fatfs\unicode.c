/*------------------------------------------------------------------------*/
/* Unicode - Local code bidirectional converter for FATFS LFN support    */
/* Based on ChaN's ccsbcs.c for CP437 (US OEM)                           */
/*------------------------------------------------------------------------*/

#include "ff.h"

#if _USE_LFN != 0

/* CP437 (US OEM) to Unicode conversion table */
static const WCHAR Tbl[] = {
    0x00C7, 0x00FC, 0x00E9, 0x00E2, 0x00E4, 0x00E0, 0x00E5, 0x00E7,
    0x00EA, 0x00EB, 0x00E8, 0x00EF, 0x00EE, 0x00EC, 0x00C4, 0x00C5,
    0x00C9, 0x00E6, 0x00C6, 0x00F4, 0x00F6, 0x00F2, 0x00FB, 0x00F9,
    0x00FF, 0x00D6, 0x00DC, 0x00A2, 0x00A3, 0x00A5, 0x20A7, 0x0192,
    0x00E1, 0x00ED, 0x00F3, 0x00FA, 0x00F1, 0x00D1, 0x00AA, 0x00BA,
    0x00BF, 0x2310, 0x00AC, 0x00BD, 0x00BC, 0x00A1, 0x00AB, 0x00BB,
    0x2591, 0x2592, 0x2593, 0x2502, 0x2524, 0x2561, 0x2562, 0x2556,
    0x2555, 0x2563, 0x2551, 0x2557, 0x255D, 0x255C, 0x255B, 0x2510,
    0x2514, 0x2534, 0x252C, 0x251C, 0x2500, 0x253C, 0x255E, 0x255F,
    0x255A, 0x2554, 0x2569, 0x2566, 0x2560, 0x2550, 0x256C, 0x2567,
    0x2568, 0x2564, 0x2565, 0x2559, 0x2558, 0x2552, 0x2553, 0x256B,
    0x256A, 0x2518, 0x250C, 0x2588, 0x2584, 0x258C, 0x2590, 0x2580,
    0x03B1, 0x00DF, 0x0393, 0x03C0, 0x03A3, 0x03C3, 0x00B5, 0x03C4,
    0x03A6, 0x0398, 0x03A9, 0x03B4, 0x221E, 0x03C6, 0x03B5, 0x2229,
    0x2261, 0x00B1, 0x2265, 0x2264, 0x2320, 0x2321, 0x00F7, 0x2248,
    0x00B0, 0x2219, 0x00B7, 0x221A, 0x207F, 0x00B2, 0x25A0, 0x00A0
};

/*------------------------------------------------------------------------*/
/* OEM <==> Unicode conversions                                          */
/*------------------------------------------------------------------------*/

WCHAR ff_convert (	/* Converted code, 0 means conversion error */
	WCHAR	chr,	/* Character code to be converted */
	UINT	dir		/* 0: OEM->Unicode, 1: Unicode->OEM */
)
{
	WCHAR c;

	if (chr < 0x80) {	/* ASCII */
		c = chr;

	} else {
		if (dir) {	/* Unicode -> OEM */
			for (c = 0; c < 0x80; c++) {
				if (chr == Tbl[c]) break;
			}
			c = (c + 0x80) & 0xFF;
		} else {		/* OEM -> Unicode */
			c = (chr >= 0x100) ? 0 : Tbl[chr - 0x80];
		}
	}

	return c;
}

WCHAR ff_wtoupper (	/* Upper converted character */
	WCHAR chr		/* Input character */
)
{
	static const WCHAR tbl_lower[] = {	/* Lower case characters to be converted */
		0x0061,0x0062,0x0063,0x0064,0x0065,0x0066,0x0067,0x0068,0x0069,0x006A,0x006B,0x006C,0x006D,0x006E,0x006F,
		0x0070,0x0071,0x0072,0x0073,0x0074,0x0075,0x0076,0x0077,0x0078,0x0079,0x007A,0x00E0,0x00E1,0x00E2,0x00E3,
		0x00E4,0x00E5,0x00E6,0x00E7,0x00E8,0x00E9,0x00EA,0x00EB,0x00EC,0x00ED,0x00EE,0x00EF,0x00F0,0x00F1,0x00F2,
		0x00F3,0x00F4,0x00F5,0x00F6,0x00F8,0x00F9,0x00FA,0x00FB,0x00FC,0x00FD,0x00FE,0x00FF,0x0101,0x0103,0x0105,
		0x0107,0x0109,0x010B,0x010D,0x010F,0x0111,0x0113,0x0115,0x0117,0x0119,0x011B,0x011D,0x011F,0x0121,0x0123,
		0x0125,0x0127,0x0129,0x012B,0x012D,0x012F,0x0131,0x0133,0x0135,0x0137,0x013A,0x013C,0x013E,0x0140,0x0142,
		0x0144,0x0146,0x0148,0x014B,0x014D,0x014F,0x0151,0x0153,0x0155,0x0157,0x0159,0x015B,0x015D,0x015F,0x0161,
		0x0163,0x0165,0x0167,0x0169,0x016B,0x016D,0x016F,0x0171,0x0173,0x0175,0x0177,0x017A,0x017C,0x017E,0x017F,
		0
	};
	static const WCHAR tbl_upper[] = {	/* Corresponding upper case characters */
		0x0041,0x0042,0x0043,0x0044,0x0045,0x0046,0x0047,0x0048,0x0049,0x004A,0x004B,0x004C,0x004D,0x004E,0x004F,
		0x0050,0x0051,0x0052,0x0053,0x0054,0x0055,0x0056,0x0057,0x0058,0x0059,0x005A,0x00C0,0x00C1,0x00C2,0x00C3,
		0x00C4,0x00C5,0x00C6,0x00C7,0x00C8,0x00C9,0x00CA,0x00CB,0x00CC,0x00CD,0x00CE,0x00CF,0x00D0,0x00D1,0x00D2,
		0x00D3,0x00D4,0x00D5,0x00D6,0x00D8,0x00D9,0x00DA,0x00DB,0x00DC,0x00DD,0x00DE,0x0178,0x0100,0x0102,0x0104,
		0x0106,0x0108,0x010A,0x010C,0x010E,0x0110,0x0112,0x0114,0x0116,0x0118,0x011A,0x011C,0x011E,0x0120,0x0122,
		0x0124,0x0126,0x0128,0x012A,0x012C,0x012E,0x0049,0x0132,0x0134,0x0136,0x0139,0x013B,0x013D,0x013F,0x0141,
		0x0143,0x0145,0x0147,0x014A,0x014C,0x014E,0x0150,0x0152,0x0154,0x0156,0x0158,0x015A,0x015C,0x015E,0x0160,
		0x0162,0x0164,0x0166,0x0168,0x016A,0x016C,0x016E,0x0170,0x0172,0x0174,0x0176,0x0179,0x017B,0x017D,0x0053,
		0
	};
	int i;

	for (i = 0; tbl_lower[i] && chr != tbl_lower[i]; i++) ;

	return tbl_lower[i] ? tbl_upper[i] : chr;
}

#endif /* _USE_LFN != 0 */
