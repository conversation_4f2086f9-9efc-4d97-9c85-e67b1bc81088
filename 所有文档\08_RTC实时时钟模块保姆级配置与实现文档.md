# RTC实时时钟模块保姆级配置与实现文档

## 目录
1. [硬件连接说明](#硬件连接说明)
2. [CubeMX配置步骤](#cubemx配置步骤)
3. [RTC配置详解](#rtc配置详解)
4. [底层BSP配置](#底层bsp配置)
5. [RTC工作原理](#rtc工作原理)
6. [时间格式转换](#时间格式转换)
7. [闹钟功能实现](#闹钟功能实现)
8. [应用层实现](#应用层实现)
9. [使用示例](#使用示例)
10. [常见问题与解决方案](#常见问题与解决方案)

---

## 硬件连接说明

### RTC硬件配置
RTC模块是MCU内部集成的实时时钟，无需外部连接，但需要外部时钟源：

| 时钟源 | 频率 | 精度 | 功耗 | 说明 |
|--------|------|------|------|------|
| LSE (外部低速晶振) | 32.768kHz | 高 | 低 | 推荐使用 |
| LSI (内部低速振荡器) | ~32kHz | 低 | 极低 | 备用选择 |
| HSE/128 | 高速晶振/128 | 中 | 高 | 不推荐 |

### 外部晶振连接
如果使用LSE外部晶振：
```
MCU          32.768kHz晶振
PC14 -------- XTAL_IN
PC15 -------- XTAL_OUT
             负载电容到GND
```

### 备份域电源
- **VBAT引脚**: 连接3V纽扣电池，保证断电时RTC继续工作
- **备份寄存器**: 可存储用户数据，断电不丢失

---

## CubeMX配置步骤

### 1. 基本RTC配置

#### 启用RTC
1. 进入System Core → RCC
2. 在Low Speed Clock (LSE)中选择"Crystal/Ceramic Resonator"
3. 进入Timers → RTC
4. 设置Activated为"Enable"

#### 配置RTC参数
1. 进入Configuration → Timers → RTC
2. 设置基本参数：
   ```
   Clock Source: LSE (Low Speed External)
   Asynchronous Predivider: 127
   Synchronous Predivider: 255
   Hour Format: 24 Hours
   ```

### 2. 时钟源配置

#### LSE配置
```
RCC Configuration:
  Low Speed Clock (LSE): Crystal/Ceramic Resonator
  Drive Level: Low drive (default)
```

#### 预分频器计算
```
RTC_CLK = LSE_CLK / ((Async_Prediv + 1) × (Sync_Prediv + 1))
32768Hz = 32768Hz / ((127 + 1) × (255 + 1))
32768Hz = 32768Hz / (128 × 256)
32768Hz = 32768Hz / 32768 = 1Hz
```

### 3. 中断配置

#### NVIC设置
如果需要RTC中断：
1. 进入Configuration → System Core → NVIC
2. 使能中断：
   ```
   RTC global interrupt: Enabled
   RTC Alarm interrupt through EXTI line 17: Enabled
   ```

### 4. 备份域配置

#### 电源管理
```
System Core → PWR:
  Backup Regulator: Enabled (如果使用VBAT)
```

---

## RTC配置详解

### 1. RTC时钟源选择

#### LSE配置 (推荐)
```c
#define RTC_CLOCK_SOURCE_LXTAL

/* LSE配置参数 */
#define LSE_STARTUP_TIMEOUT    5000    // LSE启动超时时间
#define LSE_DRIVE_LEVEL        0       // 驱动能力：0=低，1=中低，2=中高，3=高
```

#### LSI配置 (备用)
```c
#define RTC_CLOCK_SOURCE_IRC32K

/* LSI配置参数 */
#define LSI_STARTUP_TIMEOUT    2000    // LSI启动超时时间
```

### 2. 预分频器配置

```c
/* 预分频器值 */
#ifdef RTC_CLOCK_SOURCE_LXTAL
    #define PRESCALER_S    0xFF    // 同步预分频器 (255)
    #define PRESCALER_A    0x7F    // 异步预分频器 (127)
#elif defined RTC_CLOCK_SOURCE_IRC32K
    #define PRESCALER_S    0x13F   // 同步预分频器 (319)
    #define PRESCALER_A    0x63    // 异步预分频器 (99)
#endif

/* 计算公式 */
// LSE: 32768 / ((127+1) * (255+1)) = 1Hz
// LSI: 32000 / ((99+1) * (319+1)) = 1Hz (约)
```

---

## 底层BSP配置

### 1. 头文件定义 (mcu_cmic_gd32f470vet6.h)

```c
/* RTC相关定义 */
#define RTC_CLOCK_SOURCE_LXTAL    // 使用外部32.768kHz晶振
// #define RTC_CLOCK_SOURCE_IRC32K   // 使用内部32kHz振荡器

#define BKP_VALUE    0x32F0        // 备份寄存器标志值

/* RTC结构体声明 */
extern rtc_parameter_struct rtc_initpara;
extern uint32_t prescaler_a, prescaler_s;

/* 函数声明 */
void bsp_rtc_init(void);
int bsp_rtc_setup(void);
void bsp_rtc_pre_cfg(void);
```

### 2. BSP初始化函数 (mcu_cmic_gd32f470vet6.c)

```c
/* 全局变量定义 */
rtc_parameter_struct rtc_initpara;
uint32_t prescaler_a, prescaler_s;
uint32_t RTCSRC_FLAG = 0;

/*!
    \brief      RTC预配置
    \param[in]  none
    \param[out] none
    \retval     none
*/
void bsp_rtc_pre_cfg(void)
{
#if defined (RTC_CLOCK_SOURCE_IRC32K)
    rcu_osci_on(RCU_IRC32K);
    rcu_osci_stab_wait(RCU_IRC32K);
    rcu_rtc_clock_config(RCU_RTCSRC_IRC32K);
    
    prescaler_s = 0x13F;  // 319
    prescaler_a = 0x63;   // 99
#elif defined (RTC_CLOCK_SOURCE_LXTAL)
    rcu_osci_on(RCU_LXTAL);
    rcu_osci_stab_wait(RCU_LXTAL);
    rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);
    
    prescaler_s = 0xFF;   // 255
    prescaler_a = 0x7F;   // 127
#else
    #error RTC clock source should be defined.
#endif

    rcu_periph_clock_enable(RCU_RTC);
    rtc_register_sync_wait();
}

/*!
    \brief      RTC初始化
    \param[in]  none
    \param[out] none
    \retval     0-成功，其他-失败
*/
int bsp_rtc_init(void)
{
    /* 使能PMU时钟 */
    rcu_periph_clock_enable(RCU_PMU);
    
    /* 使能备份域写访问 */
    pmu_backup_write_enable();
    
    /* RTC预配置 */
    bsp_rtc_pre_cfg();
    
    /* 获取RTC时钟源选择 */
    RTCSRC_FLAG = GET_BITS(RCU_BDCTL, 8, 9);
    
    /* 清除复位标志 */
    rcu_all_reset_flag_clear();
    
    return 0;
}

/*!
    \brief      RTC设置默认时间
    \param[in]  none
    \param[out] none
    \retval     0-成功，其他-失败
*/
int bsp_rtc_setup(void)
{
    /* 设置默认时间：2025-04-30 23:59:50 */
    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    rtc_initpara.year = 0x25;              // 2025年
    rtc_initpara.day_of_week = RTC_SATURDAY;
    rtc_initpara.month = RTC_APR;          // 4月
    rtc_initpara.date = 0x30;              // 30日
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;
    
    /* 设置时间 */
    rtc_initpara.hour = 0x23;              // 23时
    rtc_initpara.minute = 0x59;            // 59分
    rtc_initpara.second = 0x50;            // 50秒
    
    /* 配置RTC */
    if(ERROR == rtc_init(&rtc_initpara))
    {
        return -1;
    }
    else
    {
        RTC_BKP0 = BKP_VALUE;  // 设置备份寄存器标志
        return 0;
    }
}
```

---

## RTC工作原理

### 1. RTC架构
```
时钟源 → 预分频器 → 计数器 → 时间/日期寄存器
   ↓         ↓         ↓           ↓
  LSE    异步/同步   32位计数    BCD格式存储
```

### 2. BCD格式说明
RTC使用BCD (Binary Coded Decimal) 格式存储时间：
- 每个十进制数字用4位二进制表示
- 例如：23 → 0x23 (0010 0011)
- 高4位表示十位，低4位表示个位

### 3. 时间寄存器结构
```c
/* 时间寄存器 (RTC_TIME) */
typedef struct {
    uint32_t second  : 7;   // 秒 (BCD格式，0-59)
    uint32_t reserved1 : 1;
    uint32_t minute  : 7;   // 分 (BCD格式，0-59)
    uint32_t reserved2 : 1;
    uint32_t hour    : 6;   // 时 (BCD格式，0-23或1-12)
    uint32_t am_pm   : 1;   // AM/PM标志
    uint32_t reserved3 : 9;
} rtc_time_reg_t;

/* 日期寄存器 (RTC_DATE) */
typedef struct {
    uint32_t date    : 6;   // 日 (BCD格式，1-31)
    uint32_t reserved1 : 2;
    uint32_t month   : 5;   // 月 (BCD格式，1-12)
    uint32_t reserved2 : 3;
    uint32_t year    : 8;   // 年 (BCD格式，0-99)
    uint32_t week_day : 3;  // 星期 (1-7)
    uint32_t reserved3 : 5;
} rtc_date_reg_t;
```

### 4. 写保护机制
RTC寄存器具有写保护功能：
```c
/* 解除写保护 */
RTC_WPK = 0xCA;
RTC_WPK = 0x53;

/* 写入RTC寄存器 */
// ... 配置操作 ...

/* 启用写保护 */
RTC_WPK = 0xFF;
```

---

## 时间格式转换

### 1. BCD与十进制转换

```c
/*!
    \brief      十进制转BCD
    \param[in]  decimal: 十进制数 (0-99)
    \param[out] none
    \retval     BCD格式数
*/
uint8_t decimal_to_bcd(uint8_t decimal)
{
    return ((decimal / 10) << 4) | (decimal % 10);
}

/*!
    \brief      BCD转十进制
    \param[in]  bcd: BCD格式数
    \param[out] none
    \retval     十进制数
*/
uint8_t bcd_to_decimal(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}
```

### 2. 时间设置函数

```c
/*!
    \brief      设置RTC时间
    \param[in]  year: 年 (2000-2099)
    \param[in]  month: 月 (1-12)
    \param[in]  day: 日 (1-31)
    \param[in]  hour: 时 (0-23)
    \param[in]  minute: 分 (0-59)
    \param[in]  second: 秒 (0-59)
    \param[out] none
    \retval     1-成功，0-失败
*/
uint8_t rtc_set_time(uint16_t year, uint8_t month, uint8_t day, 
                     uint8_t hour, uint8_t minute, uint8_t second)
{
    /* 参数检查 */
    if (year < 2000 || year > 2099) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > 31) return 0;
    if (hour > 23) return 0;
    if (minute > 59) return 0;
    if (second > 59) return 0;
    
    /* 转换为BCD格式 */
    uint8_t year_bcd = decimal_to_bcd(year - 2000);
    
    /* 设置RTC参数 */
    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    rtc_initpara.year = year_bcd;
    rtc_initpara.day_of_week = RTC_MONDAY;  // 默认星期一
    rtc_initpara.month = decimal_to_bcd(month);
    rtc_initpara.date = decimal_to_bcd(day);
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;
    
    /* 设置时间 */
    rtc_initpara.hour = decimal_to_bcd(hour);
    rtc_initpara.minute = decimal_to_bcd(minute);
    rtc_initpara.second = decimal_to_bcd(second);
    
    /* 配置RTC */
    if(ERROR == rtc_init(&rtc_initpara))
    {
        return 0;
    }
    else
    {
        RTC_BKP0 = BKP_VALUE;
        return 1;
    }
}
```

### 3. 时间读取函数

```c
/*!
    \brief      获取RTC时间字符串
    \param[out] time_str: 时间字符串缓冲区
    \param[in]  buffer_size: 缓冲区大小
    \param[out] none
    \retval     none
*/
void rtc_get_time_string(char* time_str, size_t buffer_size)
{
    /* 从RTC获取当前时间 */
    rtc_current_time_get(&rtc_initpara);
    
    /* 将BCD转换为十进制 */
    uint16_t year = 2000 + bcd_to_decimal(rtc_initpara.year);
    uint8_t month = bcd_to_decimal(rtc_initpara.month);
    uint8_t day = bcd_to_decimal(rtc_initpara.date);
    uint8_t hour = bcd_to_decimal(rtc_initpara.hour);
    uint8_t minute = bcd_to_decimal(rtc_initpara.minute);
    uint8_t second = bcd_to_decimal(rtc_initpara.second);
    
    /* 格式化时间字符串 */
    snprintf(time_str, buffer_size, "%04d-%02d-%02d %02d:%02d:%02d",
             year, month, day, hour, minute, second);
}

/*!
    \brief      获取时间戳
    \param[in]  none
    \param[out] none
    \retval     Unix时间戳
*/
uint32_t rtc_get_timestamp(void)
{
    rtc_current_time_get(&rtc_initpara);
    
    /* 转换为十进制 */
    uint16_t year = 2000 + bcd_to_decimal(rtc_initpara.year);
    uint8_t month = bcd_to_decimal(rtc_initpara.month);
    uint8_t day = bcd_to_decimal(rtc_initpara.date);
    uint8_t hour = bcd_to_decimal(rtc_initpara.hour);
    uint8_t minute = bcd_to_decimal(rtc_initpara.minute);
    uint8_t second = bcd_to_decimal(rtc_initpara.second);
    
    /* 简化的时间戳计算（从2000年开始） */
    uint32_t days = (year - 2000) * 365 + (month - 1) * 30 + (day - 1);
    uint32_t timestamp = days * 86400 + hour * 3600 + minute * 60 + second;
    
    return timestamp;
}
```

---

## 闹钟功能实现

### 1. 闹钟结构体

```c
/* 闹钟配置结构体 */
typedef struct {
    uint8_t alarm_hour;      // 闹钟小时 (BCD格式)
    uint8_t alarm_minute;    // 闹钟分钟 (BCD格式)
    uint8_t alarm_second;    // 闹钟秒 (BCD格式)
    uint8_t alarm_day;       // 闹钟日期 (BCD格式)
    uint32_t alarm_mask;     // 闹钟屏蔽位
    uint32_t weekday_or_date;// 星期或日期选择
    uint32_t am_pm;          // AM/PM选择
} rtc_alarm_struct;
```

### 2. 闹钟配置函数

```c
/*!
    \brief      设置RTC闹钟
    \param[in]  alarm_id: 闹钟ID (RTC_ALARM0 或 RTC_ALARM1)
    \param[in]  hour: 小时 (0-23)
    \param[in]  minute: 分钟 (0-59)
    \param[in]  second: 秒 (0-59)
    \param[out] none
    \retval     0-成功，其他-失败
*/
int rtc_set_alarm(uint8_t alarm_id, uint8_t hour, uint8_t minute, uint8_t second)
{
    rtc_alarm_struct alarm_config;
    
    /* 参数检查 */
    if (hour > 23 || minute > 59 || second > 59) return -1;
    
    /* 禁用闹钟 */
    rtc_alarm_disable(alarm_id);
    
    /* 配置闹钟参数 */
    alarm_config.alarm_hour = decimal_to_bcd(hour);
    alarm_config.alarm_minute = decimal_to_bcd(minute);
    alarm_config.alarm_second = decimal_to_bcd(second);
    alarm_config.alarm_day = 0x01;  // 默认1号
    
    /* 设置闹钟屏蔽：只比较时分秒 */
    alarm_config.alarm_mask = RTC_ALARM_DATE_MASK;
    alarm_config.weekday_or_date = RTC_ALARM_DATE_SELECTED;
    alarm_config.am_pm = RTC_AM;
    
    /* 配置闹钟 */
    rtc_alarm_config(alarm_id, &alarm_config);
    
    /* 使能闹钟中断 */
    rtc_interrupt_enable(alarm_id == RTC_ALARM0 ? RTC_INT_ALRM0 : RTC_INT_ALRM1);
    
    /* 使能闹钟 */
    rtc_alarm_enable(alarm_id);
    
    printf("闹钟设置成功：%02d:%02d:%02d\r\n", hour, minute, second);
    return 0;
}

/*!
    \brief      设置每日闹钟
    \param[in]  hour: 小时 (0-23)
    \param[in]  minute: 分钟 (0-59)
    \param[out] none
    \retval     0-成功，其他-失败
*/
int rtc_set_daily_alarm(uint8_t hour, uint8_t minute)
{
    rtc_alarm_struct alarm_config;
    
    /* 禁用闹钟0 */
    rtc_alarm_disable(RTC_ALARM0);
    
    /* 配置每日闹钟 */
    alarm_config.alarm_hour = decimal_to_bcd(hour);
    alarm_config.alarm_minute = decimal_to_bcd(minute);
    alarm_config.alarm_second = 0x00;
    alarm_config.alarm_day = 0x01;
    
    /* 屏蔽日期和秒，只比较时分 */
    alarm_config.alarm_mask = RTC_ALARM_DATE_MASK | RTC_ALARM_SECOND_MASK;
    alarm_config.weekday_or_date = RTC_ALARM_DATE_SELECTED;
    alarm_config.am_pm = RTC_AM;
    
    /* 配置并使能闹钟 */
    rtc_alarm_config(RTC_ALARM0, &alarm_config);
    rtc_interrupt_enable(RTC_INT_ALRM0);
    rtc_alarm_enable(RTC_ALARM0);
    
    printf("每日闹钟设置：%02d:%02d\r\n", hour, minute);
    return 0;
}
```

### 3. 闹钟中断处理

```c
/*!
    \brief      RTC中断处理函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void RTC_IRQHandler(void)
{
    if(rtc_flag_get(RTC_FLAG_ALRM0) != RESET)
    {
        /* 闹钟0触发 */
        printf("闹钟0触发！\r\n");
        
        /* 执行闹钟动作 */
        alarm_action();
        
        /* 清除中断标志 */
        rtc_flag_clear(RTC_FLAG_ALRM0);
    }
    
    if(rtc_flag_get(RTC_FLAG_ALRM1) != RESET)
    {
        /* 闹钟1触发 */
        printf("闹钟1触发！\r\n");
        
        /* 清除中断标志 */
        rtc_flag_clear(RTC_FLAG_ALRM1);
    }
}

/*!
    \brief      闹钟动作
    \param[in]  none
    \param[out] none
    \retval     none
*/
void alarm_action(void)
{
    /* LED闪烁 */
    for(int i = 0; i < 10; i++)
    {
        LED1_TOGGLE;
        delay_ms(200);
    }
    
    /* 蜂鸣器响铃 */
    // buzzer_beep(1000, 3);  // 1秒响3次
    
    /* 串口提示 */
    printf("⏰ 闹钟时间到！\r\n");
}
```

---

## 应用层实现

### 1. 应用层头文件 (rtc_app.h)

```c
#ifndef __RTC_APP_H__
#define __RTC_APP_H__

#include "stdint.h"
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 时间结构体 */
typedef struct {
    uint16_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
    uint8_t weekday;
} datetime_t;

/* 函数声明 */
uint8_t decimal_to_bcd(uint8_t decimal);
uint8_t bcd_to_decimal(uint8_t bcd);
uint8_t rtc_set_time(uint16_t year, uint8_t month, uint8_t day, 
                     uint8_t hour, uint8_t minute, uint8_t second);
void rtc_get_time_string(char* time_str, size_t buffer_size);
void rtc_get_datetime(datetime_t *dt);
uint32_t rtc_get_timestamp(void);
int rtc_set_alarm(uint8_t alarm_id, uint8_t hour, uint8_t minute, uint8_t second);
int rtc_set_daily_alarm(uint8_t hour, uint8_t minute);
void rtc_task(void);

#ifdef __cplusplus
}
#endif

#endif
```

### 2. 应用层实现文件 (rtc_app.c)

```c
#include "mcu_cmic_gd32f470vet6.h"
#include "rtc_app.h"
#include <stdio.h>
#include <string.h>

extern rtc_parameter_struct rtc_initpara;

/*!
    \brief      RTC任务函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_task(void)
{
    static uint32_t last_display_time = 0;
    uint32_t current_time = get_system_ms();
    
    /* 每秒更新一次时间显示 */
    if(current_time - last_display_time >= 1000)
    {
        last_display_time = current_time;
        
        /* 获取当前时间 */
        rtc_current_time_get(&rtc_initpara);
        
        /* 在OLED上显示时间 */
        oled_printf(0, 3, "%02x:%02x:%02x", 
                   rtc_initpara.hour, 
                   rtc_initpara.minute, 
                   rtc_initpara.second);
    }
}

/*!
    \brief      获取日期时间结构体
    \param[out] dt: 日期时间结构体指针
    \param[out] none
    \retval     none
*/
void rtc_get_datetime(datetime_t *dt)
{
    rtc_current_time_get(&rtc_initpara);
    
    dt->year = 2000 + bcd_to_decimal(rtc_initpara.year);
    dt->month = bcd_to_decimal(rtc_initpara.month);
    dt->day = bcd_to_decimal(rtc_initpara.date);
    dt->hour = bcd_to_decimal(rtc_initpara.hour);
    dt->minute = bcd_to_decimal(rtc_initpara.minute);
    dt->second = bcd_to_decimal(rtc_initpara.second);
    dt->weekday = rtc_initpara.day_of_week;
}

/*!
    \brief      检查是否为闰年
    \param[in]  year: 年份
    \param[out] none
    \retval     1-闰年，0-平年
*/
uint8_t is_leap_year(uint16_t year)
{
    return ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0));
}

/*!
    \brief      获取月份天数
    \param[in]  year: 年份
    \param[in]  month: 月份 (1-12)
    \param[out] none
    \retval     该月天数
*/
uint8_t get_days_in_month(uint16_t year, uint8_t month)
{
    const uint8_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    
    if (month == 2 && is_leap_year(year))
        return 29;
    else
        return days_in_month[month - 1];
}

/*!
    \brief      时间校验
    \param[in]  year: 年份
    \param[in]  month: 月份
    \param[in]  day: 日期
    \param[in]  hour: 小时
    \param[in]  minute: 分钟
    \param[in]  second: 秒
    \param[out] none
    \retval     1-有效，0-无效
*/
uint8_t validate_datetime(uint16_t year, uint8_t month, uint8_t day,
                         uint8_t hour, uint8_t minute, uint8_t second)
{
    if (year < 2000 || year > 2099) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > get_days_in_month(year, month)) return 0;
    if (hour > 23) return 0;
    if (minute > 59) return 0;
    if (second > 59) return 0;
    
    return 1;
}
```

---

## 使用示例

### 1. 基本初始化和使用
```c
int main(void)
{
    // 系统初始化
    system_init();
    
    // RTC初始化
    bsp_rtc_init();
    
    // 检查是否需要设置时间
    if(RTC_BKP0 != BKP_VALUE)
    {
        printf("首次启动，设置默认时间\r\n");
        bsp_rtc_setup();
    }
    else
    {
        printf("RTC时间有效\r\n");
    }
    
    // 显示当前时间
    char time_str[32];
    rtc_get_time_string(time_str, sizeof(time_str));
    printf("当前时间：%s\r\n", time_str);
    
    // 主循环
    while(1)
    {
        rtc_task();  // RTC任务
        delay_ms(10);
    }
}
```

### 2. 时间设置示例
```c
void time_setting_example(void)
{
    /* 设置时间：2025-01-01 12:00:00 */
    if(rtc_set_time(2025, 1, 1, 12, 0, 0))
    {
        printf("时间设置成功\r\n");
    }
    else
    {
        printf("时间设置失败\r\n");
    }
    
    /* 通过串口命令设置时间 */
    // 命令格式：RTC Config 2025 01 01 12 00 00
}

void process_rtc_command(char *cmd)
{
    if(strncmp(cmd, "RTC Config", 10) == 0)
    {
        uint16_t year;
        uint8_t month, day, hour, minute, second;
        
        if(sscanf(cmd, "RTC Config %d %d %d %d %d %d",
                  &year, &month, &day, &hour, &minute, &second) == 6)
        {
            if(validate_datetime(year, month, day, hour, minute, second))
            {
                if(rtc_set_time(year, month, day, hour, minute, second))
                {
                    printf("时间设置成功：%04d-%02d-%02d %02d:%02d:%02d\r\n",
                           year, month, day, hour, minute, second);
                }
                else
                {
                    printf("时间设置失败\r\n");
                }
            }
            else
            {
                printf("时间参数无效\r\n");
            }
        }
        else
        {
            printf("命令格式错误\r\n");
            printf("格式：RTC Config YYYY MM DD HH MM SS\r\n");
        }
    }
    else if(strcmp(cmd, "RTC now") == 0)
    {
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        printf("当前时间：%s\r\n", time_str);
    }
}
```

### 3. 闹钟设置示例
```c
void alarm_setting_example(void)
{
    /* 设置每日7:30闹钟 */
    rtc_set_daily_alarm(7, 30);
    
    /* 设置一次性闹钟：今天18:00:00 */
    rtc_set_alarm(RTC_ALARM1, 18, 0, 0);
    
    printf("闹钟设置完成\r\n");
}

void process_alarm_command(char *cmd)
{
    if(strncmp(cmd, "alarm set", 9) == 0)
    {
        uint8_t hour, minute;
        
        if(sscanf(cmd, "alarm set %d %d", &hour, &minute) == 2)
        {
            if(hour < 24 && minute < 60)
            {
                rtc_set_daily_alarm(hour, minute);
                printf("每日闹钟设置：%02d:%02d\r\n", hour, minute);
            }
            else
            {
                printf("时间参数无效\r\n");
            }
        }
    }
    else if(strcmp(cmd, "alarm off") == 0)
    {
        rtc_alarm_disable(RTC_ALARM0);
        rtc_alarm_disable(RTC_ALARM1);
        printf("闹钟已关闭\r\n");
    }
}
```

### 4. 数据记录时间戳
```c
void data_logging_with_timestamp(void)
{
    datetime_t dt;
    uint32_t voltage = get_voltage_mv();
    
    /* 获取当前时间 */
    rtc_get_datetime(&dt);
    
    /* 记录数据到SD卡 */
    char log_entry[128];
    sprintf(log_entry, "%04d-%02d-%02d %02d:%02d:%02d,%lu.%02lu\r\n",
            dt.year, dt.month, dt.day, dt.hour, dt.minute, dt.second,
            voltage/1000, (voltage%1000)/10);
    
    sd_append_file("0:/datalog.csv", log_entry);
    
    printf("数据记录：%s", log_entry);
}
```

### 5. 在调度器中使用
```c
// 在scheduler.c中添加RTC任务
static task_t scheduler_task[] =
{
     {led_task,  1,    0}      // 1ms周期执行LED任务
    ,{rtc_task,  1000, 0}      // 1s周期执行RTC任务
    ,{adc_task,  100,  0}      // 100ms周期执行ADC任务
    ,{other_task, 500, 0}      // 其他任务
};
```

---

## 常见问题与解决方案

### 1. RTC时间不准确
**可能原因**：
- 时钟源选择错误
- 预分频器配置错误
- 外部晶振问题

**解决方案**：
```c
// 检查时钟源
uint32_t rtc_source = GET_BITS(RCU_BDCTL, 8, 9);
printf("RTC时钟源：%lu (0=无，1=LSE，2=LSI，3=HSE/128)\r\n", rtc_source);

// 校准LSI频率
if(rtc_source == 2)  // LSI
{
    // 测量实际LSI频率并调整预分频器
    uint32_t actual_lsi_freq = measure_lsi_frequency();
    prescaler_s = (actual_lsi_freq / (prescaler_a + 1)) - 1;
}

// 检查外部晶振
if(rtc_source == 1)  // LSE
{
    if(!(RCU_BDCTL & RCU_BDCTL_LXTALEN))
    {
        printf("LSE未启动\r\n");
    }
}
```

### 2. 断电后时间丢失
**可能原因**：
- 没有连接VBAT
- 备份电池电量不足
- 备份域配置错误

**解决方案**：
```c
// 检查备份域电源
void check_backup_domain(void)
{
    /* 检查备份寄存器 */
    if(RTC_BKP0 != BKP_VALUE)
    {
        printf("备份域数据丢失，需要重新设置时间\r\n");
        bsp_rtc_setup();
    }
    
    /* 检查复位原因 */
    if(rcu_flag_get(RCU_FLAG_PORRST))
    {
        printf("上电复位\r\n");
    }
    if(rcu_flag_get(RCU_FLAG_EPRST))
    {
        printf("外部复位\r\n");
    }
}

// 使能备份域
void enable_backup_domain(void)
{
    rcu_periph_clock_enable(RCU_PMU);
    pmu_backup_write_enable();
    
    /* 如果有VBAT，使能备份调节器 */
    // pmu_backup_regulator_enable();
}
```

### 3. 闹钟不触发
**可能原因**：
- 中断未使能
- 闹钟配置错误
- 中断处理函数错误

**解决方案**：
```c
// 检查闹钟配置
void check_alarm_config(void)
{
    rtc_alarm_struct alarm_time;
    
    /* 读取闹钟配置 */
    rtc_alarm_get(RTC_ALARM0, &alarm_time);
    
    printf("闹钟0配置：%02x:%02x:%02x\r\n",
           alarm_time.alarm_hour,
           alarm_time.alarm_minute,
           alarm_time.alarm_second);
    
    /* 检查闹钟使能状态 */
    if(RTC_CTL & RTC_CTL_ALRM0EN)
    {
        printf("闹钟0已使能\r\n");
    }
    else
    {
        printf("闹钟0未使能\r\n");
    }
    
    /* 检查中断使能 */
    if(RTC_CTL & RTC_CTL_ALRM0IE)
    {
        printf("闹钟0中断已使能\r\n");
    }
    else
    {
        printf("闹钟0中断未使能\r\n");
    }
}
```

### 4. BCD转换错误
**可能原因**：
- 转换函数错误
- 数据范围超限
- 格式理解错误

**解决方案**：
```c
// 安全的BCD转换
uint8_t safe_decimal_to_bcd(uint8_t decimal)
{
    if(decimal > 99) return 0x99;  // 限制最大值
    return ((decimal / 10) << 4) | (decimal % 10);
}

uint8_t safe_bcd_to_decimal(uint8_t bcd)
{
    uint8_t tens = (bcd >> 4) & 0x0F;
    uint8_t ones = bcd & 0x0F;
    
    if(tens > 9 || ones > 9) return 0;  // 无效BCD
    return tens * 10 + ones;
}

// 验证BCD格式
uint8_t is_valid_bcd(uint8_t bcd)
{
    return ((bcd >> 4) <= 9) && ((bcd & 0x0F) <= 9);
}
```

### 5. 时间设置失败
**可能原因**：
- 写保护未解除
- 初始化模式进入失败
- 参数错误

**解决方案**：
```c
// 强制时间设置
int force_rtc_set_time(uint16_t year, uint8_t month, uint8_t day,
                       uint8_t hour, uint8_t minute, uint8_t second)
{
    int retry_count = 3;
    
    while(retry_count--)
    {
        /* 解除写保护 */
        RTC_WPK = RTC_UNLOCK_KEY1;
        RTC_WPK = RTC_UNLOCK_KEY2;
        
        /* 进入初始化模式 */
        if(rtc_init_mode_enter() == SUCCESS)
        {
            /* 设置时间 */
            if(rtc_set_time(year, month, day, hour, minute, second))
            {
                /* 退出初始化模式 */
                rtc_init_mode_exit();
                
                /* 启用写保护 */
                RTC_WPK = RTC_LOCK_KEY;
                
                return 0;  // 成功
            }
        }
        
        delay_ms(10);  // 重试延时
    }
    
    return -1;  // 失败
}
```

---

## 总结

本文档详细介绍了RTC实时时钟模块的完整配置和实现过程，包括：

1. **硬件层面**: 时钟源选择、外部晶振连接、备份电源
2. **配置层面**: CubeMX配置、预分频器设置、中断配置
3. **驱动层面**: BSP底层驱动、写保护机制
4. **格式层面**: BCD转换、时间格式处理
5. **功能层面**: 时间设置读取、闹钟功能、时间戳生成
6. **应用层面**: 时间显示、数据记录、命令处理
7. **实践层面**: 各种使用示例和问题解决

通过本文档，您可以：
- 完全掌握RTC模块的配置方法
- 理解RTC工作原理和BCD格式
- 实现精确的时间管理功能
- 处理各种RTC相关问题
- 构建完整的时间系统

RTC是嵌入式系统中重要的时间管理模块，正确使用RTC可以实现精确计时、定时任务、数据时间戳等重要功能。建议在实际使用时注意时钟源选择和备份电源配置。
