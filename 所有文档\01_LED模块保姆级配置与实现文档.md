# LED模块保姆级配置与实现文档

## 目录
1. [硬件连接说明](#硬件连接说明)
2. [CubeMX配置步骤](#cubemx配置步骤)
3. [底层BSP配置](#底层bsp配置)
4. [应用层实现](#应用层实现)
5. [API函数详解](#api函数详解)
6. [使用示例](#使用示例)
7. [常见问题与解决方案](#常见问题与解决方案)

---

## 硬件连接说明

### LED硬件分布
本开发板共有6个LED，分布在两个GPIO端口上：

| LED编号 | GPIO端口 | GPIO引脚 | 物理位置 | 控制逻辑 |
|---------|----------|----------|----------|----------|
| LED1    | GPIOD    | PD0      | 板载LED1 | 低电平点亮 |
| LED2    | GPIOD    | PD1      | 板载LED2 | 低电平点亮 |
| LED3    | GPIOB    | PB6      | 板载LED3 | 低电平点亮 |
| LED4    | GPIOD    | PD3      | 板载LED4 | 低电平点亮 |
| LED5    | GPIOD    | PD4      | 板载LED5 | 低电平点亮 |
| LED6    | GPIOD    | PD5      | 板载LED6 | 低电平点亮 |

### 电路特性
- **控制逻辑**: 所有LED均为低电平点亮（共阳极接法）
- **电流限制**: 每个LED通过限流电阻控制电流
- **电压等级**: 3.3V逻辑电平

---

## CubeMX配置步骤

### 1. 打开CubeMX工程
1. 启动STM32CubeMX
2. 选择芯片型号：GD32F470VET6（或对应的STM32F4系列）
3. 创建新工程

### 2. GPIO配置步骤

#### 配置GPIOD端口（LED1,2,4,5,6）
1. **选择引脚**：
   - 在Pinout视图中找到PD0、PD1、PD3、PD4、PD5
   - 右键点击每个引脚，选择"GPIO_Output"

2. **配置参数**：
   ```
   GPIO Mode: Output Push Pull
   GPIO Pull-up/Pull-down: No pull-up and no pull-down
   Maximum output speed: Low
   User Label: 
     - PD0 → LED1
     - PD1 → LED2  
     - PD3 → LED4
     - PD4 → LED5
     - PD5 → LED6
   ```

#### 配置GPIOB端口（LED3）
1. **选择引脚**：
   - 找到PB6引脚
   - 右键点击，选择"GPIO_Output"

2. **配置参数**：
   ```
   GPIO Mode: Output Push Pull
   GPIO Pull-up/Pull-down: No pull-up and no pull-down
   Maximum output speed: Low
   User Label: LED3
   ```

### 3. 时钟配置
1. 进入Clock Configuration页面
2. 确保GPIOD和GPIOB时钟已使能
3. 系统时钟配置为168MHz（根据实际需求）

### 4. 代码生成
1. 进入Project Manager
2. 设置工程名称和路径
3. 选择IDE：Keil MDK-ARM
4. 点击"GENERATE CODE"

---

## 底层BSP配置

### 1. 头文件定义 (mcu_cmic_gd32f470vet6.h)

```c
/* LED GPIO端口和时钟定义 */
#define LED_PORT        GPIOD
#define LED_PORT2       GPIOB
#define LED_CLK_PORT    RCU_GPIOD
#define LED_CLK_PORT2   RCU_GPIOB

/* LED引脚定义 */
#define LED1_PIN        GPIO_PIN_0
#define LED2_PIN        GPIO_PIN_1
#define LED3_PIN        GPIO_PIN_6
#define LED4_PIN        GPIO_PIN_3
#define LED5_PIN        GPIO_PIN_4
#define LED6_PIN        GPIO_PIN_5

/* LED控制宏定义 - 带参数控制 */
#define LED1_SET(x)     do { if(x) GPIO_BOP(LED_PORT) = LED1_PIN; else GPIO_BC(LED_PORT) = LED1_PIN; } while(0)
#define LED2_SET(x)     do { if(x) GPIO_BOP(LED_PORT) = LED2_PIN; else GPIO_BC(LED_PORT) = LED2_PIN; } while(0)
#define LED3_SET(x)     do { if(x) GPIO_BOP(LED_PORT2)= LED3_PIN; else GPIO_BC(LED_PORT2)= LED3_PIN; } while(0)
#define LED4_SET(x)     do { if(x) GPIO_BOP(LED_PORT) = LED4_PIN; else GPIO_BC(LED_PORT) = LED4_PIN; } while(0)
#define LED5_SET(x)     do { if(x) GPIO_BOP(LED_PORT) = LED5_PIN; else GPIO_BC(LED_PORT) = LED5_PIN; } while(0)
#define LED6_SET(x)     do { if(x) GPIO_BOP(LED_PORT) = LED6_PIN; else GPIO_BC(LED_PORT) = LED6_PIN; } while(0)

/* LED翻转宏定义 */
#define LED1_TOGGLE     do { GPIO_TG(LED_PORT) = LED1_PIN; } while(0)
#define LED2_TOGGLE     do { GPIO_TG(LED_PORT) = LED2_PIN; } while(0)
#define LED3_TOGGLE     do { GPIO_TG(LED_PORT2)= LED3_PIN; } while(0)
#define LED4_TOGGLE     do { GPIO_TG(LED_PORT) = LED4_PIN; } while(0)
#define LED5_TOGGLE     do { GPIO_TG(LED_PORT) = LED5_PIN; } while(0)
#define LED6_TOGGLE     do { GPIO_TG(LED_PORT) = LED6_PIN; } while(0)

/* LED直接控制宏定义 */
#define LED1_ON         do { GPIO_BC(LED_PORT) = LED1_PIN; } while(0)
#define LED2_ON         do { GPIO_BC(LED_PORT) = LED2_PIN; } while(0)
#define LED3_ON         do { GPIO_BC(LED_PORT2)= LED3_PIN; } while(0)
#define LED4_ON         do { GPIO_BC(LED_PORT) = LED4_PIN; } while(0)
#define LED5_ON         do { GPIO_BC(LED_PORT) = LED5_PIN; } while(0)
#define LED6_ON         do { GPIO_BC(LED_PORT) = LED6_PIN; } while(0)

#define LED1_OFF        do { GPIO_BOP(LED_PORT) = LED1_PIN; } while(0)
#define LED2_OFF        do { GPIO_BOP(LED_PORT) = LED2_PIN; } while(0)
#define LED3_OFF        do { GPIO_BOP(LED_PORT2)= LED3_PIN; } while(0)
#define LED4_OFF        do { GPIO_BOP(LED_PORT) = LED4_PIN; } while(0)
#define LED5_OFF        do { GPIO_BOP(LED_PORT) = LED5_PIN; } while(0)
#define LED6_OFF        do { GPIO_BOP(LED_PORT) = LED6_PIN; } while(0)

/* 函数声明 */
void bsp_led_init(void);
```

### 2. BSP初始化函数 (mcu_cmic_gd32f470vet6.c)

```c
//LED初始化函数
void bsp_led_init(void)
{
    /* 使能LED相关GPIO时钟 */
    rcu_periph_clock_enable(LED_CLK_PORT);      // 使能GPIOD时钟
    rcu_periph_clock_enable(LED_CLK_PORT2);     // 使能GPIOB时钟
    
    /* 配置LED GPIO端口模式 */ 
    gpio_mode_set(LED_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, 
                  LED1_PIN | LED2_PIN | LED4_PIN | LED5_PIN | LED6_PIN);
    gpio_mode_set(LED_PORT2, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, LED3_PIN);
    
    /* 配置GPIO输出选项 */
    gpio_output_options_set(LED_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, 
                           LED1_PIN | LED2_PIN | LED4_PIN | LED5_PIN | LED6_PIN);
    gpio_output_options_set(LED_PORT2, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, LED3_PIN);
    
    /* 初始化LED状态为关闭（高电平） */
    GPIO_BC(LED_PORT) = LED1_PIN | LED2_PIN | LED4_PIN | LED5_PIN | LED6_PIN;
    GPIO_BC(LED_PORT2) = LED3_PIN;
}
```

**关键参数说明**：
- `GPIO_MODE_OUTPUT`: 设置为输出模式
- `GPIO_PUPD_PULLUP`: 使能内部上拉电阻
- `GPIO_OTYPE_PP`: 推挽输出模式
- `GPIO_OSPEED_50MHZ`: 输出速度50MHz
- `GPIO_BC`: 设置引脚为低电平（点亮LED）
- `GPIO_BOP`: 设置引脚为高电平（熄灭LED）

---

## 应用层实现

### 1. 应用层头文件 (led_app.h)

```c
#ifndef __LED_APP_H__
#define __LED_APP_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 全局LED状态数组声明 */
extern uint8_t ucLed[6];

/* 函数声明 */
void led_disp(uint8_t *ucLed);
void led_task(void);

#ifdef __cplusplus
}
#endif

#endif
```

### 2. 应用层实现文件 (led_app.c)

```c
#include "mcu_cmic_gd32f470vet6.h"

/* LED状态数组：0=熄灭，1=点亮 */
uint8_t ucLed[6] = {0,0,0,0,0,0};

/**
 * @brief 显示LED状态
 * @param ucLed LED数据数组指针
 * @note 该函数根据LED状态数组控制LED显示，只有状态改变时才更新硬件
 */
void led_disp(uint8_t *ucLed)
{
    // 用于记录当前LED状态以便比较
    uint8_t temp = 0x00;
    // 记录上一次LED状态，用于判断是否需要更新
    static uint8_t temp_old = 0xff;

    // 将6个LED状态压缩到一个字节中
    for (int i = 0; i < 6; i++)
    {
        if (ucLed[i]) temp |= (1<<i); // 将第i位设置为1
    }

    // 仅当当前状态与上一次状态不同时才更新显示
    if (temp_old != temp)
    {
        // 调用相应的LED控制宏
        LED1_SET(temp & 0x01);
        LED2_SET(temp & 0x02);
        LED3_SET(temp & 0x04);
        LED4_SET(temp & 0x08);
        LED5_SET(temp & 0x10);
        LED6_SET(temp & 0x20);

        // 更新上一次状态
        temp_old = temp;
    }
}

/**
 * @brief LED显示任务函数
 * @note 每次调用此函数时，LED会根据ucLed数组值进行更新
 *       通常在主循环或定时器中周期性调用
 */
void led_task(void)
{
    led_disp(ucLed);
}
```

---

## API函数详解

### 1. 底层控制宏

#### LED_SET系列宏
```c
LED1_SET(x)  // x=1点亮，x=0熄灭
LED2_SET(x)
LED3_SET(x)
LED4_SET(x)
LED5_SET(x)
LED6_SET(x)
```

**使用示例**：
```c
LED1_SET(1);  // 点亮LED1
LED1_SET(0);  // 熄灭LED1
```

#### LED直接控制宏
```c
LED1_ON      // 直接点亮LED1
LED1_OFF     // 直接熄灭LED1
LED1_TOGGLE  // 翻转LED1状态
```

**使用示例**：
```c
LED1_ON;      // 点亮LED1
LED1_OFF;     // 熄灭LED1
LED1_TOGGLE;  // 翻转LED1状态
```

### 2. 应用层函数

#### led_disp函数
```c
void led_disp(uint8_t *ucLed)
```
**功能**: 根据LED状态数组更新LED显示
**参数**: ucLed - 长度为6的LED状态数组
**返回**: 无
**特点**: 
- 只有状态改变时才更新硬件
- 提高效率，减少不必要的GPIO操作

#### led_task函数
```c
void led_task(void)
```
**功能**: LED任务函数，调用led_disp更新LED
**参数**: 无
**返回**: 无
**用途**: 在调度器中周期性调用

---

## 使用示例

### 1. 基本初始化
```c
int main(void)
{
    // 系统初始化
    system_init();
    
    // LED初始化
    bsp_led_init();
    
    // 主循环
    while(1)
    {
        led_task();  // 更新LED显示
        delay_ms(10);
    }
}
```

### 2. 控制单个LED
```c
// 方法1：使用全局数组
ucLed[0] = 1;  // 点亮LED1
ucLed[1] = 0;  // 熄灭LED2

// 方法2：直接控制
LED1_ON;       // 点亮LED1
LED2_OFF;      // 熄灭LED2
```

### 3. LED流水灯效果
```c
void led_running_light(void)
{
    static uint8_t step = 0;
    
    // 清除所有LED
    for(int i = 0; i < 6; i++)
    {
        ucLed[i] = 0;
    }
    
    // 点亮当前LED
    ucLed[step] = 1;
    
    // 移动到下一个LED
    step++;
    if(step >= 6) step = 0;
}
```

### 4. LED呼吸灯效果（需要PWM支持）
```c
// 注意：此功能需要将LED配置为PWM输出
void led_breathing_effect(void)
{
    static uint16_t brightness = 0;
    static int8_t direction = 1;
    
    brightness += direction * 10;
    
    if(brightness >= 1000)
    {
        brightness = 1000;
        direction = -1;
    }
    else if(brightness <= 0)
    {
        brightness = 0;
        direction = 1;
    }
    
    // 设置PWM占空比（需要PWM配置）
    // timer_channel_output_pulse_value_config(TIMER2, TIMER_CH_0, brightness);
}
```

### 5. 在调度器中使用
```c
// 在scheduler.c中添加LED任务
static task_t scheduler_task[] =
{
     {led_task,  1,    0}      // 1ms周期执行LED任务
    ,{other_task, 100, 0}      // 其他任务
};
```

---

## 常见问题与解决方案

### 1. LED不亮
**可能原因**：
- GPIO时钟未使能
- GPIO配置错误
- 控制逻辑反了

**解决方案**：
```c
// 检查时钟使能
rcu_periph_clock_enable(LED_CLK_PORT);

// 检查GPIO配置
gpio_mode_set(LED_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, LED1_PIN);

// 检查控制逻辑（低电平点亮）
GPIO_BC(LED_PORT) = LED1_PIN;  // 点亮
GPIO_BOP(LED_PORT) = LED1_PIN; // 熄灭
```

### 2. LED闪烁异常
**可能原因**：
- 调用频率过高
- 状态更新逻辑错误

**解决方案**：
```c
// 使用状态比较，避免频繁更新
static uint8_t temp_old = 0xff;
if (temp_old != temp)
{
    // 只有状态改变时才更新
    LED1_SET(temp & 0x01);
    temp_old = temp;
}
```

### 3. 部分LED无法控制
**可能原因**：
- 引脚复用冲突
- 硬件连接问题

**解决方案**：
```c
// 检查引脚是否被其他功能占用
// 在CubeMX中确认引脚配置
// 使用万用表检查硬件连接
```

### 4. 编译错误
**可能原因**：
- 头文件包含错误
- 宏定义冲突

**解决方案**：
```c
// 确保包含正确的头文件
#include "mcu_cmic_gd32f470vet6.h"

// 检查宏定义是否冲突
#ifndef LED1_PIN
#define LED1_PIN GPIO_PIN_0
#endif
```

---

## 总结

本文档详细介绍了LED模块的完整配置和实现过程，包括：

1. **硬件层面**: LED分布、电路特性
2. **配置层面**: CubeMX配置步骤
3. **驱动层面**: BSP底层驱动实现
4. **应用层面**: 应用层封装和使用
5. **实践层面**: 各种使用示例和问题解决

通过本文档，您可以：
- 完全掌握LED模块的配置方法
- 理解LED控制的底层原理
- 快速实现各种LED效果
- 解决常见的LED控制问题

建议在实际使用时，先按照文档步骤完成基本配置，然后根据具体需求选择合适的控制方式。
