#include "cmic_gd32f470vet6.h"
#include "sd_app.h"
#include "log_app.h"

// ��־����ȫ�ֱ���
static uint8_t auto_log_enabled = 1;  // �Զ���־��¼��־ (0=����, 1=����)

// testǰ�����������ڴ洢RTC����ֱ��log0.txt���� (����Flash�洢)
#define PRE_TEST_BUFFER_SIZE 1024
#define PRE_TEST_BUFFER_FLASH_ADDR 0x00050000  // testǰ������Flash��ַ
#define PRE_TEST_BUFFER_MAGIC 0x12345678       // ��������Ч��ħ��

typedef struct {
    uint32_t magic;                           // ��������Ч��ħ��
    uint16_t buffer_pos;                      // ��ǰ������λ��
    uint8_t logging_enabled;                  // testǰ��־��¼���ñ�־
    uint8_t reserved;                         // ���뱣���ֽ�
    char buffer_data[PRE_TEST_BUFFER_SIZE];   // ����������
} pre_test_buffer_t;

static pre_test_buffer_t pre_test_buffer;
uint16_t pre_test_buffer_pos = 0;  // ȫ�ֱ�������main.cʹ��
uint8_t pre_test_logging = 1;  // ����testǰ��־��¼ (ȫ�ֱ�������log_app.cʹ��)

/**
 * @brief ��Flash����testǰ������
 */
void load_pre_test_buffer_from_flash(void)
{
    // ��Flash��ȡ������
    spi_flash_buffer_read((uint8_t*)&pre_test_buffer, PRE_TEST_BUFFER_FLASH_ADDR, sizeof(pre_test_buffer_t));

    // ���ħ��
    if(pre_test_buffer.magic == PRE_TEST_BUFFER_MAGIC)
    {
        // �ҵ���Ч���������ָ�״̬
        pre_test_buffer_pos = pre_test_buffer.buffer_pos;
        pre_test_logging = pre_test_buffer.logging_enabled;
    }
    else
    {
        // ��ʼ���»�����
        pre_test_buffer.magic = PRE_TEST_BUFFER_MAGIC;
        pre_test_buffer.buffer_pos = 0;
        pre_test_buffer.logging_enabled = 1;
        pre_test_buffer_pos = 0;
        pre_test_logging = 1;
        memset(pre_test_buffer.buffer_data, 0, PRE_TEST_BUFFER_SIZE);
    }
}

/**
 * @brief ����testǰ��������Flash
 */
void save_pre_test_buffer_to_flash(void)
{
    // ���»�����״̬
    pre_test_buffer.buffer_pos = pre_test_buffer_pos;
    pre_test_buffer.logging_enabled = pre_test_logging;

    // ������������д��Flash
    spi_flash_sector_erase(PRE_TEST_BUFFER_FLASH_ADDR);
    spi_flash_buffer_write((uint8_t*)&pre_test_buffer, PRE_TEST_BUFFER_FLASH_ADDR, sizeof(pre_test_buffer_t));
}

/**
 * @brief ���testǰ������ (����Ϊ��״̬)
 */
void clear_pre_test_buffer(void)
{
    pre_test_buffer.magic = PRE_TEST_BUFFER_MAGIC;
    pre_test_buffer.buffer_pos = 0;
    pre_test_buffer.logging_enabled = 1;
    pre_test_buffer_pos = 0;
    pre_test_logging = 1;
    memset(pre_test_buffer.buffer_data, 0, PRE_TEST_BUFFER_SIZE);

    // �������״̬��Flash
    save_pre_test_buffer_to_flash();
}

__IO uint16_t tx_count = 0;         // ���ͼ�����
__IO uint8_t rx_flag = 0;           // ���ձ�־
uint8_t uart_dma_buffer[512] = {0}; // UART DMA������

// ����״̬����
static uint8_t waiting_for_ratio_input = 0;
static uint8_t waiting_for_limit_input = 0;
static uint8_t waiting_for_rtc_input = 0;

// ȫ�ֱ���
uint32_t current_ratio = 199;   // Ĭ��1.99
uint32_t current_limit = 1011;  // Ĭ��10.11
static uint8_t hide_mode = 0;   // ����ģʽ��־

/**
 * @brief USART�Զ���printf����
 *
 * @param usart_periph Ҫʹ�õ�USART����
 * @param format ��ʽ�ַ��� (��printf��ͬ)
 * @param ... �ɱ����
 * @return int ���͵��ַ���
 */
int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;

    // ��ʼ���ɱ�����б�
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    // ����ÿ���ַ�
    for(tx_count = 0; tx_count < len; tx_count++){
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }

    // ��¼����־�ļ� (�Ƴ�\r\n�Ի�ø�������־)
    if(len > 0)
    {
        char log_msg[512];
        strcpy(log_msg, buffer);
        // �Ƴ�ĩβ��\r\n������־��¼
        char* newline = strstr(log_msg, "\r\n");
        if(newline) *newline = '\0';
        if(strlen(log_msg) > 0)
        {
            if(pre_test_logging)
            {
                // testǰ�׶δ洢��testǰ������
                char formatted_msg[256];
                snprintf(formatted_msg, sizeof(formatted_msg), "OUT: %s", log_msg);
                add_to_pre_test_buffer(formatted_msg);
            }
            else if(auto_log_enabled)
            {
                // test��������־��¼
                log_write_formatted("OUT: %s", log_msg);
            }
        }
    }

    return len;
}

/**
 * @brief ����Flash����
 *
 * @return uint8_t Flash����ͨ������1��ʧ�ܷ���0
 */
uint8_t test_flash(void)
{
    uint32_t flash_id = spi_flash_read_id();

    if(flash_id != 0x000000 && flash_id != 0xFFFFFF)
    {
        my_printf(DEBUG_USART, "flash......OK\r\n");
        my_printf(DEBUG_USART, "flashID: 0x%06lX\r\n", flash_id);
        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "flash......error\r\n");
        my_printf(DEBUG_USART, "flashID: 0x%06lX\r\n", flash_id);
        return 0;
    }
}

/**
 * @brief ����TF������
 *
 * @return uint8_t TF������ͨ������1��ʧ�ܷ���0
 */
uint8_t test_tf_card(void)
{
    // ʹ��ȫ���ļ�ϵͳ�������Ǳ��ز���
    if(!init_filesystem())
    {
        my_printf(DEBUG_USART, "TF card......error\r\n");
        return 0;
    }

    // TF���ڲ������ڼ��ɹ������

    // �򵥲��ԣ����Դ򿪸�Ŀ¼
    DIR test_dir;
    FRESULT res = f_opendir(&test_dir, "0:/");

    if(res == FR_OK)
    {
        f_closedir(&test_dir);

        // ���Ի�ȡ�ļ�ϵͳ��Ϣ
        DWORD fre_clust, fre_sect, tot_sect;
        FATFS* fs_ptr = NULL;

        res = f_getfree("0:", &fre_clust, &fs_ptr);

        if(res == FR_OK && fs_ptr != NULL)
        {
            tot_sect = (fs_ptr->n_fatent - 2) * fs_ptr->csize;
            fre_sect = fre_clust * fs_ptr->csize;

            my_printf(DEBUG_USART, "TF card......OK\r\n");
            my_printf(DEBUG_USART, "TF card memory: %lu KB total, %lu KB free\r\n",
                     tot_sect / 2, fre_sect / 2);
        }
        else
        {
            // ��ʹf_getfreeʧ�ܣ��ܴ�Ŀ¼˵��������
            my_printf(DEBUG_USART, "TF card......OK\r\n");
            my_printf(DEBUG_USART, "TF card detected (size info unavailable)\r\n");
        }

        return 1;
    }

    my_printf(DEBUG_USART, "TF card......error\r\n");
    return 0;
}

// �洢��Flash�е�����ֵ
typedef struct {
    uint32_t ratio;
    uint32_t limit;
    uint32_t sampling_cycle;  // �������ڣ���λ�� (5, 10, �� 15)
    uint32_t magic;           // ������֤������Ч�Ե�ħ��
} config_data_t;

// �豸ID�ṹ��
typedef struct {
    char device_id[32];  // �豸ID�ַ���
    uint32_t magic;      // ������֤������Ч�Ե�ħ��
} device_id_t;

#define CONFIG_MAGIC 0x12345678
#define DEVICE_ID_MAGIC 0x87654321
#define CONFIG_FLASH_ADDR 0x00010000     // �洢���õ�Flash��ַ
#define DEVICE_ID_FLASH_ADDR 0x00020000  // �洢�豸ID��Flash��ַ

// ��ǰ��������ֵȫ�ֱ���
uint32_t current_ratio = 199;  // Ĭ��ֵ 1.99 * 100
uint32_t current_limit = 1011; // Ĭ��ֵ 10.11 * 100
static uint8_t waiting_for_ratio_input = 0;
static uint8_t waiting_for_limit_input = 0;
static uint8_t waiting_for_rtc_input = 0;

// ��������ȫ�ֱ���
static uint8_t sampling_enabled = 0;  // �������ñ�־
static uint32_t sampling_counter = 0; // ����������
static uint32_t led_counter = 0;      // 1��LED��˸������
static uint32_t sampling_period = 50; // �������ڣ�100msΪ��λ (Ĭ��5s = 50*100ms)
static uint8_t hide_mode = 0;         // ����ģʽ��־ (0=����, 1=ʮ���������)

/**
 * @brief ��testǰ���������Ӵ�ʱ�������Ϣ
 *
 * @param message Ҫ���ӵ���Ϣ
 */
void add_to_pre_test_buffer(const char* message)
{
    if(!pre_test_logging) return;

    char timestamped_msg[256];
    char current_time[32];

    rtc_get_time_string(current_time, sizeof(current_time));
    snprintf(timestamped_msg, sizeof(timestamped_msg), "%s %s\r\n", current_time, message);

    uint16_t msg_len = strlen(timestamped_msg);

    // ��黺�����Ƿ����㹻�ռ�
    if(pre_test_buffer_pos + msg_len < PRE_TEST_BUFFER_SIZE)
    {
        strcpy(&pre_test_buffer.buffer_data[pre_test_buffer_pos], timestamped_msg);
        pre_test_buffer_pos += msg_len;

        // ���ڱ��浽Flash�����ϵ�ʱ���ݶ�ʧ
        save_pre_test_buffer_to_flash();
    }
}

/**
 * @brief ��testǰ������ˢ�µ���־�ļ�
 */
void flush_pre_test_buffer_to_log(void)
{
    if(pre_test_buffer_pos > 0)
    {
        // ʹ��log_writeд�뻺�������� (�ڲ������ļ�����)
        char* line_start = pre_test_buffer.buffer_data;
        char* line_end;

        // ����д���Ա�֤��ȷ�ĸ�ʽ
        while((line_end = strstr(line_start, "\r\n")) != NULL)
        {
            *line_end = '\0';  // ��ʱ��ֹ��

            // д��ԭʼʱ���������
            if(strlen(line_start) > 0)
            {
                log_write_raw(line_start);  // д��ԭʼʱ���������
            }

            *line_end = '\r';  // �ָ��ַ�
            line_start = line_end + 2;  // �ƶ�����һ��
        }

        // ˢ�º���ջ�����
        clear_pre_test_buffer();
    }
}

/**
 * @brief ����ʱ���ַ���������RTC
 *
 * @param time_str ��ʽΪ"YYYY MM DD HH:MM:SS"��ʱ���ַ���
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t parse_and_set_rtc_time(char* time_str)
{
    uint16_t year;
    uint8_t month, day, hour, minute, second;

    // ����ǰ���ո�
    while(*time_str == ' ') time_str++;

    // ����ʱ���ַ�����"2025 01 01 12:00:30"
    int parsed = sscanf(time_str, "%hu %hhu %hhu %hhu:%hhu:%hhu",
                       &year, &month, &day, &hour, &minute, &second);

    if(parsed != 6)
    {
        return 0; // ����ʧ��
    }

    // ʹ��rtc_app.c��������RTC
    return rtc_set_time(year, month, day, hour, minute, second);
}

/**
 * @brief ������������ֵ
 *
 * @param input_str ��������ֵ�������ַ���
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t process_ratio_input(char* input_str)
{
    // ��������ֵ
    float ratio_value = atof(input_str);

    // ��֤��Χ��0~100��
    if(ratio_value < 0.0f || ratio_value > 100.0f)
    {
        my_printf(DEBUG_USART, "ratio invalid\r\n");
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);
        return 0;
    }

    // ���µ�ǰ����ֵ
    current_ratio = (uint32_t)(ratio_value * 100.0f + 0.5f);

    my_printf(DEBUG_USART, "ratio modified success\r\n");
    my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);

    return 1;
}

/**
 * @brief ������ֵ����ֵ
 *
 * @param input_str ������ֵ�������ַ���
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t process_limit_input(char* input_str)
{
    // ��������ֵ
    float limit_value = atof(input_str);

    // ��֤��Χ��0~200��
    if(limit_value < 0.0f || limit_value > 200.0f)
    {
        my_printf(DEBUG_USART, "limit invalid\r\n");
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);
        return 0;
    }

    // ���µ�ǰ��ֵ
    current_limit = (uint32_t)(limit_value * 100.0f + 0.5f);

    my_printf(DEBUG_USART, "limit modified success\r\n");
    my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);

    return 1;
}

/**
 * @brief ���浱ǰ���õ�Flash
 *
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t save_config_to_flash(void)
{
    // ����ǰ��������ֵд��Flash
    if(write_config_to_flash(current_ratio, current_limit))
    {
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);
        my_printf(DEBUG_USART, "save parameters to flash\r\n");
        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "config save error\r\n");
        return 0;
    }
}

/**
 * @brief ϵͳ����ʱ��Flash�������ã���Ĭ��
 *
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t load_config_from_flash_on_startup(void)
{
    config_data_t config;

    // ��Flash��ȡ��������
    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    // ��֤ħ��
    if(config.magic == CONFIG_MAGIC)
    {
        // ��Flashֵ����ȫ�ֱ���
        current_ratio = config.ratio;
        current_limit = config.limit;

        // �����Ч����²�������
        if(config.sampling_cycle == 5 || config.sampling_cycle == 10 || config.sampling_cycle == 15)
        {
            switch(config.sampling_cycle)
            {
                case 5:  sampling_period = 50;  break;
                case 10: sampling_period = 100; break;
                case 15: sampling_period = 150; break;
            }
        }

        return 1;
    }
    else
    {
        // δ�ҵ���Ч���ã�����Ĭ��ֵ
        return 0;
    }
}

/**
 * @brief ��Flash��ȡ���ò���ʾ
 *
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t read_config_from_flash(void)
{
    config_data_t config;

    // ��Flash��ȡ��������
    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    // ��֤ħ��
    if(config.magic == CONFIG_MAGIC)
    {
        my_printf(DEBUG_USART, "read parameters from flash\r\n");
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", config.ratio/100, config.ratio%100);
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", config.limit/100, config.limit%100);
        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "read parameters from flash\r\n");
        my_printf(DEBUG_USART, "No valid config found in flash\r\n");
        return 0;
    }
}

/**
 * @brief ��ʼ�����Բ���
 */
void start_sampling(void)
{
    // ��ʼ����ʱ��ʼ��ADC
    bsp_adc_init();

    // ��ʼǰ��Flash��ȡ��������
    uint8_t cycle_from_flash = read_sampling_cycle_from_flash();

    // ����Flash���ø��²�������
    switch(cycle_from_flash)
    {
        case 5:
            sampling_period = 50;  // 5s = 50 * 100ms
            break;
        case 10:
            sampling_period = 100; // 10s = 100 * 100ms
            break;
        case 15:
            sampling_period = 150; // 15s = 150 * 100ms
            break;
        default:
            sampling_period = 50;  // Ĭ��Ϊ5s
            break;
    }

    sampling_enabled = 1;
    sampling_counter = 0;
    led_counter = 0;

    // ��ʼ��LED״̬
    extern uint8_t ucLed[6];
    ucLed[1] = 0;  // LED2��ʼ�رգ�������ֵ�����ƣ�

    my_printf(DEBUG_USART, "Periodic Sampling\r\n");
    my_printf(DEBUG_USART, "sample cycle:%ds\r\n", cycle_from_flash);
    log_write_formatted("Sampling started with %ds cycle", cycle_from_flash);
}

/**
 * @brief ֹͣ�����Բ���
 */
void stop_sampling(void)
{
    sampling_enabled = 0;
    sampling_counter = 0;
    led_counter = 0;

    // �ر����д򿪵��ļ�
    close_all_files();

    // �ر�LED1��LED2
    extern uint8_t ucLed[6];
    ucLed[0] = 0;  // LED1�رգ�����ָʾ��
    ucLed[1] = 0;  // LED2�رգ�����ָʾ��

    my_printf(DEBUG_USART, "Periodic Sampling STOP\r\n");
    log_write("Sampling stopped");
}

/**
 * @brief ��ȡ����״̬
 *
 * @return uint8_t �������÷���1�����÷���0
 */
uint8_t get_sampling_status(void)
{
    return sampling_enabled;
}

/**
 * @brief ��ȡ��ǰ�������ڣ�100ms��λ��
 *
 * @return uint32_t ��ǰ�������ڣ�50=5s, 100=10s, 150=15s��
 */
uint32_t get_sampling_period(void)
{
    return sampling_period;
}

/**
 * @brief �л�����״̬�����ڰ������ƣ�
 */
void toggle_sampling(void)
{
    if(sampling_enabled)
    {
        stop_sampling();
    }
    else
    {
        start_sampling();
    }
}

/**
 * @brief ���ò������ڲ����浽Flash
 *
 * @param period_seconds �������ڣ��룩��5��10��15��
 */
void set_sampling_period(uint8_t period_seconds)
{
    // ��֤����
    if(period_seconds != 5 && period_seconds != 10 && period_seconds != 15)
    {
        period_seconds = 5;  // �����Ч��Ĭ��Ϊ5s
    }

    // ���浽Flash���ڵ�Դѭ���б���
    write_sampling_cycle_to_flash(period_seconds);

    // ���µ�ǰ�������ڣ���������Ч��
    switch(period_seconds)
    {
        case 5:
            sampling_period = 50;  // 5s = 50 * 100ms
            break;
        case 10:
            sampling_period = 100; // 10s = 100 * 100ms
            break;
        case 15:
            sampling_period = 150; // 15s = 150 * 100ms
            break;
    }

    // ���ò���������
    sampling_counter = 0;

    // ��ӡ������Ϣ
    my_printf(DEBUG_USART, "sample cycle adjust:%ds\r\n", period_seconds);
}

/**
 * @brief ���豸IDд��Flash
 *
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t write_device_id_to_flash(void)
{
    device_id_t device_data;

    // �����豸ID
    strcpy(device_data.device_id, "2025-CMIC-2025916750");
    device_data.magic = DEVICE_ID_MAGIC;

    // �Ȳ�������
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);

    // ���豸IDд��Flash
    spi_flash_buffer_write((uint8_t*)&device_data, DEVICE_ID_FLASH_ADDR, sizeof(device_id_t));

    return 1;
}

/**
 * @brief ��Flash��ȡ�豸ID��ͨ��UART��ʾ
 *
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t read_and_display_device_id(void)
{
    device_id_t device_data;

    // ��Flash��ȡ�豸ID
    spi_flash_buffer_read((uint8_t*)&device_data, DEVICE_ID_FLASH_ADDR, sizeof(device_id_t));

    // ��֤ħ��
    if(device_data.magic == DEVICE_ID_MAGIC)
    {
        my_printf(DEBUG_USART, "Device_ID:%s\r\n", device_data.device_id);
        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "Device_ID:Not Found\r\n");
        return 0;
    }
}

/**
 * @brief ����������д��Flash
 *
 * @param ratio ����ֵ
 * @param limit ��ֵ
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t write_config_to_flash(uint32_t ratio, uint32_t limit)
{
    config_data_t config;

    // ��ȡ���������Ա�����������
    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    // ���±�������ֵ
    config.ratio = ratio;
    config.limit = limit;

    // �����������Ч���ã�����Ĭ�ϲ�������
    if(config.magic != CONFIG_MAGIC)
    {
        config.sampling_cycle = 5;  // Ĭ��5��
    }

    config.magic = CONFIG_MAGIC;

    // �Ȳ�������
    spi_flash_sector_erase(CONFIG_FLASH_ADDR);

    // ����������д��Flash
    spi_flash_buffer_write((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    return 1;
}

/**
 * @brief ����������д��Flash
 *
 * @param cycle_seconds �������ڣ��룩
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t write_sampling_cycle_to_flash(uint8_t cycle_seconds)
{
    config_data_t config;

    // ��ȡ��������
    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    // �����������Ч���ã�����Ĭ��ֵ
    if(config.magic != CONFIG_MAGIC)
    {
        config.ratio = 199;   // Ĭ��1.99
        config.limit = 1011;  // Ĭ��10.11
    }

    // ���²�������
    config.sampling_cycle = cycle_seconds;
    config.magic = CONFIG_MAGIC;

    // �Ȳ�������
    spi_flash_sector_erase(CONFIG_FLASH_ADDR);

    // ����������д��Flash
    spi_flash_buffer_write((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    return 1;
}

/**
 * @brief ��Flash��ȡ��������
 *
 * @return uint8_t �������ڣ��룩��5��10��15����δ�ҵ�ʱĬ��Ϊ5
 */
uint8_t read_sampling_cycle_from_flash(void)
{
    config_data_t config;

    // ��Flash��ȡ��������
    spi_flash_buffer_read((uint8_t*)&config, CONFIG_FLASH_ADDR, sizeof(config_data_t));

    // ��֤ħ������֤����ֵ
    if(config.magic == CONFIG_MAGIC &&
       (config.sampling_cycle == 5 || config.sampling_cycle == 10 || config.sampling_cycle == 15))
    {
        return config.sampling_cycle;
    }
    else
    {
        return 5;  // Ĭ��Ϊ5��
    }
}

/**
 * @brief ��RTCʱ��ת��ΪUnixʱ���
 *
 * @return uint32_t Unixʱ���
 */
uint32_t rtc_to_unix_timestamp(void)
{
    extern rtc_parameter_struct rtc_initpara;
    rtc_current_time_get(&rtc_initpara);

    // ��BCDת��Ϊʮ����
    uint8_t year = ((rtc_initpara.year >> 4) * 10) + (rtc_initpara.year & 0x0F);
    uint8_t month = ((rtc_initpara.month >> 4) * 10) + (rtc_initpara.month & 0x0F);
    uint8_t date = ((rtc_initpara.date >> 4) * 10) + (rtc_initpara.date & 0x0F);
    uint8_t hour = ((rtc_initpara.hour >> 4) * 10) + (rtc_initpara.hour & 0x0F);
    uint8_t minute = ((rtc_initpara.minute >> 4) * 10) + (rtc_initpara.minute & 0x0F);
    uint8_t second = ((rtc_initpara.second >> 4) * 10) + (rtc_initpara.second & 0x0F);

    // ת��Ϊ������ݣ�����20xx��
    uint16_t full_year = 2000 + year;

    // ÿ�������������꣩
    uint16_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // ������Unix��Ԫ��1970-01-01������������
    uint32_t days = 0;

    // ����������ݵ�����
    for(uint16_t y = 1970; y < full_year; y++)
    {
        if((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0))
            days += 366;  // ����
        else
            days += 365;  // ƽ��
    }

    // ���ӵ�ǰ��������·ݵ�����
    for(uint8_t m = 1; m < month; m++)
    {
        days += days_in_month[m-1];
        // ����������Ӷ���һ��
        if(m == 2 && ((full_year % 4 == 0 && full_year % 100 != 0) || (full_year % 400 == 0)))
            days += 1;
    }

    // ���ӵ�ǰ�·ݵ���������1��Ϊ�ӵ�0�쿪ʼ���㣩
    days += (date - 1);

    // ת��Ϊ�벢����ʱ��
    uint32_t timestamp = days * 86400UL + hour * 3600UL + minute * 60UL + second;

    // ����ģʽʱ��У����ȥ8Сʱ��8 * 3600 = 28800�룩
    if(timestamp >= 28800UL)
    {
        timestamp -= 28800UL;
    }

    return timestamp;
}

/**
 * @brief ���ļ���ȡһ�У�ʹ��f_read��
 *
 * @param file �ļ�ָ��
 * @param buffer �洢�еĻ�����
 * @param buffer_size ��������С
 * @return uint8_t �ɹ���ȡ�з���1��EOF����󷵻�0
 */
uint8_t read_line_from_file(FIL* file, char* buffer, uint16_t buffer_size)
{
    UINT bytes_read;
    char ch;
    uint16_t index = 0;
    uint8_t found_line_ending = 0;

    while(index < (buffer_size - 1))
    {
        // ��ȡһ���ַ�
        FRESULT result = f_read(file, &ch, 1, &bytes_read);
        if(result != FR_OK)
        {
            break;
        }
        if(bytes_read == 0)
        {
            break; // EOF
        }

        // ����н�����
        if(ch == '\n' || ch == '\r')
        {
            found_line_ending = 1;
            // ����������н����ַ�
            if(ch == '\r')
            {
                // �����һ���ַ��Ƿ�Ϊ\n
                DWORD current_pos = f_tell(file);
                UINT temp_bytes_read;
                if(f_read(file, &ch, 1, &temp_bytes_read) == FR_OK && temp_bytes_read == 1)
                {
                    if(ch != '\n')
                    {
                        // ����\n������
                        f_lseek(file, current_pos);
                    }
                }
            }
            break;
        }

        buffer[index++] = ch;
    }

    buffer[index] = '\0';

    // ���ؽ��
    if(found_line_ending || index > 0)
    {
        return 1;  // �ɹ���ȡһ�У����ֻ���н���������Ϊ�գ�
    }
    else
    {
        return 0;  // EOF�����
    }
}

/**
 * @brief ��TF����ȡ����
 *
 * @return uint8_t �ɹ�����1��ʧ�ܷ���0
 */
uint8_t read_config_from_tf_card(void)
{
    FIL config_file;
    char line_buffer[128];
    uint8_t config_found = 0;

    // ���Դ������ļ�
    if(f_open(&config_file, "0:/config.ini", FA_READ) != FR_OK)
    {
        return 0;  // �ļ������ڻ��޷���
    }

    // ���ж�ȡ�����ļ�
    while(read_line_from_file(&config_file, line_buffer, sizeof(line_buffer)))
    {
        // �������к�ע��
        if(strlen(line_buffer) == 0 || line_buffer[0] == '#' || line_buffer[0] == ';')
            continue;

        // ���ұ�������
        if(strstr(line_buffer, "Ch0=") != NULL && strstr(line_buffer, "[Ratio]") == NULL)
        {
            char* value_start = strstr(line_buffer, "=");
            if(value_start != NULL)
            {
                value_start++; // ����'='
                float ratio_value = atof(value_start);
                if(ratio_value >= 0.0f && ratio_value <= 100.0f)
                {
                    current_ratio = (uint32_t)(ratio_value * 100.0f + 0.5f);
                    config_found = 1;
                }
            }
        }
        // ������ֵ����
        else if(strstr(line_buffer, "Ch0=") != NULL && strstr(line_buffer, "[Limit]") == NULL)
        {
            char* value_start = strstr(line_buffer, "=");
            if(value_start != NULL)
            {
                value_start++; // ����'='
                float limit_value = atof(value_start);
                if(limit_value >= 0.0f && limit_value <= 200.0f)
                {
                    current_limit = (uint32_t)(limit_value * 100.0f + 0.5f);
                    config_found = 1;
                }
            }
        }
    }

    f_close(&config_file);

    if(config_found)
    {
        my_printf(DEBUG_USART, "Configuration loaded from TF card\r\n");
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);
        return 1;
    }

    return 0;
}

/**
 * @brief ��������
 *
 * @param cmd_str �����ַ���
 */
void process_command(char* cmd_str)
{
    // �Ƴ�ĩβ�Ļ��з�
    char* newline = strstr(cmd_str, "\r\n");
    if(newline) *newline = '\0';
    newline = strstr(cmd_str, "\n");
    if(newline) *newline = '\0';
    newline = strstr(cmd_str, "\r");
    if(newline) *newline = '\0';

    // ��¼���յ�����
    if(strlen(cmd_str) > 0)
    {
        if(pre_test_logging)
        {
            // testǰ�洢��testǰ������
            char formatted_msg[256];
            snprintf(formatted_msg, sizeof(formatted_msg), "IN: %s", cmd_str);
            add_to_pre_test_buffer(formatted_msg);
        }
        else
        {
            // test��������־��¼
            log_write_formatted("IN: %s", cmd_str);
        }
    }

    // ����Ƿ��ڵȴ�ratio����
    if(waiting_for_ratio_input)
    {
        waiting_for_ratio_input = 0;  // ��λ��־
        process_ratio_input(cmd_str);
        return;
    }

    // ����Ƿ��ڵȴ�limit����
    if(waiting_for_limit_input)
    {
        waiting_for_limit_input = 0;  // ��λ��־
        process_limit_input(cmd_str);
        return;
    }

    // ����Ƿ��ڵȴ�RTC����
    if(waiting_for_rtc_input)
    {
        waiting_for_rtc_input = 0;  // ��λ��־
        if(parse_and_set_rtc_time(cmd_str))
        {
            my_printf(DEBUG_USART, "RTC Config success\r\n");
            char current_time[32];
            rtc_get_time_string(current_time, sizeof(current_time));
            my_printf(DEBUG_USART, "Time:%s\r\n", current_time);
            log_write_formatted("RTC Config Success: %s", current_time);
        }
        else
        {
            my_printf(DEBUG_USART, "RTC Config error\r\n");
            my_printf(DEBUG_USART, "Format: YYYY MM DD HH:MM:SS\r\n");
            my_printf(DEBUG_USART, "Example: 2025 06 15 10:10:10\r\n");
            log_write_formatted("RTC Config Failed: %s", cmd_str);
        }
        return;
    }

    // ���test����
    if(strcmp(cmd_str, "test") == 0)
    {
        // ��ʼϵͳ�Լ�
        my_printf(DEBUG_USART, "=====system selftest=====\r\n");
        log_write("Command: test");

        // ����Flash
        test_flash();

        // ����TF��
        if(test_tf_card())
        {
            // �ɹ�����ʱ���ǽ�������������Ϊ0�����۳�ʼ��״̬��Σ�
            set_boot_count_and_save(0);

            // ����Ϊ�ѳ�ʼ��
            if(!is_log_initialized())
            {
                set_log_initialized();

                // �״γ�ʼ����Ϊ������Ϣ����log0.txt��Ȼ��Ϊ��������log1.txt

                // �ڴ���log0.txt�ڼ�����Զ���־��¼�Է�ֹ���������Ⱦ
                auto_log_enabled = 0;

                log_close();
                log_start_session();  // �ⴴ��log0.txt��boot_count=0��

                // ���ȣ�������ǰ��������RTC���ˢ�µ�log0.txt
                flush_pre_test_buffer_to_log();

                // Ȼ��д����Գ�ʼ����Ϣ
                log_write("Test successful - System first initialization");
                log_write("Flash detection completed");
                log_write("TF card detection completed");
                log_write("System ready for operation");
                log_write("=== log0.txt completed ===");

                // ǿ�ƹر�log0.txt��ͬ��
                log_close();

                // ����������������ʼ����������־��¼
                increment_boot_count();
                log_start_session();  // �ⴴ��log1.txt��boot_count=1��

                // ���������Զ���־��¼������������
                auto_log_enabled = 1;

                my_printf(DEBUG_USART, "System initialized. Normal operation logging started.\r\n");
            }
            else
            {
                // ϵͳ�ѳ�ʼ������������
                my_printf(DEBUG_USART, "System test completed successfully\r\n");
            }
        }
        else
        {
            my_printf(DEBUG_USART, "System test failed\r\n");
        }

        my_printf(DEBUG_USART, "=====system selftest end=====\r\n");
    }
    // Check for ratio command
    else if(strcmp(cmd_str, "ratio") == 0)
    {
        my_printf(DEBUG_USART, "Ratio=%lu.%02lu\r\n", current_ratio/100, current_ratio%100);
        my_printf(DEBUG_USART, "Input value(0~100):\r\n");
        waiting_for_ratio_input = 1;  // Set flag to wait for input
    }
    // Check for limit command
    else if(strcmp(cmd_str, "limit") == 0)
    {
        my_printf(DEBUG_USART, "Limit=%lu.%02lu\r\n", current_limit/100, current_limit%100);
        my_printf(DEBUG_USART, "Input value(0~200):\r\n");
        waiting_for_limit_input = 1;  // Set flag to wait for input
    }
    // Check for conf command
    else if(strcmp(cmd_str, "conf") == 0)
    {
        if(read_config_from_tf_card())
        {
            // Success message already printed in read_config_from_tf_card()
        }
        else
        {
            my_printf(DEBUG_USART, "conf.ini file not found, using default values\r\n");
            my_printf(DEBUG_USART, "Default Ratio: 1.00\r\n");
            my_printf(DEBUG_USART, "Default Limit: 2.50\r\n");
            my_printf(DEBUG_USART, "Please create config.ini file on TF card with:\r\n");
            my_printf(DEBUG_USART, "[Ratio]\r\nCh0=1.00\r\n[Limit]\r\nCh0=2.50\r\n");
        }
    }
    // Check for config save command
    else if(strcmp(cmd_str, "config save") == 0)
    {
        save_config_to_flash();
    }
    // Check for config read command
    else if(strcmp(cmd_str, "config read") == 0)
    {
        read_config_from_flash();
    }
    // Check for start command
    else if(strcmp(cmd_str, "start") == 0)
    {
        start_sampling();
        log_write("Command: start - Sampling started");
    }
    // Check for stop command
    else if(strcmp(cmd_str, "stop") == 0)
    {
        stop_sampling();
        log_write("Command: stop - Sampling stopped");
    }
    // Check for hide command
    else if(strcmp(cmd_str, "hide") == 0)
    {
        hide_mode = 1;  // Enable hide mode
        my_printf(DEBUG_USART, "Hide mode ON - HEX output enabled\r\n");
        log_write("Command: hide - Hide mode enabled");
    }
    // Check for unhide command
    else if(strcmp(cmd_str, "unhide") == 0)
    {
        hide_mode = 0;  // Disable hide mode
        my_printf(DEBUG_USART, "Hide mode OFF - Normal output enabled\r\n");
        log_write("Command: unhide - Hide mode disabled");
    }
    // Check for RTC now command
    else if(strcmp(cmd_str, "RTC now") == 0)
    {
        char current_time[32];
        rtc_get_time_string(current_time, sizeof(current_time));
        my_printf(DEBUG_USART, "Current Time %s\r\n", current_time);
    }
    // Check for RTC Config command (two-step process)
    else if(strcmp(cmd_str, "RTC Config") == 0)
    {
        waiting_for_rtc_input = 1;
        my_printf(DEBUG_USART, "Input Datatime\r\n");
    }
    // Check for log close command (format: log[N] close)
    else if(strncmp(cmd_str, "log", 3) == 0 && strstr(cmd_str, "close") != NULL)
    {
        int log_number;
        if(sscanf(cmd_str, "log%d close", &log_number) == 1)
        {
            if(log_number >= 0 && log_number <= 3)  // Limit log number range
            {
                close_log_file_by_number((uint32_t)log_number);
            }
            else
            {
                my_printf(DEBUG_USART, "Invalid log number. Use log0-log3\r\n");
            }
        }
        else
        {
            my_printf(DEBUG_USART, "Invalid format. Use: log[N] close (e.g., log0 close)\r\n");
        }
    }
    else if(strlen(cmd_str) > 0)
    {
        my_printf(DEBUG_USART, "Unknown command: %s\r\n", cmd_str);
        my_printf(DEBUG_USART, "Available commands: test, conf, ratio, limit, config save, config read, start, stop, hide, unhide, RTC Config, RTC now, format, log[N] close\r\n");
    }
}

/**
 * @brief UART������
 *
 * ��rx_flag����ʱ�������յ���UART����
 */
void uart_task(void)
{
    if(!rx_flag) return;

    // ��������rx_flag
    rx_flag = 0;

    // �������յ�������
    process_command((char*)uart_dma_buffer);

    // ��ջ�����
    memset(uart_dma_buffer, 0, 256);
}

/**
 * @brief �������� - �ɵ�����ÿ100ms����
 */
void sampling_task(void)
{
    if(!sampling_enabled) return;

    extern uint8_t ucLed[6];
    extern uint16_t adc_value[1];

    // ����ADCת��������ʱ��ȡֵ
    adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);

    // ���ӳ�ʱ�Է�ֹ����ѭ��
    uint32_t timeout = 10000;
    while(!adc_flag_get(ADC0, ADC_FLAG_EOC) && timeout > 0) {
        timeout--;
    }

    if(timeout > 0) {
        adc_value[0] = adc_routine_data_read(ADC0);
        adc_flag_clear(ADC0, ADC_FLAG_EOC);
    } else {
        // ADC��ʱ��ʹ��֮ǰ��ֵ��Ĭ��ֵ
        my_printf(DEBUG_USART, "ADC timeout\r\n");
    }

    // LED1��1��������˸��5 * 100ms = 0.5s����5 * 100ms = 0.5s�أ�
    led_counter++;
    if(led_counter >= 5)
    {
        led_counter = 0;
        ucLed[0] = !ucLed[0];  // ÿ0.5���л�LED1��1���������ڣ�
    }

    // �ɱ�����ADC����
    sampling_counter++;
    if(sampling_counter >= sampling_period)
    {
        sampling_counter = 0;

        // ������ʾ��ѹ��ADC_value * Ratio * 3.3V / 4096
        // ʹ��64λ�����������
        uint64_t temp_calc = (uint64_t)adc_value[0] * current_ratio * 3300;
        uint32_t displayed_voltage_mv = temp_calc / (4096 * 100);  // �Ժ�������

        // ��ȡ��ǰʱ��
        char current_time[32];
        rtc_get_time_string(current_time, sizeof(current_time));

        // ����ѹ�Ƿ񳬹���ֵ
        uint32_t limit_mv = (current_limit * 1000) / 100;  // ����ֵת��Ϊ����
        uint8_t is_over_limit = (displayed_voltage_mv > limit_mv);

        if(is_over_limit)
        {
            // ���ޣ���LED2
            ucLed[1] = 1;  // LED2��
        }
        else
        {
            // ������Χ���ر�LED2
            ucLed[1] = 0;  // LED2��
        }

        // ����ģʽ�͵�ѹ״̬д���ļ�
        if(is_over_limit)
        {
            // ���ޣ���д��overLimit�ļ���
            char voltage_str[32];
            snprintf(voltage_str, sizeof(voltage_str), "%lu.%02luV", displayed_voltage_mv/1000, (displayed_voltage_mv%1000)/10);
            write_data_to_file(current_time, voltage_str, STORAGE_MODE_OVERLIMIT);
        }
        else
        {
            // ������ѹ����д��sample�ļ���
            char voltage_str[32];
            snprintf(voltage_str, sizeof(voltage_str), "%lu.%02luV", displayed_voltage_mv/1000, (displayed_voltage_mv%1000)/10);
            write_sample_to_file(current_time, voltage_str);
        }

        // ����ģʽ������ɶ���ʽ
        if(is_over_limit)
        {
            my_printf(DEBUG_USART, "%s ch0=%lu.%02luV OverLimit(%lu.%02luV)\r\n",
                     current_time,
                     displayed_voltage_mv/1000, (displayed_voltage_mv%1000)/10,
                     limit_mv/1000, (limit_mv%1000)/10);
        }
        else
        {
            my_printf(DEBUG_USART, "%s ch0=%lu.%02luV\r\n", current_time, displayed_voltage_mv/1000, (displayed_voltage_mv%1000)/10);
        }
    }
}