<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\2025916750.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\2025916750.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Mon Aug 04 10:06:46 2025
<BR><P>
<H3>Maximum Stack Usage =       2500 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
sampling_task &rArr; write_sample_to_file &rArr; write_data_to_file &rArr; open_new_file &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[2d]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2d]">ADC_IRQHandler</a><BR>
 <LI><a href="#[15]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[15]">BusFault_Handler</a><BR>
 <LI><a href="#[18]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[18]">DebugMon_Handler</a><BR>
 <LI><a href="#[13]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[13]">HardFault_Handler</a><BR>
 <LI><a href="#[14]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[14]">MemManage_Handler</a><BR>
 <LI><a href="#[12]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[12]">NMI_Handler</a><BR>
 <LI><a href="#[19]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[19]">PendSV_Handler</a><BR>
 <LI><a href="#[17]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[17]">SVC_Handler</a><BR>
 <LI><a href="#[16]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[16]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[77]">$Super$$SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from systick_wrapper_ual.o(.text)
 <LI><a href="#[2d]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[69]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">EXTI10_15_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6b]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[73]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">SDIO_IRQHandler</a> from gd32f4xx_it.o(i.SDIO_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6e]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6f]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[70]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">SysTick_Handler</a> from systick_wrapper_ual.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[74]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[1d]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[72]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[71]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6a]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6c]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6d]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">USART0_IRQHandler</a> from gd32f4xx_it.o(i.USART0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[68]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[80]">__main</a> from __main.o(!!!main) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[7f]">__perf_counter_init</a> from perf_counter.o(i.__perf_counter_init) referenced from perf_counter.o(.init_array)
 <LI><a href="#[7e]">_get_lc_ctype</a> from lc_ctype_c.o(locale$$code) referenced from rt_ctype_table.o(.text)
 <LI><a href="#[7b]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[7a]">_sbackspace</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[7c]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[79]">_sgetc</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[78]">_snputc</a> from _snputc.o(.text) referenced 2 times from vsnprintf.o(.text)
 <LI><a href="#[c]">btn_task</a> from btn_app.o(i.btn_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[7d]">isspace</a> from isspace.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[a]">led_task</a> from led_app.o(i.led_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[10]">log_periodic_sync</a> from log_app.o(i.log_periodic_sync) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[b]">oled_task</a> from oled_app.o(i.oled_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[76]">perfc_port_insert_to_system_timer_insert_ovf_handler</a> from perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler) referenced from systick_wrapper_ual.o(.text)
 <LI><a href="#[e]">rtc_task</a> from rtc_app.o(i.rtc_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[f]">sampling_task</a> from usart_app.o(i.sampling_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[d]">uart_task</a> from usart_app.o(i.uart_task) referenced 2 times from scheduler.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[80]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[82]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[84]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[209]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[20a]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[20b]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[20c]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[20d]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[85]"></a>_printf_n</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_n.o(.ARM.Collect$$_printf_percent$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_charcount
</UL>

<P><STRONG><a name="[c9]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[87]"></a>_printf_p</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_p.o(.ARM.Collect$$_printf_percent$$00000002))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_p &rArr; _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
</UL>

<P><STRONG><a name="[89]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[8b]"></a>_printf_e</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_e.o(.ARM.Collect$$_printf_percent$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_e &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[8c]"></a>_printf_g</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_g.o(.ARM.Collect$$_printf_percent$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_g &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[8d]"></a>_printf_a</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_a.o(.ARM.Collect$$_printf_percent$$00000006))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = _printf_a &rArr; _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[20e]"></a>_printf_ll</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007))

<P><STRONG><a name="[8f]"></a>_printf_i</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_i.o(.ARM.Collect$$_printf_percent$$00000008))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_i &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[91]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[92]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[93]"></a>_printf_o</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_o &rArr; _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[95]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[97]"></a>_printf_lli</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lli &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[99]"></a>_printf_lld</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lld &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[9a]"></a>_printf_llu</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_llu &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[9b]"></a>_printf_llo</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_llo &rArr; _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
</UL>

<P><STRONG><a name="[9d]"></a>_printf_llx</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_llx &rArr; _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
</UL>

<P><STRONG><a name="[20f]"></a>_printf_l</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_l.o(.ARM.Collect$$_printf_percent$$00000012))

<P><STRONG><a name="[9f]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[a1]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[a3]"></a>_printf_lc</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[a5]"></a>_printf_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_ls &rArr; _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
</UL>

<P><STRONG><a name="[210]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[b2]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[a7]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[211]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[a9]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[212]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[213]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[214]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[215]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[ab]"></a>__rt_lib_init_lc_ctype_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000012))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_ctype_2 &rArr; _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
</UL>

<P><STRONG><a name="[216]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[217]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[ac]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[218]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[219]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[21a]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[21b]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[ae]"></a>__rt_lib_init_cpp_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000031))
<BR><BR>[Stack]<UL><LI>Max Depth = 16 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_cpp_2 &rArr; __cpp_initialize__aeabi_
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__cpp_initialize__aeabi_
</UL>

<P><STRONG><a name="[21c]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[21d]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[21e]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[21f]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[220]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[221]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[222]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[223]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[224]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[b7]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[225]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[226]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[227]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[228]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[229]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[22a]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[22b]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[22c]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[83]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[22d]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[af]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[b1]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[22e]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[b3]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 2220 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; read_and_display_device_id &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[22f]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[e5]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[b6]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[230]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[b8]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[11]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e4]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1a]"></a>SysTick_Handler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick_wrapper_ual.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[231]"></a>__ensure_systick_wrapper</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, systick_wrapper_ual.o(.text), UNUSED)

<P><STRONG><a name="[bb]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_sdiv
</UL>

<P><STRONG><a name="[232]"></a>_ll_udiv</STRONG> (Thumb, 238 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[19a]"></a>__aeabi_ldivmod</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_system_ms
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_insert_to_system_timer_insert_ovf_handler
</UL>

<P><STRONG><a name="[ba]"></a>_ll_sdiv</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, llsdiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[bc]"></a>vsnprintf</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, vsnprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_formatted
</UL>

<P><STRONG><a name="[bf]"></a>__2snprintf</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, __2snprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_to_pre_test_buffer
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_filename
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_time_string
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_recovery_check
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_start_session
</UL>

<P><STRONG><a name="[c1]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
</UL>

<P><STRONG><a name="[c2]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
</UL>

<P><STRONG><a name="[c0]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[90]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_signed
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_i
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[c6]"></a>_printf_longlong_hex</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[96]"></a>_printf_int_hex</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[9e]"></a>_printf_ll_hex</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llx
</UL>

<P><STRONG><a name="[88]"></a>_printf_hex_ptr</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_p
</UL>

<P><STRONG><a name="[c7]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[ca]"></a>__0sscanf</STRONG> (Thumb, 52 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[cc]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[196]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flush_pre_test_buffer_to_log
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[116]"></a>strcpy</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_device_id_to_flash
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_to_pre_test_buffer
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[115]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flush_pre_test_buffer_to_log
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_to_pre_test_buffer
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_raw
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
</UL>

<P><STRONG><a name="[1c4]"></a>strncmp</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[110]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[ce]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[233]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[cf]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[234]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[235]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[236]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[111]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[d0]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[237]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[153]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_pre_test_buffer_from_flash
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_pre_test_buffer
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[238]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[239]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[d1]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[1c3]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
</UL>

<P><STRONG><a name="[23a]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[2]"></a>__rt_heap_escrow</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1]"></a>__rt_heap_expand</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[81]"></a>__cpp_initialize__aeabi_</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, init_aeabi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __cpp_initialize__aeabi_
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_cpp_2
</UL>

<P><STRONG><a name="[c3]"></a>_printf_truncate_signed</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[c4]"></a>_printf_truncate_unsigned</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[c5]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[86]"></a>_printf_charcount</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, _printf_charcount.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_n
</UL>

<P><STRONG><a name="[bd]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>

<P><STRONG><a name="[be]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>

<P><STRONG><a name="[78]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> __2snprintf.o(.text)
<LI> vsnprintf.o(.text)
</UL>
<P><STRONG><a name="[d2]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[a0]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[a2]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[d3]"></a>_printf_wctomb</STRONG> (Thumb, 182 bytes, Stack size 56 bytes, _printf_wctomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>

<P><STRONG><a name="[98]"></a>_printf_longlong_dec</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, _printf_longlong_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llu
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lld
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lli
</UL>

<P><STRONG><a name="[d6]"></a>_printf_longlong_oct</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[94]"></a>_printf_int_oct</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_o
</UL>

<P><STRONG><a name="[9c]"></a>_printf_ll_oct</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llo
</UL>

<P><STRONG><a name="[cd]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>

<P><STRONG><a name="[cb]"></a>__vfscanf_char</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[79]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[7a]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[b9]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[23b]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[e3]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[23c]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[d5]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[7d]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = isspace &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[23d]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[de]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[e1]"></a>_printf_fp_hex_real</STRONG> (Thumb, 756 bytes, Stack size 72 bytes, _printf_fp_hex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[e2]"></a>_printf_lcs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[a4]"></a>_printf_wchar</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lc
</UL>

<P><STRONG><a name="[a6]"></a>_printf_wstring</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ls
</UL>

<P><STRONG><a name="[d7]"></a>__vfscanf</STRONG> (Thumb, 880 bytes, Stack size 96 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = __vfscanf &rArr; _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[d4]"></a>_wcrtomb</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, _wcrtomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>

<P><STRONG><a name="[23e]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[23f]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[240]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[b0]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[d8]"></a>__rt_ctype_table</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, rt_ctype_table.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
</UL>

<P><STRONG><a name="[aa]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
</UL>

<P><STRONG><a name="[e0]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[da]"></a>_btod_etento</STRONG> (Thumb, 224 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[b5]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[db]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[e7]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[e6]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[e8]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[e9]"></a>_e2e</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[dc]"></a>_btod_ediv</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
</UL>

<P><STRONG><a name="[dd]"></a>_btod_emul</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
</UL>

<P><STRONG><a name="[ea]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[15]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f6]"></a>OLED_Clear</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = OLED_Clear &rArr; OLED_Write_data &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[f9]"></a>OLED_Init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_Write_data &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fa]"></a>OLED_Set_Position</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, oled.o(i.OLED_Set_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[fb]"></a>OLED_ShowChar</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
</UL>

<P><STRONG><a name="[fc]"></a>OLED_ShowStr</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowStr))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
</UL>

<P><STRONG><a name="[f7]"></a>OLED_Write_cmd</STRONG> (Thumb, 264 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop_on_bus
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start_on_bus
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_master_addressing
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_flag_get
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_flag_clear
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_dma_config
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_config
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_memory_address_config
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Position
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[f8]"></a>OLED_Write_data</STRONG> (Thumb, 264 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = OLED_Write_data &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop_on_bus
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start_on_bus
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_master_addressing
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_flag_get
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_flag_clear
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_dma_config
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_config
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_memory_address_config
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>

<P><STRONG><a name="[19]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SDIO_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SDIO_IRQHandler &rArr; sd_interrupts_process &rArr; sd_transfer_stop &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>$Super$$SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> systick_wrapper_ual.o(.text)
</UL>
<P><STRONG><a name="[74]"></a>SystemInit</STRONG> (Thumb, 276 bytes, Stack size 24 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SystemInit &rArr; system_clock_config &rArr; system_clock_240m_25m_hxtal
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_soft_delay_
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[40]"></a>USART0_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.USART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = USART0_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_config
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_get
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[df]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[7f]"></a>__perf_counter_init</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, perf_counter.o(i.__perf_counter_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = __perf_counter_init &rArr; init_cycle_counter &rArr; update_perf_counter &rArr; get_system_ticks &rArr; check_systick
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_cycle_counter
</UL>
<BR>[Address Reference Count : 1]<UL><LI> perf_counter.o(.init_array)
</UL>
<P><STRONG><a name="[1a4]"></a>__perf_os_patch_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, perf_counter.o(i.__perf_os_patch_init))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_cycle_counter
</UL>

<P><STRONG><a name="[c8]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[12a]"></a>adc_calibration_enable</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_calibration_enable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[122]"></a>adc_channel_length_config</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_channel_length_config))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[11a]"></a>adc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[121]"></a>adc_data_alignment_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_data_alignment_config))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[127]"></a>adc_dma_mode_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_dma_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[126]"></a>adc_dma_request_after_last_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_dma_request_after_last_enable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[128]"></a>adc_enable</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_enable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[125]"></a>adc_external_trigger_config</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_external_trigger_config))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[124]"></a>adc_external_trigger_source_config</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_external_trigger_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[1d8]"></a>adc_flag_clear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[1d6]"></a>adc_flag_get</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[123]"></a>adc_routine_channel_config</STRONG> (Thumb, 126 bytes, Stack size 12 bytes, gd32f4xx_adc.o(i.adc_routine_channel_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = adc_routine_channel_config
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[1d7]"></a>adc_routine_data_read</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_routine_data_read))
<BR><BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[12b]"></a>adc_software_trigger_enable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_software_trigger_enable))
<BR><BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[120]"></a>adc_special_function_config</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_special_function_config))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[11f]"></a>adc_sync_mode_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_sync_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[113]"></a>add_to_pre_test_buffer</STRONG> (Thumb, 82 bytes, Stack size 304 bytes, usart_app.o(i.add_to_pre_test_buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 496 + Unknown Stack Size
<LI>Call Chain = add_to_pre_test_buffer &rArr; rtc_get_time_string &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_pre_test_buffer_to_flash
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_time_string
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_formatted
</UL>

<P><STRONG><a name="[1d5]"></a>bcd_to_decimal</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, rtc_app.o(i.bcd_to_decimal))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_time_string
</UL>

<P><STRONG><a name="[118]"></a>bsp_adc_init</STRONG> (Thumb, 242 bytes, Stack size 48 bytes, cmic_gd32f470vet6.o(i.bsp_adc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = bsp_adc_init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_circulation_enable
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_sync_mode_config
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_special_function_config
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_routine_channel_config
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_source_config
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_config
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_request_after_last_enable
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_mode_enable
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_data_alignment_config
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_clock_config
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_channel_length_config
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_enable
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_software_trigger_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12c]"></a>bsp_btn_init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, cmic_gd32f470vet6.o(i.bsp_btn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_btn_init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12d]"></a>bsp_gd25qxx_init</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, cmic_gd32f470vet6.o(i.bsp_gd25qxx_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = bsp_gd25qxx_init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[131]"></a>bsp_led_init</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, cmic_gd32f470vet6.o(i.bsp_led_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = bsp_led_init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[132]"></a>bsp_oled_init</STRONG> (Thumb, 224 bytes, Stack size 56 bytes, cmic_gd32f470vet6.o(i.bsp_oled_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = bsp_oled_init &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_mode_addr_config
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_enable
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clock_config
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_ack_config
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_para_struct_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_circulation_disable
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[135]"></a>bsp_rtc_init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, cmic_gd32f470vet6.o(i.bsp_rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_rtc_init &rArr; bsp_rtc_pre_cfg &rArr; rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_pre_cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[137]"></a>bsp_rtc_pre_cfg</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, cmic_gd32f470vet6.o(i.bsp_rtc_pre_cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = bsp_rtc_pre_cfg &rArr; rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[13d]"></a>bsp_usart_init</STRONG> (Thumb, 252 bytes, Stack size 56 bytes, cmic_gd32f470vet6.o(i.bsp_usart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = bsp_usart_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_dma_receive_config
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_circulation_disable
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c]"></a>btn_task</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, btn_app.o(i.btn_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 2204 + Unknown Stack Size
<LI>Call Chain = btn_task &rArr; key_task &rArr; toggle_sampling &rArr; start_sampling &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_task
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[152]"></a>clear_pre_test_buffer</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, usart_app.o(i.clear_pre_test_buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = clear_pre_test_buffer &rArr; save_pre_test_buffer_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_pre_test_buffer_to_flash
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flush_pre_test_buffer_to_log
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[154]"></a>close_all_files</STRONG> (Thumb, 62 bytes, Stack size 32 bytes, sd_app.o(i.close_all_files))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = close_all_files &rArr; f_close &rArr; f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
</UL>

<P><STRONG><a name="[157]"></a>close_log_file_by_number</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, log_app.o(i.close_log_file_by_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 2196 + Unknown Stack Size
<LI>Call Chain = close_log_file_by_number &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_current_log_number
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_formatted
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_close
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[16e]"></a>clust2sect</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, ff.o(i.clust2sect))
<BR><BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[129]"></a>delay_1ms</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[10a]"></a>delay_decrement</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$SysTick_Handler
</UL>

<P><STRONG><a name="[ef]"></a>delay_ms</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, perf_counter.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = delay_ms &rArr; get_system_ticks &rArr; check_systick
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_system_ticks
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[150]"></a>disk_initialize</STRONG> (Thumb, 112 bytes, Stack size 80 bytes, diskio.o(i.disk_initialize))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = disk_initialize &rArr; sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; sdio_data_config
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_information_get
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_mode_config
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_filesystem
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[179]"></a>disk_ioctl</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, diskio.o(i.disk_ioctl))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = disk_ioctl &rArr; sd_card_capacity_get
</UL>
<BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_capacity_get
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
</UL>

<P><STRONG><a name="[151]"></a>disk_read</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, diskio.o(i.disk_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = disk_read &rArr; sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[14f]"></a>disk_status</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, diskio.o(i.disk_status))
<BR><BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[17d]"></a>disk_write</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, diskio.o(i.disk_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_erase
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>

<P><STRONG><a name="[107]"></a>dma_channel_disable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_channel_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[104]"></a>dma_channel_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_channel_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[11d]"></a>dma_channel_subperipheral_select</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_channel_subperipheral_select))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[134]"></a>dma_circulation_disable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_circulation_disable))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
</UL>

<P><STRONG><a name="[11e]"></a>dma_circulation_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_circulation_enable))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[11b]"></a>dma_deinit</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_deinit))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[106]"></a>dma_flag_clear</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[105]"></a>dma_flag_get</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[183]"></a>dma_flow_controller_config</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_flow_controller_config))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[101]"></a>dma_memory_address_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_memory_address_config))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[182]"></a>dma_multi_data_mode_init</STRONG> (Thumb, 150 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_multi_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_multi_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>

<P><STRONG><a name="[11c]"></a>dma_single_data_mode_init</STRONG> (Thumb, 140 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_single_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_single_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>

<P><STRONG><a name="[133]"></a>dma_single_data_para_struct_init</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_single_data_para_struct_init))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
</UL>

<P><STRONG><a name="[102]"></a>dma_transfer_number_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_transfer_number_config))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[10f]"></a>dma_transfer_number_get</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_transfer_number_get))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[156]"></a>f_close</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ff.o(i.f_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = f_close &rArr; f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;open_new_file
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close_all_files
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_recovery_check
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_start_session
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_close
</UL>

<P><STRONG><a name="[207]"></a>f_closedir</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ff.o(i.f_closedir))
<BR><BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_tf_card
</UL>

<P><STRONG><a name="[185]"></a>f_getfree</STRONG> (Thumb, 226 bytes, Stack size 48 bytes, ff.o(i.f_getfree))
<BR><BR>[Stack]<UL><LI>Max Depth = 260<LI>Call Chain = f_getfree &rArr; chk_mounted &rArr; check_fs &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_tf_card
</UL>

<P><STRONG><a name="[186]"></a>f_mount</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, ff.o(i.f_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = f_mount &rArr; find_volume &rArr; check_fs &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_filesystem
</UL>

<P><STRONG><a name="[189]"></a>f_open</STRONG> (Thumb, 368 bytes, Stack size 96 bytes, ff.o(i.f_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 460<LI>Call Chain = f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>
<BR>[Called By]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;open_new_file
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_recovery_check
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_start_session
</UL>

<P><STRONG><a name="[18d]"></a>f_opendir</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, ff.o(i.f_opendir))
<BR><BR>[Stack]<UL><LI>Max Depth = 340<LI>Call Chain = f_opendir &rArr; follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_tf_card
</UL>

<P><STRONG><a name="[155]"></a>f_sync</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, ff.o(i.f_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fattime
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;open_new_file
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close_all_files
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_periodic_sync
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_raw
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_start_session
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_close
</UL>

<P><STRONG><a name="[190]"></a>f_write</STRONG> (Thumb, 474 bytes, Stack size 40 bytes, ff.o(i.f_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 268<LI>Call Chain = f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;validate
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_raw
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
</UL>

<P><STRONG><a name="[191]"></a>ff_oem2uni</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ff.o(i.ff_oem2uni))
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
</UL>

<P><STRONG><a name="[192]"></a>ff_uni2oem</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ff.o(i.ff_uni2oem))
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
</UL>

<P><STRONG><a name="[160]"></a>ff_wtoupper</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ff.o(i.ff_wtoupper))
<BR><BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>

<P><STRONG><a name="[194]"></a>flush_pre_test_buffer_to_log</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, usart_app.o(i.flush_pre_test_buffer_to_log))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = flush_pre_test_buffer_to_log &rArr; log_write_raw &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_pre_test_buffer
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_raw
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[1b4]"></a>gd32f4xx_firmware_version_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.gd32f4xx_firmware_version_get))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[171]"></a>gen_numname</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, ff.o(i.gen_numname))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = gen_numname
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[197]"></a>generate_filename</STRONG> (Thumb, 178 bytes, Stack size 56 bytes, sd_app.o(i.generate_filename))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = generate_filename &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;open_new_file
</UL>

<P><STRONG><a name="[158]"></a>get_current_log_number</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, log_app.o(i.get_current_log_number))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close_log_file_by_number
</UL>

<P><STRONG><a name="[162]"></a>get_fat</STRONG> (Thumb, 202 bytes, Stack size 24 bytes, ff.o(i.get_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[1c0]"></a>get_sampling_period</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usart_app.o(i.get_sampling_period))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[1bf]"></a>get_sampling_status</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usart_app.o(i.get_sampling_status))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[199]"></a>get_system_ms</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, perf_counter.o(i.get_system_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_system_ms &rArr; check_systick
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_systick
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
</UL>

<P><STRONG><a name="[168]"></a>get_system_ticks</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, perf_counter.o(i.get_system_ticks))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_system_ticks &rArr; check_systick
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_systick
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_perf_counter
</UL>

<P><STRONG><a name="[12e]"></a>gpio_af_set</STRONG> (Thumb, 100 bytes, Stack size 28 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gd25qxx_init
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
</UL>

<P><STRONG><a name="[f0]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[ee]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[1a7]"></a>gpio_input_bit_get</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>

<P><STRONG><a name="[ec]"></a>gpio_mode_set</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gd25qxx_init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
</UL>

<P><STRONG><a name="[ed]"></a>gpio_output_options_set</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gd25qxx_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
</UL>

<P><STRONG><a name="[f5]"></a>i2c_ack_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_ack_config))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[f2]"></a>i2c_clock_config</STRONG> (Thumb, 184 bytes, Stack size 16 bytes, gd32f4xx_i2c.o(i.i2c_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[f1]"></a>i2c_deinit</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, gd32f4xx_i2c.o(i.i2c_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = i2c_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[103]"></a>i2c_dma_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_dma_config))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[f4]"></a>i2c_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_enable))
<BR><BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[100]"></a>i2c_flag_clear</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[fd]"></a>i2c_flag_get</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[ff]"></a>i2c_master_addressing</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_master_addressing))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[f3]"></a>i2c_mode_addr_config</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f4xx_i2c.o(i.i2c_mode_addr_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = i2c_mode_addr_config
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Bus_Reset
</UL>

<P><STRONG><a name="[fe]"></a>i2c_start_on_bus</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_start_on_bus))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[108]"></a>i2c_stop_on_bus</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_i2c.o(i.i2c_stop_on_bus))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[19f]"></a>increment_boot_count</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, log_app.o(i.increment_boot_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = increment_boot_count &rArr; write_log_config_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_config_to_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init
</UL>

<P><STRONG><a name="[112]"></a>init_cycle_counter</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, perf_counter.o(i.init_cycle_counter))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = init_cycle_counter &rArr; update_perf_counter &rArr; get_system_ticks &rArr; check_systick
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__perf_os_patch_init
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_init_system_timer
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_clear_system_timer_ovf_pending
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_perf_counter
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__perf_counter_init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1a5]"></a>init_filesystem</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, sd_app.o(i.init_filesystem))
<BR><BR>[Stack]<UL><LI>Max Depth = 2196 + Unknown Stack Size
<LI>Call Chain = init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_tf_card
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;open_new_file
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init
</UL>

<P><STRONG><a name="[1c8]"></a>is_log_initialized</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, log_app.o(i.is_log_initialized))
<BR><BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[1a6]"></a>key_read</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, btn_app.o(i.key_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = key_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_task
</UL>

<P><STRONG><a name="[146]"></a>key_task</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, btn_app.o(i.key_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 2204 + Unknown Stack Size
<LI>Call Chain = key_task &rArr; toggle_sampling &rArr; start_sampling &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toggle_sampling
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sampling_period
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_read
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btn_task
</UL>

<P><STRONG><a name="[1aa]"></a>led_disp</STRONG> (Thumb, 128 bytes, Stack size 12 bytes, led_app.o(i.led_disp))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = led_disp
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[a]"></a>led_task</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, led_app.o(i.led_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = led_task &rArr; led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[1ab]"></a>load_config_from_flash_on_startup</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, usart_app.o(i.load_config_from_flash_on_startup))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = load_config_from_flash_on_startup &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ad]"></a>load_pre_test_buffer_from_flash</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usart_app.o(i.load_pre_test_buffer_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = load_pre_test_buffer_from_flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15b]"></a>log_close</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, log_app.o(i.log_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = log_close &rArr; f_close &rArr; f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close_log_file_by_number
</UL>

<P><STRONG><a name="[1ae]"></a>log_init</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, log_app.o(i.log_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 2204 + Unknown Stack Size
<LI>Call Chain = log_init &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_filesystem
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;smart_recovery_check
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_log_config_from_flash
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_start_session
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;increment_boot_count
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10]"></a>log_periodic_sync</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, log_app.o(i.log_periodic_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = log_periodic_sync &rArr; f_sync &rArr; sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[1b1]"></a>log_start_session</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, log_app.o(i.log_start_session))
<BR><BR>[Stack]<UL><LI>Max Depth = 868 + Unknown Stack Size
<LI>Call Chain = log_start_session &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_formatted
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init
</UL>

<P><STRONG><a name="[1b2]"></a>log_write</STRONG> (Thumb, 70 bytes, Stack size 304 bytes, log_app.o(i.log_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 572 + Unknown Stack Size
<LI>Call Chain = log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_time_string
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_formatted
</UL>

<P><STRONG><a name="[15a]"></a>log_write_formatted</STRONG> (Thumb, 50 bytes, Stack size 280 bytes, log_app.o(i.log_write_formatted))
<BR><BR>[Stack]<UL><LI>Max Depth = 852 + Unknown Stack Size
<LI>Call Chain = log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_to_pre_test_buffer
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_start_session
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close_log_file_by_number
</UL>

<P><STRONG><a name="[195]"></a>log_write_raw</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, log_app.o(i.log_write_raw))
<BR><BR>[Stack]<UL><LI>Max Depth = 284<LI>Call Chain = log_write_raw &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flush_pre_test_buffer_to_log
</UL>

<P><STRONG><a name="[b4]"></a>main</STRONG> (Thumb, 136 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 2220 + Unknown Stack Size
<LI>Call Chain = main &rArr; read_and_display_device_id &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_device_id_to_flash
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_and_display_device_id
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_pre_test_buffer_from_flash
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_config_from_flash_on_startup
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_pre_test_buffer
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_fatfs_init
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gd25qxx_init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_init
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_log_system
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_cycle_counter
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd32f4xx_firmware_version_get
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[159]"></a>my_printf</STRONG> (Thumb, 166 bytes, Stack size 1328 bytes, usart_app.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 2180 + Unknown Stack Size
<LI>Call Chain = my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_to_pre_test_buffer
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_formatted
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_tf_card
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_flash
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_and_display_device_id
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_filesystem
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sampling_period
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close_log_file_by_number
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[144]"></a>nvic_irq_enable</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_fatfs_init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
</UL>

<P><STRONG><a name="[1bd]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>

<P><STRONG><a name="[1be]"></a>oled_printf</STRONG> (Thumb, 52 bytes, Stack size 544 bytes, oled_app.o(i.oled_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 708 + Unknown Stack Size
<LI>Call Chain = oled_printf &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowStr
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[b]"></a>oled_task</STRONG> (Thumb, 254 bytes, Stack size 24 bytes, oled_app.o(i.oled_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 732 + Unknown Stack Size
<LI>Call Chain = oled_task &rArr; oled_printf &rArr; OLED_ShowStr &rArr; OLED_ShowChar &rArr; OLED_Set_Position &rArr; OLED_Write_cmd &rArr; I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sampling_status
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_sampling_period
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_printf
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[1c1]"></a>open_new_file</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, sd_app.o(i.open_new_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 2228 + Unknown Stack Size
<LI>Call Chain = open_new_file &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_filesystem
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_filename
</UL>
<BR>[Called By]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
</UL>

<P><STRONG><a name="[1a2]"></a>perfc_port_clear_system_timer_ovf_pending</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, perfc_port_default.o(i.perfc_port_clear_system_timer_ovf_pending))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_cycle_counter
</UL>

<P><STRONG><a name="[14c]"></a>perfc_port_get_system_timer_elapsed</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, perfc_port_default.o(i.perfc_port_get_system_timer_elapsed))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_systick
</UL>

<P><STRONG><a name="[208]"></a>perfc_port_get_system_timer_freq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, perfc_port_default.o(i.perfc_port_get_system_timer_freq))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_perf_counter
</UL>

<P><STRONG><a name="[14b]"></a>perfc_port_get_system_timer_top</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, perfc_port_default.o(i.perfc_port_get_system_timer_top))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_insert_to_system_timer_insert_ovf_handler
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_systick
</UL>

<P><STRONG><a name="[1a1]"></a>perfc_port_init_system_timer</STRONG> (Thumb, 50 bytes, Stack size 12 bytes, perfc_port_default.o(i.perfc_port_init_system_timer))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = perfc_port_init_system_timer
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_cycle_counter
</UL>

<P><STRONG><a name="[76]"></a>perfc_port_insert_to_system_timer_insert_ovf_handler</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, perf_counter.o(i.perfc_port_insert_to_system_timer_insert_ovf_handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = perfc_port_insert_to_system_timer_insert_ovf_handler &rArr; __aeabi_ldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_get_system_timer_top
</UL>
<BR>[Address Reference Count : 1]<UL><LI> systick_wrapper_ual.o(.text)
</UL>
<P><STRONG><a name="[14d]"></a>perfc_port_is_system_timer_ovf_pending</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, perfc_port_default.o(i.perfc_port_is_system_timer_ovf_pending))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_systick
</UL>

<P><STRONG><a name="[136]"></a>pmu_backup_write_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[1c2]"></a>process_command</STRONG> (Thumb, 302 bytes, Stack size 24 bytes, usart_app.o(i.process_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 2284 + Unknown Stack Size
<LI>Call Chain = process_command &rArr; test_tf_card &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_tf_card
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_flash
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flush_pre_test_buffer_to_log
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_log_initialized
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_boot_count_and_save
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_start_session
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_close
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_log_initialized
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;increment_boot_count
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close_log_file_by_number
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[163]"></a>put_fat</STRONG> (Thumb, 246 bytes, Stack size 32 bytes, ff.o(i.put_fat))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
</UL>
<BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remove_chain
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>

<P><STRONG><a name="[138]"></a>rcu_all_reset_flag_clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[19c]"></a>rcu_clock_freq_get</STRONG> (Thumb, 200 bytes, Stack size 52 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clock_config
</UL>

<P><STRONG><a name="[1d3]"></a>rcu_flag_get</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
</UL>

<P><STRONG><a name="[139]"></a>rcu_osci_on</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_pre_cfg
</UL>

<P><STRONG><a name="[13a]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 306 bytes, Stack size 16 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_pre_cfg
</UL>

<P><STRONG><a name="[119]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_pre_cfg
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_oled_init
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_led_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gd25qxx_init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_btn_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
</UL>

<P><STRONG><a name="[19e]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_deinit
</UL>

<P><STRONG><a name="[19d]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_deinit
</UL>

<P><STRONG><a name="[13b]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_pre_cfg
</UL>

<P><STRONG><a name="[1b7]"></a>read_and_display_device_id</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, usart_app.o(i.read_and_display_device_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 2220 + Unknown Stack Size
<LI>Call Chain = read_and_display_device_id &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1af]"></a>read_log_config_from_flash</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, log_app.o(i.read_log_config_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = read_log_config_from_flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init
</UL>

<P><STRONG><a name="[1d4]"></a>read_sampling_cycle_from_flash</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, usart_app.o(i.read_sampling_cycle_from_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = read_sampling_cycle_from_flash &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
</UL>

<P><STRONG><a name="[1b5]"></a>reset_log_system</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, log_app.o(i.reset_log_system))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = reset_log_system &rArr; write_log_config_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_config_to_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[198]"></a>rtc_current_time_get</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, gd32f4xx_rtc.o(i.rtc_current_time_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generate_filename
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_task
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_time_string
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_task
</UL>

<P><STRONG><a name="[114]"></a>rtc_get_time_string</STRONG> (Thumb, 88 bytes, Stack size 48 bytes, rtc_app.o(i.rtc_get_time_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = rtc_get_time_string &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bcd_to_decimal
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_to_pre_test_buffer
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
</UL>

<P><STRONG><a name="[13c]"></a>rtc_register_sync_wait</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_pre_cfg
</UL>

<P><STRONG><a name="[e]"></a>rtc_task</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, rtc_app.o(i.rtc_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rtc_task &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[f]"></a>sampling_task</STRONG> (Thumb, 406 bytes, Stack size 104 bytes, usart_app.o(i.sampling_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 2500 + Unknown Stack Size
<LI>Call Chain = sampling_task &rArr; write_sample_to_file &rArr; write_data_to_file &rArr; open_new_file &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_to_file
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_get_time_string
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_software_trigger_enable
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_routine_data_read
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_get
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_flag_clear
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[117]"></a>save_pre_test_buffer_to_flash</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, usart_app.o(i.save_pre_test_buffer_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = save_pre_test_buffer_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>
<BR>[Called By]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_pre_test_buffer
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_to_pre_test_buffer
</UL>

<P><STRONG><a name="[1b9]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(i.scheduler_init))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ba]"></a>scheduler_run</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, scheduler.o(i.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = scheduler_run &rArr; get_system_ms &rArr; check_systick
</UL>
<BR>[Calls]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_system_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[17c]"></a>sd_block_read</STRONG> (Thumb, 464 bytes, Stack size 40 bytes, sdio_sdcard.o(i.sd_block_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = sd_block_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[17f]"></a>sd_block_write</STRONG> (Thumb, 634 bytes, Stack size 48 bytes, sdio_sdcard.o(i.sd_block_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = sd_block_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[177]"></a>sd_bus_mode_config</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_bus_mode_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = sd_bus_mode_config &rArr; sd_bus_width_config &rArr; sd_scr_get &rArr; sdio_data_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[17a]"></a>sd_card_capacity_get</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_card_capacity_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sd_card_capacity_get
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
</UL>

<P><STRONG><a name="[174]"></a>sd_card_information_get</STRONG> (Thumb, 504 bytes, Stack size 20 bytes, sdio_sdcard.o(i.sd_card_information_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = sd_card_information_get
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[1f0]"></a>sd_card_init</STRONG> (Thumb, 234 bytes, Stack size 24 bytes, sdio_sdcard.o(i.sd_card_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = sd_card_init &rArr; r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_get
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[175]"></a>sd_card_select_deselect</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, sdio_sdcard.o(i.sd_card_select_deselect))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = sd_card_select_deselect &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[176]"></a>sd_cardstatus_get</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_cardstatus_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = sd_cardstatus_get &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[180]"></a>sd_erase</STRONG> (Thumb, 230 bytes, Stack size 24 bytes, sdio_sdcard.o(i.sd_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = sd_erase &rArr; sd_card_state_get &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[1b8]"></a>sd_fatfs_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, sd_app.o(i.sd_fatfs_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sd_fatfs_init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[173]"></a>sd_init</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, sdio_sdcard.o(i.sd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_init &rArr; sd_power_on &rArr; sdio_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_deinit
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_config
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_config
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[109]"></a>sd_interrupts_process</STRONG> (Thumb, 226 bytes, Stack size 24 bytes, sdio_sdcard.o(i.sd_interrupts_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = sd_interrupts_process &rArr; sd_transfer_stop &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_get
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_flag_clear
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_disable
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDIO_IRQHandler
</UL>

<P><STRONG><a name="[17b]"></a>sd_multiblocks_read</STRONG> (Thumb, 572 bytes, Stack size 40 bytes, sdio_sdcard.o(i.sd_multiblocks_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = sd_multiblocks_read &rArr; dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_receive_config
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
</UL>

<P><STRONG><a name="[17e]"></a>sd_multiblocks_write</STRONG> (Thumb, 734 bytes, Stack size 48 bytes, sdio_sdcard.o(i.sd_multiblocks_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_interrupt_enable
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_disable
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_enable
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dma_disable
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_write
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_datablocksize_get
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_config
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>

<P><STRONG><a name="[1f3]"></a>sd_power_on</STRONG> (Thumb, 270 bytes, Stack size 32 bytes, sdio_sdcard.o(i.sd_power_on))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = sd_power_on &rArr; sdio_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_power_state_set
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_hardware_clock_disable
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_enable
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_clock_config
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_bus_mode_set
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[178]"></a>sd_transfer_mode_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, sdio_sdcard.o(i.sd_transfer_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
</UL>

<P><STRONG><a name="[1f7]"></a>sd_transfer_stop</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, sdio_sdcard.o(i.sd_transfer_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = sd_transfer_stop &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1ed]"></a>sdio_bus_mode_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_bus_mode_set))
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[1ec]"></a>sdio_clock_config</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, gd32f4xx_sdio.o(i.sdio_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sdio_clock_config
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[1f9]"></a>sdio_clock_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[1cb]"></a>sdio_command_index_get</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_command_index_get))
<BR><BR>[Called By]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[1e2]"></a>sdio_command_response_config</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, gd32f4xx_sdio.o(i.sdio_command_response_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = sdio_command_response_config
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_erase
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[1e4]"></a>sdio_csm_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_csm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_erase
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[1dd]"></a>sdio_data_config</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, gd32f4xx_sdio.o(i.sdio_data_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sdio_data_config
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[1e6]"></a>sdio_data_read</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_read))
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[1de]"></a>sdio_data_transfer_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_transfer_config))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[1e9]"></a>sdio_data_write</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_data_write))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[1f2]"></a>sdio_deinit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f4xx_sdio.o(i.sdio_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = sdio_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[1e0]"></a>sdio_dma_disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_disable))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1e8]"></a>sdio_dma_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dma_enable))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1df]"></a>sdio_dsm_disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_disable))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1e5]"></a>sdio_dsm_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_dsm_enable))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
</UL>

<P><STRONG><a name="[15e]"></a>sdio_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r7_error_check
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r3_error_check
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r2_error_check
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>

<P><STRONG><a name="[15d]"></a>sdio_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmdsent_error_check
</UL>

<P><STRONG><a name="[1ee]"></a>sdio_hardware_clock_disable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_hardware_clock_disable))
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[1f6]"></a>sdio_interrupt_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1e7]"></a>sdio_interrupt_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1f5]"></a>sdio_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1f4]"></a>sdio_interrupt_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_interrupts_process
</UL>

<P><STRONG><a name="[1f1]"></a>sdio_power_state_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[1f8]"></a>sdio_power_state_set</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_power_state_set))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[1cc]"></a>sdio_response_get</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_response_get))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_erase
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r6_error_check
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[1e3]"></a>sdio_wait_type_set</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_sdio.o(i.sdio_wait_type_set))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_erase
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[1c7]"></a>set_boot_count_and_save</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, log_app.o(i.set_boot_count_and_save))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = set_boot_count_and_save &rArr; write_log_config_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_config_to_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[1c9]"></a>set_log_initialized</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, log_app.o(i.set_log_initialized))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = set_log_initialized &rArr; write_log_config_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_config_to_flash
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[1a9]"></a>set_sampling_period</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, usart_app.o(i.set_sampling_period))
<BR><BR>[Stack]<UL><LI>Max Depth = 2188 + Unknown Stack Size
<LI>Call Chain = set_sampling_period &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sampling_cycle_to_flash
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_task
</UL>

<P><STRONG><a name="[1b0]"></a>smart_recovery_check</STRONG> (Thumb, 66 bytes, Stack size 648 bytes, log_app.o(i.smart_recovery_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 1108 + Unknown Stack Size
<LI>Call Chain = smart_recovery_check &rArr; f_open &rArr; dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_init
</UL>

<P><STRONG><a name="[202]"></a>spi_dma_disable</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_dma_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
</UL>

<P><STRONG><a name="[201]"></a>spi_dma_enable</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_dma_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
</UL>

<P><STRONG><a name="[1fd]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
</UL>

<P><STRONG><a name="[1ac]"></a>spi_flash_buffer_read</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_buffer_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = spi_flash_buffer_read &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sampling_cycle_to_flash
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_sampling_cycle_from_flash
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_and_display_device_id
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_pre_test_buffer_from_flash
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;load_config_from_flash_on_startup
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_config_to_flash
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_log_config_from_flash
</UL>

<P><STRONG><a name="[1dc]"></a>spi_flash_buffer_write</STRONG> (Thumb, 212 bytes, Stack size 32 bytes, gd25qxx.o(i.spi_flash_buffer_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sampling_cycle_to_flash
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_device_id_to_flash
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_pre_test_buffer_to_flash
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_config_to_flash
</UL>

<P><STRONG><a name="[130]"></a>spi_flash_init</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd25qxx.o(i.spi_flash_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = spi_flash_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gd25qxx_init
</UL>

<P><STRONG><a name="[1fc]"></a>spi_flash_page_write</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_page_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>

<P><STRONG><a name="[200]"></a>spi_flash_read_id</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_read_id))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = spi_flash_read_id &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_flash
</UL>

<P><STRONG><a name="[1db]"></a>spi_flash_sector_erase</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = spi_flash_sector_erase &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
</UL>
<BR>[Called By]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sampling_cycle_to_flash
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_device_id_to_flash
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;save_pre_test_buffer_to_flash
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_log_config_to_flash
</UL>

<P><STRONG><a name="[1fb]"></a>spi_flash_send_byte_dma</STRONG> (Thumb, 212 bytes, Stack size 56 bytes, gd25qxx.o(i.spi_flash_send_byte_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_enable
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_disable
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>

<P><STRONG><a name="[1ff]"></a>spi_flash_wait_for_write_end</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_wait_for_write_end))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = spi_flash_wait_for_write_end &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
</UL>

<P><STRONG><a name="[1fe]"></a>spi_flash_write_enable</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_write_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte_dma
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
</UL>

<P><STRONG><a name="[12f]"></a>spi_init</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gd25qxx_init
</UL>

<P><STRONG><a name="[203]"></a>start_sampling</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, usart_app.o(i.start_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 2196 + Unknown Stack Size
<LI>Call Chain = start_sampling &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_sampling_cycle_from_flash
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write_formatted
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_adc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toggle_sampling
</UL>

<P><STRONG><a name="[204]"></a>stop_sampling</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, usart_app.o(i.stop_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 2188 + Unknown Stack Size
<LI>Call Chain = stop_sampling &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close_all_files
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;log_write
</UL>
<BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toggle_sampling
</UL>

<P><STRONG><a name="[1b3]"></a>systick_config</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config
</UL>
<BR>[Calls]<UL><LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1c5]"></a>test_flash</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, usart_app.o(i.test_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 2196 + Unknown Stack Size
<LI>Call Chain = test_flash &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_read_id
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[1c6]"></a>test_tf_card</STRONG> (Thumb, 128 bytes, Stack size 64 bytes, usart_app.o(i.test_tf_card))
<BR><BR>[Stack]<UL><LI>Max Depth = 2260 + Unknown Stack Size
<LI>Call Chain = test_tf_card &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_filesystem
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_closedir
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
</UL>

<P><STRONG><a name="[1a8]"></a>toggle_sampling</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usart_app.o(i.toggle_sampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 2196 + Unknown Stack Size
<LI>Call Chain = toggle_sampling &rArr; start_sampling &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stop_sampling
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_sampling
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_task
</UL>

<P><STRONG><a name="[d]"></a>uart_task</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, usart_app.o(i.uart_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 2292 + Unknown Stack Size
<LI>Call Chain = uart_task &rArr; process_command &rArr; test_tf_card &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_command
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[1a3]"></a>update_perf_counter</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, perf_counter.o(i.update_perf_counter))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = update_perf_counter &rArr; get_system_ticks &rArr; check_systick
</UL>
<BR>[Calls]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_get_system_timer_freq
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_system_ticks
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_cycle_counter
</UL>

<P><STRONG><a name="[13f]"></a>usart_baudrate_set</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
</UL>

<P><STRONG><a name="[10e]"></a>usart_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[1bb]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[13e]"></a>usart_deinit</STRONG> (Thumb, 214 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
</UL>

<P><STRONG><a name="[142]"></a>usart_dma_receive_config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_dma_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
</UL>

<P><STRONG><a name="[143]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
</UL>

<P><STRONG><a name="[1bc]"></a>usart_flag_get</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[145]"></a>usart_interrupt_enable</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
</UL>

<P><STRONG><a name="[10d]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 50 bytes, Stack size 12 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_IRQHandler
</UL>

<P><STRONG><a name="[140]"></a>usart_receive_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
</UL>

<P><STRONG><a name="[141]"></a>usart_transmit_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_usart_init
</UL>

<P><STRONG><a name="[1d9]"></a>write_data_to_file</STRONG> (Thumb, 240 bytes, Stack size 168 bytes, sd_app.o(i.write_data_to_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 2396 + Unknown Stack Size
<LI>Call Chain = write_data_to_file &rArr; open_new_file &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_close
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;open_new_file
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_sample_to_file
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[1b6]"></a>write_device_id_to_flash</STRONG> (Thumb, 42 bytes, Stack size 48 bytes, usart_app.o(i.write_device_id_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = write_device_id_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1a0]"></a>write_log_config_to_flash</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, log_app.o(i.write_log_config_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = write_log_config_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_log_system
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_log_initialized
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_boot_count_and_save
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;increment_boot_count
</UL>

<P><STRONG><a name="[1da]"></a>write_sample_to_file</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, sd_app.o(i.write_sample_to_file))
<BR><BR>[Stack]<UL><LI>Max Depth = 2396 + Unknown Stack Size
<LI>Call Chain = write_sample_to_file &rArr; write_data_to_file &rArr; open_new_file &rArr; init_filesystem &rArr; my_printf &rArr; log_write_formatted &rArr; log_write &rArr; f_write &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_data_to_file
</UL>
<BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sampling_task
</UL>

<P><STRONG><a name="[1fa]"></a>write_sampling_cycle_to_flash</STRONG> (Thumb, 62 bytes, Stack size 32 bytes, usart_app.o(i.write_sampling_cycle_to_flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = write_sampling_cycle_to_flash &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte_dma &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_sampling_period
</UL>

<P><STRONG><a name="[ad]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[7e]"></a>_get_lc_ctype</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_ctype_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_ctype_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rt_ctype_table.o(.text)
</UL>
<P><STRONG><a name="[a8]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[241]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[242]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[8a]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_g
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_e
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[8e]"></a>_printf_fp_hex</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf2.o(x$fpl$printf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_a
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[147]"></a>check_fs</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, ff.o(i.check_fs))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = check_fs &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_mounted
</UL>

<P><STRONG><a name="[166]"></a>chk_chr</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ff.o(i.chk_chr))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[14e]"></a>chk_mounted</STRONG> (Thumb, 730 bytes, Stack size 32 bytes, ff.o(i.chk_mounted))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = chk_mounted &rArr; check_fs &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
</UL>

<P><STRONG><a name="[15f]"></a>cmp_lfn</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, ff.o(i.cmp_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = cmp_lfn
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_wtoupper
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[161]"></a>create_chain</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, ff.o(i.create_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
</UL>

<P><STRONG><a name="[164]"></a>create_name</STRONG> (Thumb, 470 bytes, Stack size 48 bytes, ff.o(i.create_name))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = create_name
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_convert
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chk_chr
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
</UL>

<P><STRONG><a name="[169]"></a>dir_find</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, ff.o(i.dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 284<LI>Call Chain = dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cmp
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmp_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[16d]"></a>dir_next</STRONG> (Thumb, 264 bytes, Stack size 24 bytes, ff.o(i.dir_next))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_chain
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[16f]"></a>dir_register</STRONG> (Thumb, 604 bytes, Stack size 80 bytes, ff.o(i.dir_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 364<LI>Call Chain = dir_register &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sum_sfn
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_cpy
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fit_lfn
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[16a]"></a>dir_sdi</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, ff.o(i.dir_sdi))
<BR><BR>[Stack]<UL><LI>Max Depth = 212<LI>Call Chain = dir_sdi &rArr; get_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clust2sect
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;follow_path
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[165]"></a>ff_convert</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ff.o(i.ff_convert))
<BR><BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_uni2oem
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ff_oem2uni
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[188]"></a>find_volume</STRONG> (Thumb, 618 bytes, Stack size 24 bytes, ff.o(i.find_volume))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = find_volume &rArr; check_fs &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_initialize
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_word
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ld_dword
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_ldnumber
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
</UL>

<P><STRONG><a name="[172]"></a>fit_lfn</STRONG> (Thumb, 104 bytes, Stack size 28 bytes, ff.o(i.fit_lfn))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = fit_lfn
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[18a]"></a>follow_path</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, ff.o(i.follow_path))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = follow_path &rArr; dir_find &rArr; dir_next &rArr; create_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_sdi
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_opendir
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[18b]"></a>get_fattime</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ff.o(i.get_fattime))
<BR><BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[187]"></a>get_ldnumber</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, ff.o(i.get_ldnumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_ldnumber
</UL>
<BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_mount
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[193]"></a>ld_dword</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, ff.o(i.ld_dword))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
</UL>

<P><STRONG><a name="[149]"></a>ld_word</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ff.o(i.ld_word))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[16c]"></a>mem_cmp</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ff.o(i.mem_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = mem_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[170]"></a>mem_cpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ff.o(i.mem_cpy))
<BR><BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gen_numname
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
</UL>

<P><STRONG><a name="[167]"></a>mem_set</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ff.o(i.mem_set))
<BR><BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;create_name
</UL>

<P><STRONG><a name="[148]"></a>move_window</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, ff.o(i.move_window))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_read
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_getfree
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sync
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_volume
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_next
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_fs
</UL>

<P><STRONG><a name="[18c]"></a>remove_chain</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, ff.o(i.remove_chain))
<BR><BR>[Stack]<UL><LI>Max Depth = 228<LI>Call Chain = remove_chain &rArr; put_fat &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;put_fat
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fat
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_open
</UL>

<P><STRONG><a name="[16b]"></a>sum_sfn</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ff.o(i.sum_sfn))
<BR><BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_register
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dir_find
</UL>

<P><STRONG><a name="[18f]"></a>sync</STRONG> (Thumb, 190 bytes, Stack size 24 bytes, ff.o(i.sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = sync &rArr; move_window &rArr; disk_write &rArr; sd_multiblocks_write &rArr; dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;move_window
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mem_set
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_write
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_ioctl
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[18e]"></a>validate</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, ff.o(i.validate))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = validate
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disk_status
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_write
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f_sync
</UL>

<P><STRONG><a name="[eb]"></a>I2C_Bus_Reset</STRONG> (Thumb, 246 bytes, Stack size 16 bytes, oled.o(i.I2C_Bus_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = I2C_Bus_Reset &rArr; i2c_clock_config &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_mode_addr_config
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_enable
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_clock_config
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_ack_config
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_deinit
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_data
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_cmd
</UL>

<P><STRONG><a name="[15c]"></a>cmdsent_error_check</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, sdio_sdcard.o(i.cmdsent_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = cmdsent_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[181]"></a>dma_receive_config</STRONG> (Thumb, 162 bytes, Stack size 64 bytes, sdio_sdcard.o(i.dma_receive_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = dma_receive_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[184]"></a>dma_transfer_config</STRONG> (Thumb, 164 bytes, Stack size 64 bytes, sdio_sdcard.o(i.dma_transfer_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = dma_transfer_config &rArr; dma_multi_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_multi_data_mode_init
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flow_controller_config
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[19b]"></a>gpio_config</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, sdio_sdcard.o(i.gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = gpio_config &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[1ca]"></a>r1_error_check</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, sdio_sdcard.o(i.r1_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = r1_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_erase
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_cardstatus_get
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_select_deselect
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_transfer_stop
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[1cd]"></a>r1_error_type_check</STRONG> (Thumb, 140 bytes, Stack size 0 bytes, sdio_sdcard.o(i.r1_error_type_check))
<BR><BR>[Called By]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_state_get
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>

<P><STRONG><a name="[1ce]"></a>r2_error_check</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, sdio_sdcard.o(i.r2_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = r2_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[1cf]"></a>r3_error_check</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, sdio_sdcard.o(i.r3_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = r3_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[1d0]"></a>r6_error_check</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, sdio_sdcard.o(i.r6_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = r6_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_card_init
</UL>

<P><STRONG><a name="[1d1]"></a>r7_error_check</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, sdio_sdcard.o(i.r7_error_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = r7_error_check
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_power_on
</UL>

<P><STRONG><a name="[1d2]"></a>rcu_config</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, sdio_sdcard.o(i.rcu_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rcu_config
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_init
</UL>

<P><STRONG><a name="[1eb]"></a>sd_bus_width_config</STRONG> (Thumb, 200 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_bus_width_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sd_bus_width_config &rArr; sd_scr_get &rArr; sdio_data_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_scr_get
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_mode_config
</UL>

<P><STRONG><a name="[1ea]"></a>sd_card_state_get</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, sdio_sdcard.o(i.sd_card_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = sd_card_state_get &rArr; sdio_command_response_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_response_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_index_get
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_type_check
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_erase
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
</UL>

<P><STRONG><a name="[1e1]"></a>sd_datablocksize_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sdio_sdcard.o(i.sd_datablocksize_get))
<BR><BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_multiblocks_read
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_write
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_block_read
</UL>

<P><STRONG><a name="[1ef]"></a>sd_scr_get</STRONG> (Thumb, 334 bytes, Stack size 40 bytes, sdio_sdcard.o(i.sd_scr_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sd_scr_get &rArr; sdio_data_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_wait_type_set
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_get
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_flag_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_dsm_enable
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_transfer_config
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_read
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_data_config
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_csm_enable
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sdio_command_response_config
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;r1_error_check
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sd_bus_width_config
</UL>

<P><STRONG><a name="[10b]"></a>_soft_delay_</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, system_gd32f4xx.o(i._soft_delay_))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _soft_delay_
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[205]"></a>system_clock_240m_25m_hxtal</STRONG> (Thumb, 170 bytes, Stack size 12 bytes, system_gd32f4xx.o(i.system_clock_240m_25m_hxtal))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[10c]"></a>system_clock_config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = system_clock_config &rArr; system_clock_240m_25m_hxtal
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[206]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, systick.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[14a]"></a>check_systick</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, perf_counter.o(i.check_systick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = check_systick
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_is_system_timer_ovf_pending
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_get_system_timer_top
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;perfc_port_get_system_timer_elapsed
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_system_ms
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_system_ticks
</UL>

<P><STRONG><a name="[7b]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL>
<P><STRONG><a name="[7c]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[d9]"></a>_fp_digits</STRONG> (Thumb, 432 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
