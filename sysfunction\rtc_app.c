
#include "cmic_gd32f470vet6.h"

extern rtc_parameter_struct rtc_initpara;
extern uint32_t prescaler_a, prescaler_s;

/**
 * @brief 将十进制转换为BCD格式
 *
 * @param decimal 要转换的十进制值
 * @return uint8_t BCD值
 */
uint8_t decimal_to_bcd(uint8_t decimal)
{
    return ((decimal / 10) << 4) | (decimal % 10);
}

/**
 * @brief 将BCD转换为十进制格式
 *
 * @param bcd 要转换的BCD值
 * @return uint8_t 十进制值
 */
uint8_t bcd_to_decimal(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

/**
 * @brief 使用指定时间配置RTC
 *
 * @param year 年份（2000-2099）
 * @param month 月份（1-12）
 * @param day 日期（1-31）
 * @param hour 小时（0-23）
 * @param minute 分钟（0-59）
 * @param second 秒（0-59）
 * @return uint8_t 成功返回1，失败返回0
 */
uint8_t rtc_set_time(uint16_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t minute, uint8_t second)
{
    // 验证输入参数
    if(year < 2000 || year > 2099 || month < 1 || month > 12 ||
       day < 1 || day > 31 || hour > 23 || minute > 59 || second > 59)
    {
        return 0;
    }

    // 将年份转换为2位BCD（年份 - 2000）
    uint8_t year_bcd = decimal_to_bcd(year - 2000);

    // 设置RTC参数
    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    rtc_initpara.year = year_bcd;
    rtc_initpara.day_of_week = RTC_MONDAY; // 默认为星期一
    rtc_initpara.month = decimal_to_bcd(month);
    rtc_initpara.date = decimal_to_bcd(day);
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;

    // 设置时间
    rtc_initpara.hour = decimal_to_bcd(hour);
    rtc_initpara.minute = decimal_to_bcd(minute);
    rtc_initpara.second = decimal_to_bcd(second);

    // 配置RTC
    if(ERROR == rtc_init(&rtc_initpara))
    {
        return 0;
    }
    else
    {
        RTC_BKP0 = BKP_VALUE;
        return 1;
    }
}

/**
 * @brief 获取当前RTC时间并格式化为字符串
 *
 * @param time_str 存储格式化时间字符串的缓冲区
 * @param buffer_size 缓冲区大小
 */
void rtc_get_time_string(char* time_str, size_t buffer_size)
{
    // 从RTC获取当前时间
    rtc_current_time_get(&rtc_initpara);

    // 将BCD转换为十进制并格式化
    uint16_t year = 2000 + bcd_to_decimal(rtc_initpara.year);
    uint8_t month = bcd_to_decimal(rtc_initpara.month);
    uint8_t day = bcd_to_decimal(rtc_initpara.date);
    uint8_t hour = bcd_to_decimal(rtc_initpara.hour);
    uint8_t minute = bcd_to_decimal(rtc_initpara.minute);
    uint8_t second = bcd_to_decimal(rtc_initpara.second);

    snprintf(time_str, buffer_size, "%04d-%02d-%02d %02d:%02d:%02d",
             year, month, day, hour, minute, second);
}

/*!
    \brief      RTC任务 - 在OLED上显示当前时间
    \param[in]  无
    \param[out] 无
    \retval     无
*/
void rtc_task(void)
{
    rtc_current_time_get(&rtc_initpara);
}
