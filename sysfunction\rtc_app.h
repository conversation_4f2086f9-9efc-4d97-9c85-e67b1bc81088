/* 许可证
* 公司: MCUSTUDIO
* 作者: <PERSON><PERSON><PERSON><PERSON>.
* 版本: V0.10
* 时间: 2025/06/05
* 说明: RTC应用程序头文件
*/
#ifndef __RTC_APP_H_
#define __RTC_APP_H_

#include "stdint.h"
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// 函数声明
uint8_t decimal_to_bcd(uint8_t decimal);
uint8_t bcd_to_decimal(uint8_t bcd);
uint8_t rtc_set_time(uint16_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t minute, uint8_t second);
void rtc_get_time_string(char* time_str, size_t buffer_size);
void rtc_task(void);

#ifdef __cplusplus
}
#endif

#endif /* __RTC_APP_H_ */
