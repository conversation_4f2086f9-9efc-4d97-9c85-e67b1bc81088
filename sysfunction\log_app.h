#ifndef __LOG_APP_H_
#define __LOG_APP_H_

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// 日志系统初始化和管理
void log_init(void);
void log_write(const char* message);
void log_write_raw(const char* message);
void log_write_formatted(const char* format, ...);
uint8_t log_start_session(void);  // 开始日志会话（由test或自动挂载调用）
void log_close(void);
void log_periodic_sync(void);  // 数据完整性的周期性同步

// 日志系统状态
uint8_t is_log_initialized(void);
uint8_t set_log_initialized(void);
uint8_t is_log_file_open(void);
uint32_t get_boot_count(void);
uint8_t set_boot_count_and_save(uint32_t count);
uint8_t increment_boot_count(void);
uint8_t reset_log_system(void);
uint8_t smart_recovery_check(void);

// 手动log文件管理
uint8_t close_log_file_by_number(uint32_t log_number);
uint32_t get_current_log_number(void);

#ifdef __cplusplus
}
#endif

#endif /* __LOG_APP_H_ */
