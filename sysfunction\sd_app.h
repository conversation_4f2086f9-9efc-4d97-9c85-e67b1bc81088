#ifndef __SD_APP_H_
#define __SD_APP_H_

#include "stdint.h"

// 存储模式定义
typedef enum {
    STORAGE_MODE_SAMPLE = 0,
    STORAGE_MODE_HIDE = 1,
    STORAGE_MODE_OVERLIMIT = 2
} storage_mode_t;

void sd_fatfs_init(void);

// 文件系统管理函数
uint8_t init_filesystem(void);
void deinit_filesystem(void);

// 多模式数据存储函数
void generate_filename(char* filename, uint16_t buffer_size, storage_mode_t mode);
uint8_t open_new_file(storage_mode_t mode);
uint8_t write_data_to_file(char* time_str, char* voltage_str, storage_mode_t mode);
void close_all_files(void);

// 传统采样数据存储函数（向后兼容）
void generate_sample_filename(char* filename, uint16_t buffer_size);
uint8_t open_new_sample_file(void);
uint8_t write_sample_to_file(char* time_str, char* voltage_str);
void close_sample_file(void);

#endif /* __SD_APP_H_ */
