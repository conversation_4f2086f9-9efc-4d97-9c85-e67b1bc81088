
#include "cmic_gd32f470vet6.h"
#include "usart_app.h"

extern uint16_t adc_value[1];

/**
 * @brief 在OLED上使用printf格式显示字符串
 *
 * 在OLED屏幕上显示6x8小ASCII字符
 * @param x X轴上的字符位置，范围：0 - 127
 * @param y Y轴上的字符位置，范围：0 - 3
 *
 * 示例：oled_printf(0, 0, "Data = %d", dat);
 */
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 格式化字符串的临时存储
  va_list arg;      // 可变参数列表
  int len;          // 字符串长度

  va_start(arg, format);
  // 安全地将字符串格式化到缓冲区
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

/**
 * @brief OLED显示任务函数
 *
 * 根据采样状态显示系统信息：
 * - 如果采样：当前时间（每500ms）和电压（跟随采样周期）
 * - 如果空闲：系统空闲状态
 * 由调度器每500ms运行一次
 */
void oled_task(void)
{
    static uint8_t last_mode = 255;  // 跟踪模式变化（255=初始，0=空闲，1=采样）
    uint8_t current_mode = get_sampling_status() ? 1 : 0;

    // 模式改变时清屏
    if(last_mode != current_mode)
    {
        oled_printf(0, 0, "            ");  // 清除第0行（12个空格）
        oled_printf(0, 1, "            ");  // 清除第1行（12个空格）
        last_mode = current_mode;
    }

    if(get_sampling_status())
    {
        // 采样模式：显示时间和电压
        extern rtc_parameter_struct rtc_initpara;
        extern uint16_t adc_value[1];

        // 电压显示控制的静态变量
        static uint32_t voltage_update_counter = 0;
        static uint32_t last_displayed_voltage_mv = 0;

        // 获取当前时间（总是每500ms更新）
        rtc_current_time_get(&rtc_initpara);
        uint8_t hour = ((rtc_initpara.hour >> 4) * 10) + (rtc_initpara.hour & 0x0F);
        uint8_t minute = ((rtc_initpara.minute >> 4) * 10) + (rtc_initpara.minute & 0x0F);
        uint8_t second = ((rtc_initpara.second >> 4) * 10) + (rtc_initpara.second & 0x0F);

        oled_printf(0, 0, "%02d:%02d:%02d", hour, minute, second);

        // 电压显示跟随采样周期
        extern uint32_t get_sampling_period(void);
        uint32_t sampling_period_500ms = get_sampling_period() / 5;  // 将100ms单位转换为500ms单位

        voltage_update_counter++;
        if(voltage_update_counter >= sampling_period_500ms)
        {
            voltage_update_counter = 0;

            // 计算并更新电压显示
            uint64_t temp_calc = (uint64_t)adc_value[0] * current_ratio * 3300;
            last_displayed_voltage_mv = temp_calc / (4096 * 100);  // 以毫伏计算
        }

        // 在第1行显示电压（使用最后计算的值），用填充清除之前的内容
        oled_printf(0, 1, "%lu.%02luV      ", last_displayed_voltage_mv/1000, (last_displayed_voltage_mv%1000)/10);
    }
    else
    {
        // 空闲模式：显示系统状态
        oled_printf(0, 0, "system idle");
        oled_printf(0, 1, "");  // 清除第1行

        // 不采样时重置电压更新计数器
        static uint32_t voltage_update_counter = 0;
        voltage_update_counter = 0;
    }
}

/* CUSTOM EDIT */
