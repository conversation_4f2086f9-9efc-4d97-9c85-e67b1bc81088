#include "cmic_gd32f470vet6.h"
#include <string.h>
#include "usart_app.h"
#include "sd_app.h"
#include "ff.h"
#include "diskio.h"
#include <stdio.h>

// FATFS文件系统相关变量定义
FATFS fs;           // FATFS文件系统对象
FIL fdst;           // 文件对象
uint16_t i = 0, count, result = 0;  // 循环计数器和结果变量
UINT br, bw;        // 读取和写入的字节数

sd_card_info_struct sd_cardinfo;    // SD卡信息结构体

// 确保缓冲区4字节对齐，用于DMA传输
__attribute__((aligned(4))) BYTE buffer[128];       // 读取数据缓冲区
__attribute__((aligned(4))) BYTE filebuffer[128];   // 写入数据缓冲区

// 多模式数据存储变量
static FIL storage_files[3];          // 采样、隐藏、超限文件
static uint8_t files_open[3] = {0, 0, 0};         // 文件打开标志
static uint8_t data_counts[3] = {0, 0, 0};        // 当前文件中的数据计数
static char current_filenames[3][64];             // 当前文件名

// 每种模式的文件夹和文件前缀
static const char* folder_names[3] = {"sample", "hideData", "overLimit"};
static const char* file_prefixes[3] = {"sampleData", "hideData", "overLimit"};

// 全局文件系统状态
static FATFS global_fs;               // 全局文件系统对象
static uint8_t fs_mounted = 0;        // 文件系统挂载状态

/**
 * @brief       内存比较函数
 * @param       src: 源数据指针
 * @param       dst: 目标数据指针  
 * @param       length: 比较长度
 * @retval      SUCCESS: 比较成功，数据相同
 *              ERROR: 比较失败，数据不同
 */
ErrStatus memory_compare(uint8_t* src, uint8_t* dst, uint16_t length) 
{
    while(length --){
        if(*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

/**
 * @brief       SD卡FATFS初始化函数
 * @param       无
 * @retval      无
 */
void sd_fatfs_init(void)
{
    nvic_irq_enable(SDIO_IRQn, 2, 0);					//SDIO使能中断，优先级设为2
}

/**
 * @brief       通过串口打印SD卡详细信息
 * @param       无
 * @retval      无
 */
void card_info_get(void)
{
    sd_card_info_struct sd_cardinfo;      // SD卡信息结构体
    sd_error_enum status;                 // SD卡操作状态
    uint32_t block_count, block_size;     // 块数量和块大小
    
    // 获取SD卡信息
    status = sd_card_information_get(&sd_cardinfo);
    
    if(SD_OK == status)
    {
        my_printf(DEBUG_USART, "\r\n*** SD Card Info ***\r\n");
        
        // 打印卡类型
        switch(sd_cardinfo.card_type)
        {
            case SDIO_STD_CAPACITY_SD_CARD_V1_1:
                my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V1.1\r\n");
                break;
            case SDIO_STD_CAPACITY_SD_CARD_V2_0:
                my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V2.0\r\n");
                break;
            case SDIO_HIGH_CAPACITY_SD_CARD:
                my_printf(DEBUG_USART, "Card Type: High Capacity SD Card\r\n");
                break;
            case SDIO_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: Multimedia Card\r\n");
                break;
            case SDIO_HIGH_CAPACITY_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: High Capacity Multimedia Card\r\n");
                break;
            case SDIO_HIGH_SPEED_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: High Speed Multimedia Card\r\n");
                break;
            default:
                my_printf(DEBUG_USART, "Card Type: Unknown\r\n");
                break;
        }
        
        // 打印容量信息和块大小
        block_count = (sd_cardinfo.card_csd.c_size + 1) * 1024;  // 计算块数量
        block_size = 512;                                        // 固定块大小512字节
        my_printf(DEBUG_USART,"\r\n## Device size is %dKB (%.2fGB)##", sd_card_capacity_get(), sd_card_capacity_get() / 1024.0f / 1024.0f);
        my_printf(DEBUG_USART,"\r\n## Block size is %dB ##", block_size);
        my_printf(DEBUG_USART,"\r\n## Block count is %d ##", block_count);
        
        // 打印制造商ID和产品应用ID
        my_printf(DEBUG_USART, "Manufacturer ID: 0x%X\r\n", sd_cardinfo.card_cid.mid);
        my_printf(DEBUG_USART, "OEM/Application ID: 0x%X\r\n", sd_cardinfo.card_cid.oid);
        
        // 打印产品名称 (PNM) - 从32位数据中提取5个字符
        uint8_t pnm[6];
        pnm[0] = (sd_cardinfo.card_cid.pnm0 >> 24) & 0xFF;
        pnm[1] = (sd_cardinfo.card_cid.pnm0 >> 16) & 0xFF;
        pnm[2] = (sd_cardinfo.card_cid.pnm0 >> 8) & 0xFF;
        pnm[3] = sd_cardinfo.card_cid.pnm0 & 0xFF;
        pnm[4] = sd_cardinfo.card_cid.pnm1 & 0xFF;
        pnm[5] = '\0';  // 字符串结束符
        my_printf(DEBUG_USART, "Product Name: %s\r\n", pnm);
        
        // 打印产品版本和序列号
        my_printf(DEBUG_USART, "Product Revision: %d.%d\r\n", (sd_cardinfo.card_cid.prv >> 4) & 0x0F, sd_cardinfo.card_cid.prv & 0x0F);
        // 序列号以16进制格式显示，便于复制
        my_printf(DEBUG_USART, "Product Serial Number: 0x%08X\r\n", sd_cardinfo.card_cid.psn);
        
        // 打印CSD版本，输出CSD信息
        my_printf(DEBUG_USART, "CSD Version: %d.0\r\n", sd_cardinfo.card_csd.csd_struct + 1);
        
    }
    else
    {
        my_printf(DEBUG_USART, "\r\nFailed to get SD card information, error code: %d\r\n", status);
    }
}

// sd_fatfs_test函数已移除 - 会导致文件系统冲突

/**
 * @brief 初始化并挂载文件系统（启动时调用一次）
 *
 * @return uint8_t 成功返回1，失败返回0
 */
uint8_t init_filesystem(void)
{
    FRESULT result;

    if(fs_mounted)
    {
        return 1;  // 已经挂载
    }

    // 启动时重置文件状态以防止上次会话的问题
    for(int i = 0; i < 3; i++) {
        files_open[i] = 0;
        data_counts[i] = 0;
    }

    // 初始化SD卡
    DSTATUS disk_status = disk_initialize(0);
    if(disk_status != 0)
    {
        return 0;
    }

    // 挂载文件系统
    result = f_mount(&global_fs, "0:", 1);
    if(result != FR_OK)
    {
        my_printf(DEBUG_USART, "文件系统挂载失败，错误：%d\r\n", result);

        // 提供详细的错误信息和调试代码
        switch(result)
        {
            case FR_NO_FILESYSTEM:
                my_printf(DEBUG_USART, "错误：TF卡上未找到有效的FAT文件系统\r\n");
                my_printf(DEBUG_USART, "请使用电脑将TF卡格式化为FAT32\r\n");
                break;
            case FR_NOT_READY:
                my_printf(DEBUG_USART, "错误：TF卡未就绪（调试：簇大小检查失败）\r\n");
                break;
            case FR_DISK_ERR:
                my_printf(DEBUG_USART, "错误：TF卡硬件错误（调试：扇区大小检查失败）\r\n");
                break;
            case FR_INT_ERR:
                my_printf(DEBUG_USART, "错误：内部错误（调试：FAT计数检查失败）\r\n");
                break;
            case FR_WRITE_PROTECTED:
                my_printf(DEBUG_USART, "错误：写保护（调试：保留扇区检查失败）\r\n");
                break;
            default:
                my_printf(DEBUG_USART, "Error: Unknown filesystem error (code: %d)\r\n", result);
                break;
        }
        return 0;
    }

    fs_mounted = 1;
    return 1;
}

/**
 * @brief 测试TF卡原始扇区读取
 */
void test_tf_card_raw_read(void)
{
    BYTE buffer[512];
    DRESULT res;

    my_printf(DEBUG_USART, "Testing TF card raw sector read...\r\n");

    // 读取扇区0（MBR/引导扇区）
    res = disk_read(0, buffer, 0, 1);
    if(res == RES_OK)
    {
        my_printf(DEBUG_USART, "Sector 0 read successful\r\n");

        // Check boot signature
        if(buffer[510] == 0x55 && buffer[511] == 0xAA)
        {
            my_printf(DEBUG_USART, "Boot signature found (0x55AA)\r\n");

            // Check if it's MBR or VBR
            if(buffer[0] == 0xEB || buffer[0] == 0xE9)
            {
                my_printf(DEBUG_USART, "Looks like VBR (Volume Boot Record)\r\n");
                // Check filesystem type
                if(buffer[82] == 'F' && buffer[83] == 'A' && buffer[84] == 'T')
                {
                    my_printf(DEBUG_USART, "FAT filesystem detected\r\n");
                }
            }
            else
            {
                my_printf(DEBUG_USART, "Looks like MBR (Master Boot Record)\r\n");
                // Check partition table
                if(buffer[446] != 0)
                {
                    my_printf(DEBUG_USART, "Partition table found\r\n");
                    my_printf(DEBUG_USART, "Partition 1 type: 0x%02X\r\n", buffer[450]);
                }
            }
        }
        else
        {
            my_printf(DEBUG_USART, "No boot signature found\r\n");
        }

        // Print first 16 bytes for debugging
        my_printf(DEBUG_USART, "First 16 bytes: ");
        for(int i = 0; i < 16; i++)
        {
            my_printf(DEBUG_USART, "%02X ", buffer[i]);
        }
        my_printf(DEBUG_USART, "\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "Sector 0 read failed, error: %d\r\n", res);
    }
}

/**
 * @brief 将TF卡格式化为FAT32（请谨慎使用！）
 */
uint8_t format_tf_card(void)
{
    FRESULT res;
    BYTE work[FF_MAX_SS]; /* 工作区域（越大处理时间越好） */

    my_printf(DEBUG_USART, "警告：这将擦除TF卡上的所有数据！\r\n");
    my_printf(DEBUG_USART, "正在将TF卡格式化为FAT32...\r\n");

    /* 创建FAT32卷 */
    res = f_mkfs("0:", 0, work, sizeof(work));

    if(res == FR_OK)
    {
        my_printf(DEBUG_USART, "TF卡格式化成功\r\n");

        /* 尝试挂载新格式化的文件系统 */
        if(init_filesystem())
        {
            my_printf(DEBUG_USART, "格式化的文件系统挂载成功\r\n");
            return 1;
        }
        else
        {
            my_printf(DEBUG_USART, "挂载格式化的文件系统失败\r\n");
            return 0;
        }
    }
    else
    {
        my_printf(DEBUG_USART, "TF卡格式化失败，错误：%d\r\n", res);
        return 0;
    }
}

/**
 * @brief 卸载文件系统（完成时调用）
 */
void deinit_filesystem(void)
{
    if(fs_mounted)
    {
        f_mount(NULL, "0:", 0);
        fs_mounted = 0;
        my_printf(DEBUG_USART, "文件系统已卸载\r\n");
    }
}

/**
 * @brief 根据当前时间和存储模式生成文件名
 *
 * @param filename 存储生成文件名的缓冲区
 * @param buffer_size 文件名缓冲区大小
 * @param mode 存储模式（采样、隐藏、超限）
 */
void generate_filename(char* filename, uint16_t buffer_size, storage_mode_t mode)
{
    extern rtc_parameter_struct rtc_initpara;
    rtc_current_time_get(&rtc_initpara);

    // 将BCD转换为十进制
    uint8_t year = ((rtc_initpara.year >> 4) * 10) + (rtc_initpara.year & 0x0F);
    uint8_t month = ((rtc_initpara.month >> 4) * 10) + (rtc_initpara.month & 0x0F);
    uint8_t date = ((rtc_initpara.date >> 4) * 10) + (rtc_initpara.date & 0x0F);
    uint8_t hour = ((rtc_initpara.hour >> 4) * 10) + (rtc_initpara.hour & 0x0F);
    uint8_t minute = ((rtc_initpara.minute >> 4) * 10) + (rtc_initpara.minute & 0x0F);
    uint8_t second = ((rtc_initpara.second >> 4) * 10) + (rtc_initpara.second & 0x0F);

    // 根据模式生成文件名
    // 格式：文件夹/前缀YYYYMMDDHHMMSS.txt
    snprintf(filename, buffer_size, "0:/%s/%s%04d%02d%02d%02d%02d%02d.txt",
             folder_names[mode], file_prefixes[mode],
             2000 + year, month, date, hour, minute, second);
}

/**
 * @brief 生成采样文件名（向后兼容）
 */
void generate_sample_filename(char* filename, uint16_t buffer_size)
{
    generate_filename(filename, buffer_size, STORAGE_MODE_SAMPLE);
}

/**
 * @brief 打开新的采样文件进行写入
 *
 * @return uint8_t 成功返回1，失败返回0
 */
uint8_t open_new_sample_file(void)
{
    FRESULT result;

    // 强制关闭任何打开的文件并重置状态
    if(files_open[STORAGE_MODE_SAMPLE])
    {
        f_sync(&storage_files[STORAGE_MODE_SAMPLE]);  // 确保数据已写入
        f_close(&storage_files[STORAGE_MODE_SAMPLE]);
        files_open[STORAGE_MODE_SAMPLE] = 0;
        data_counts[STORAGE_MODE_SAMPLE] = 0;  // 重置计数器
    }

    // 额外安全措施：确保文件打开状态正确
    files_open[STORAGE_MODE_SAMPLE] = 0;
    data_counts[STORAGE_MODE_SAMPLE] = 0;

    // 确保文件系统已挂载
    if(!init_filesystem())
    {
        my_printf(DEBUG_USART, "严重错误：文件系统初始化失败\r\n");
        return 0;
    }

    // 测试基本文件系统操作
    DWORD free_clusters, total_clusters;
    FATFS* fs_ptr;
    FRESULT fs_status = f_getfree("0:/", &free_clusters, &fs_ptr);
    if(fs_status != FR_OK)
    {
        my_printf(DEBUG_USART, "严重错误：文件系统状态检查失败：结果=%d\r\n", fs_status);
        return 0;
    }
    total_clusters = (fs_ptr->n_fatent - 2);  // 使用变量以避免警告

    // 测试文件系统访问
    DIR test_dir;
    FRESULT test_result = f_opendir(&test_dir, "0:/");
    if(test_result != FR_OK)
    {
        my_printf(DEBUG_USART, "根目录访问失败：结果=%d\r\n", test_result);
        return 0;
    }
    // 注意：此FATFS版本中f_closedir不可用，目录对象自动释放

    // 检查采样目录并计算文件数
    DIR sample_dir;
    FILINFO file_info;
    uint16_t file_count = 0;
    FRESULT dir_result = f_opendir(&sample_dir, "0:/sample");
    if(dir_result == FR_OK)
    {
        while(f_readdir(&sample_dir, &file_info) == FR_OK && file_info.fname[0] != 0)
        {
            file_count++;
        }
        // 目录已检查
    }
    else
    {
        my_printf(DEBUG_USART, "Failed to open sample directory: result=%d\r\n", dir_result);
    }

    // 生成新文件名（长文件名格式）
    generate_sample_filename(current_filenames[STORAGE_MODE_SAMPLE], sizeof(current_filenames[STORAGE_MODE_SAMPLE]));

    // 首先尝试使用长文件名创建并打开文件
    result = f_open(&storage_files[STORAGE_MODE_SAMPLE], current_filenames[STORAGE_MODE_SAMPLE], FA_CREATE_ALWAYS | FA_WRITE);

    if(result == FR_OK)
    {
        files_open[STORAGE_MODE_SAMPLE] = 1;
        data_counts[STORAGE_MODE_SAMPLE] = 0;
        // 强制同步以确保文件写入TF卡
        f_sync(&storage_files[STORAGE_MODE_SAMPLE]);
        return 1;
    }
    else
    {
        // 长文件名失败，尝试替代方案

        // 首先尝试简单的短文件名，看看问题是否特定于长文件名
        extern rtc_parameter_struct rtc_initpara;
        rtc_current_time_get(&rtc_initpara);
        uint8_t sec_val = ((rtc_initpara.second >> 4) * 10) + (rtc_initpara.second & 0x0F);

        snprintf(current_filenames[STORAGE_MODE_SAMPLE], sizeof(current_filenames[STORAGE_MODE_SAMPLE]), "0:/sample/T%02d.TXT", sec_val);
        result = f_open(&storage_files[STORAGE_MODE_SAMPLE], current_filenames[STORAGE_MODE_SAMPLE], FA_CREATE_ALWAYS | FA_WRITE);

        if(result == FR_OK)
        {
            files_open[STORAGE_MODE_SAMPLE] = 1;
            data_counts[STORAGE_MODE_SAMPLE] = 0;
            f_sync(&storage_files[STORAGE_MODE_SAMPLE]);
            return 1;
        }

        // 尝试非常短的长文件名

        // 获取当前时间用于文件名
        extern rtc_parameter_struct rtc_initpara;
        rtc_current_time_get(&rtc_initpara);
        uint8_t hour = ((rtc_initpara.hour >> 4) * 10) + (rtc_initpara.hour & 0x0F);
        uint8_t minute = ((rtc_initpara.minute >> 4) * 10) + (rtc_initpara.minute & 0x0F);
        uint8_t second = ((rtc_initpara.second >> 4) * 10) + (rtc_initpara.second & 0x0F);

        // 尝试非常短的长文件名（12字符，只需要1个LFN条目+1个SFN条目）
        snprintf(current_filenames[STORAGE_MODE_SAMPLE], sizeof(current_filenames[STORAGE_MODE_SAMPLE]), "0:/sample/d%02d%02d%02d.txt",
                 hour, minute, second);

        // 尝试非常短的长文件名
        result = f_open(&storage_files[STORAGE_MODE_SAMPLE], current_filenames[STORAGE_MODE_SAMPLE], FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK)
        {
            files_open[STORAGE_MODE_SAMPLE] = 1;
            data_counts[STORAGE_MODE_SAMPLE] = 0;
            // 非常短的长文件名创建成功

            // 强制同步以确保文件写入TF卡
            f_sync(&storage_files[STORAGE_MODE_SAMPLE]);
            return 1;
        }

        // 如果长文件名失败则回退到短文件名
        snprintf(current_filenames[STORAGE_MODE_SAMPLE], sizeof(current_filenames[STORAGE_MODE_SAMPLE]), "0:/sample/S%02d%02d%02d.TXT",
                 hour, minute, second);

        result = f_open(&storage_files[STORAGE_MODE_SAMPLE], current_filenames[STORAGE_MODE_SAMPLE], FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK)
        {
            files_open[STORAGE_MODE_SAMPLE] = 1;
            data_counts[STORAGE_MODE_SAMPLE] = 0;
            f_sync(&storage_files[STORAGE_MODE_SAMPLE]);
            return 1;
        }

        return 0;
    }
}

/**
 * @brief 将采样数据写入文件（向后兼容的传统函数）
 *
 * @param time_str 时间字符串
 * @param voltage_str 电压字符串
 * @return uint8_t 成功返回1，失败返回0
 */
uint8_t write_sample_to_file(char* time_str, char* voltage_str)
{
    return write_data_to_file(time_str, voltage_str, STORAGE_MODE_SAMPLE);
}

/**
 * @brief 关闭当前采样文件（强制关闭并同步）
 */
void close_sample_file(void)
{
    if(files_open[STORAGE_MODE_SAMPLE])
    {
        // 关闭前强制同步以确保所有数据已写入
        f_sync(&storage_files[STORAGE_MODE_SAMPLE]);
        f_close(&storage_files[STORAGE_MODE_SAMPLE]);
        files_open[STORAGE_MODE_SAMPLE] = 0;
        data_counts[STORAGE_MODE_SAMPLE] = 0;
    }
}

/**
 * @brief 为指定存储模式打开新文件
 *
 * @param mode 存储模式（采样、隐藏、超限）
 * @return uint8_t 成功返回1，失败返回0
 */
uint8_t open_new_file(storage_mode_t mode)
{
    FRESULT result;

    // 强制关闭此模式的任何打开文件
    if(files_open[mode])
    {
        f_sync(&storage_files[mode]);
        f_close(&storage_files[mode]);
        files_open[mode] = 0;
        data_counts[mode] = 0;
    }

    // 确保文件系统已挂载
    if(!init_filesystem())
    {
        return 0;
    }

    // 生成新文件名
    generate_filename(current_filenames[mode], sizeof(current_filenames[mode]), mode);

    // 尝试创建并打开文件
    result = f_open(&storage_files[mode], current_filenames[mode], FA_CREATE_ALWAYS | FA_WRITE);

    if(result == FR_OK)
    {
        files_open[mode] = 1;
        data_counts[mode] = 0;
        f_sync(&storage_files[mode]);
        return 1;
    }

    return 0;
}

/**
 * @brief 为指定存储模式将数据写入文件
 *
 * @param time_str 时间字符串
 * @param voltage_str 电压字符串
 * @param mode 存储模式（采样、隐藏、超限）
 * @return uint8_t 成功返回1，失败返回0
 */
uint8_t write_data_to_file(char* time_str, char* voltage_str, storage_mode_t mode)
{
    // 检查是否需要打开新文件
    if(!files_open[mode] || data_counts[mode] >= 10)
    {
        if(!open_new_file(mode))
        {
            return 0;  // 打开新文件失败
        }
    }

    UINT bytes_written;
    char line_buffer[128];

    // 格式化行："2025-01-01 00:30:10 1.50V \r\n"
    snprintf(line_buffer, sizeof(line_buffer), "%s %s \r\n", time_str, voltage_str);

    // 使用重试机制写入文件
    FRESULT result;
    int retry_count = 0;
    const int max_retries = 3;

    do {
        result = f_write(&storage_files[mode], line_buffer, strlen(line_buffer), &bytes_written);
        if(result == FR_OK && bytes_written == strlen(line_buffer)) {
            break;  // 成功
        }

        retry_count++;
        if(retry_count < max_retries) {
            // 重试前短暂延迟
            for(volatile int i = 0; i < 10000; i++);
        }
    } while(retry_count < max_retries);

    if(result == FR_OK && bytes_written == strlen(line_buffer))
    {
        data_counts[mode]++;

        // 每次写入后强制同步
        f_sync(&storage_files[mode]);

        // 检查文件是否已满（10个数据条目）
        if(data_counts[mode] >= 10)
        {
            f_sync(&storage_files[mode]);
            f_close(&storage_files[mode]);
            files_open[mode] = 0;
            data_counts[mode] = 0;
        }

        return 1;
    }
    else
    {
        // 写入错误时关闭文件
        if(files_open[mode])
        {
            f_close(&storage_files[mode]);
            files_open[mode] = 0;
            data_counts[mode] = 0;
        }
        return 0;
    }
}

/**
 * @brief 关闭所有打开的文件
 */
void close_all_files(void)
{
    for(int i = 0; i < 3; i++)
    {
        if(files_open[i])
        {
            f_sync(&storage_files[i]);
            f_close(&storage_files[i]);
            files_open[i] = 0;
            data_counts[i] = 0;
        }
    }
}
