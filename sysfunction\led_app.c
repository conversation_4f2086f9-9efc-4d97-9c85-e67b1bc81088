
#include "cmic_gd32f470vet6.h"

uint8_t ucLed[6] = {0,0,0,0,0,0};  // LED状态数组

/**
 * @brief 显示LED状态
 *
 * 该函数根据LED状态数组控制LED显示
 * @param ucLed LED数据数组指针
 */
void led_disp(uint8_t *ucLed)
{
    // 用于记录当前LED状态以便比较
    uint8_t temp = 0x00;
    // 记录上一次LED状态，用于判断是否需要更新
    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {
        // 将LED状态映射到temp变量用于比较
        if (ucLed[i]) temp |= (1<<i); // 将第i位设置为1
    }

    // 仅当当前状态与上一次状态不同时才更新显示
    if (temp_old != temp)
    {
        // 调用相应的LED控制宏
        LED1_SET(temp & 0x01);
        LED2_SET(temp & 0x02);
        LED3_SET(temp & 0x04);
        LED4_SET(temp & 0x08);
        LED5_SET(temp & 0x10);
        LED6_SET(temp & 0x20);

        // 更新上一次状态
        temp_old = temp;
    }
}

/**
 * @brief LED显示任务函数
 *
 * 每次调用此函数时，LED会根据ucLed数组值进行更新
 */
void led_task(void)
{
    led_disp(ucLed);
}
