# ADC模块保姆级配置与实现文档

## 目录
1. [硬件连接说明](#硬件连接说明)
2. [CubeMX配置步骤](#cubemx配置步骤)
3. [DMA配置详解](#dma配置详解)
4. [底层BSP配置](#底层bsp配置)
5. [ADC工作原理](#adc工作原理)
6. [电压转换算法](#电压转换算法)
7. [应用层实现](#应用层实现)
8. [使用示例](#使用示例)
9. [常见问题与解决方案](#常见问题与解决方案)

---

## 硬件连接说明

### ADC硬件配置
本开发板使用ADC0的通道10进行电压采集：

| 功能 | GPIO端口 | GPIO引脚 | ADC通道 | 说明 |
|------|----------|----------|---------|------|
| ADC输入 | GPIOC | PC0 | ADC0_IN10 | 模拟电压输入 |

### 电路特性
- **输入电压范围**: 0-3.3V
- **ADC分辨率**: 12位 (0-4095)
- **参考电压**: 3.3V
- **采样频率**: 可配置
- **DMA支持**: 使用DMA1_CH0自动传输数据

### 电压分压电路
实际应用中通常需要电压分压电路来测量更高的电压：
```
输入电压 ──[R1]──┬──[R2]──GND
                 │
                PC0 (ADC输入)
```

分压比计算：`Vin_adc = Vin_actual × R2/(R1+R2)`

---

## CubeMX配置步骤

### 1. 基本ADC配置

#### 启用ADC0
1. 在Pinout视图中找到PC0
2. 设置PC0为ADC0_IN10

#### 配置ADC参数
1. 进入Configuration → Analog → ADC0
2. 设置基本参数：
   ```
   Mode: Independent mode
   Clock Prescaler: PCLK2 divided by 8
   Resolution: 12 bits
   Data Alignment: Right alignment
   Scan Conversion Mode: Enabled
   Continuous Conversion Mode: Enabled
   Discontinuous Conversion Mode: Disabled
   ```

#### 配置ADC通道
1. 在ADC0配置中，进入Parameter Settings
2. 配置Channel 10：
   ```
   Channel: IN10
   Rank: 1
   Sampling Time: 15 Cycles
   ```

### 2. DMA配置

#### 配置DMA1
1. 进入Configuration → System Core → DMA
2. 点击"Add"添加DMA请求
3. 配置DMA1 Channel 0：
   ```
   Request: ADC0
   Direction: Peripheral To Memory
   Priority: High
   Mode: Circular
   ```

#### DMA详细参数
```
Peripheral Data Width: Half Word (16-bit)
Memory Data Width: Half Word (16-bit)
Peripheral Increment: Disable
Memory Increment: Enable
Circular Mode: Enable
```

### 3. 中断配置（可选）

#### NVIC设置
如果需要ADC转换完成中断：
```
ADC global interrupt: Enabled
DMA1 channel0 global interrupt: Enabled (可选)
```

### 4. GPIO配置确认
确认GPIO配置：
```
PC0 (ADC0_IN10):
  Mode: Analog
  Pull-up/Pull-down: No pull-up and no pull-down
```

---

## DMA配置详解

### 1. DMA工作原理
DMA自动将ADC转换结果传输到内存，无需CPU干预：

```
ADC0 → DMA1_CH0 → Memory Buffer
```

### 2. DMA配置结构
```c
typedef struct {
    uint32_t periph_addr;         // 外设地址 (ADC数据寄存器)
    uint32_t periph_inc;          // 外设地址递增
    uint32_t memory0_addr;        // 内存地址
    uint32_t memory_inc;          // 内存地址递增
    uint32_t periph_memory_width; // 数据宽度
    uint32_t direction;           // 传输方向
    uint32_t number;              // 传输数据量
    uint32_t priority;            // 优先级
} dma_single_data_parameter_struct;
```

### 3. DMA初始化代码
```c
void dma_adc_init(void)
{
    dma_single_data_parameter_struct dma_single_data_parameter;
    
    /* 使能DMA1时钟 */
    rcu_periph_clock_enable(RCU_DMA1);
    
    /* 复位DMA通道 */
    dma_deinit(DMA1, DMA_CH0);
    
    /* 配置DMA参数 */
    dma_single_data_parameter.periph_addr = (uint32_t)(&ADC_RDATA(ADC0));
    dma_single_data_parameter.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_single_data_parameter.memory0_addr = (uint32_t)(adc_value);
    dma_single_data_parameter.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_single_data_parameter.periph_memory_width = DMA_PERIPH_WIDTH_16BIT;
    dma_single_data_parameter.direction = DMA_PERIPH_TO_MEMORY;
    dma_single_data_parameter.number = 1;
    dma_single_data_parameter.priority = DMA_PRIORITY_HIGH;
    
    /* 初始化DMA */
    dma_single_data_mode_init(DMA1, DMA_CH0, &dma_single_data_parameter);
    dma_channel_subperipheral_select(DMA1, DMA_CH0, DMA_SUBPERI0);
    
    /* 使能DMA循环模式 */
    dma_circulation_enable(DMA1, DMA_CH0);
    
    /* 使能DMA通道 */
    dma_channel_enable(DMA1, DMA_CH0);
}
```

---

## 底层BSP配置

### 1. 头文件定义 (mcu_cmic_gd32f470vet6.h)

```c
/* ADC相关定义 */
#define ADC1_PORT       GPIOC
#define ADC1_CLK_PORT   RCU_GPIOC
#define ADC1_PIN        GPIO_PIN_0

/* ADC数据缓冲区 */
extern uint16_t adc_value[1];

/* 函数声明 */
void bsp_adc_init(void);
```

### 2. BSP初始化函数 (mcu_cmic_gd32f470vet6.c)

```c
/* ADC数据缓冲区 */
uint16_t adc_value[1];

void bsp_adc_init(void)
{
    /* 使能相关时钟 */
    rcu_periph_clock_enable(ADC1_CLK_PORT);  // GPIO时钟
    rcu_periph_clock_enable(RCU_ADC0);       // ADC时钟
    rcu_periph_clock_enable(RCU_DMA1);       // DMA时钟
    
    /* 配置ADC时钟分频 */
    adc_clock_config(ADC_ADCCK_PCLK2_DIV8);
    
    /* 配置GPIO为模拟模式 */
    gpio_mode_set(ADC1_PORT, GPIO_MODE_ANALOG, GPIO_PUPD_NONE, ADC1_PIN);
    
    /* 配置DMA */
    dma_single_data_parameter_struct dma_single_data_parameter;
    dma_deinit(DMA1, DMA_CH0);
    
    dma_single_data_parameter.periph_addr = (uint32_t)(&ADC_RDATA(ADC0));
    dma_single_data_parameter.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_single_data_parameter.memory0_addr = (uint32_t)(adc_value);
    dma_single_data_parameter.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_single_data_parameter.periph_memory_width = DMA_PERIPH_WIDTH_16BIT;
    dma_single_data_parameter.direction = DMA_PERIPH_TO_MEMORY;
    dma_single_data_parameter.number = 1;
    dma_single_data_parameter.priority = DMA_PRIORITY_HIGH;
    
    dma_single_data_mode_init(DMA1, DMA_CH0, &dma_single_data_parameter);
    dma_channel_subperipheral_select(DMA1, DMA_CH0, DMA_SUBPERI0);
    dma_circulation_enable(DMA1, DMA_CH0);
    dma_channel_enable(DMA1, DMA_CH0);
    
    /* 配置ADC模式 */
    adc_sync_mode_config(ADC_SYNC_MODE_INDEPENDENT);
    adc_special_function_config(ADC0, ADC_CONTINUOUS_MODE, ENABLE);
    adc_special_function_config(ADC0, ADC_SCAN_MODE, ENABLE);
    adc_data_alignment_config(ADC0, ADC_DATAALIGN_RIGHT);
    
    /* 配置ADC通道 */
    adc_channel_length_config(ADC0, ADC_ROUTINE_CHANNEL, 1);
    adc_routine_channel_config(ADC0, 0, ADC_CHANNEL_10, ADC_SAMPLETIME_15);
    
    /* 配置ADC触发 */
    adc_external_trigger_source_config(ADC0, ADC_ROUTINE_CHANNEL, ADC_EXTTRIG_ROUTINE_T0_CH0);
    adc_external_trigger_config(ADC0, ADC_ROUTINE_CHANNEL, EXTERNAL_TRIGGER_DISABLE);
    
    /* 使能ADC DMA功能 */
    adc_dma_request_after_last_enable(ADC0);
    adc_dma_mode_enable(ADC0);
    
    /* 使能ADC */
    adc_enable(ADC0);
    delay_1ms(1);  // 等待ADC稳定
    
    /* ADC校准 */
    adc_calibration_enable(ADC0);
    
    /* 启动ADC软件触发 */
    adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);
}
```

**关键参数说明**：
- `ADC_ADCCK_PCLK2_DIV8`: ADC时钟分频，影响采样速度
- `ADC_CONTINUOUS_MODE`: 连续转换模式，自动重复转换
- `ADC_SCAN_MODE`: 扫描模式，支持多通道
- `ADC_DATAALIGN_RIGHT`: 数据右对齐
- `ADC_SAMPLETIME_15`: 采样时间15个时钟周期

---

## ADC工作原理

### 1. ADC转换过程
```
模拟输入 → 采样保持 → A/D转换 → 数字输出
```

### 2. 转换时序
```
触发 → 采样 → 转换 → 结果输出 → DMA传输
```

### 3. 连续转换模式
```c
// 连续转换流程
adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);
// ↓
// ADC自动连续转换
// ↓
// DMA自动传输结果到adc_value[0]
```

### 4. 采样时间计算
```
总转换时间 = 采样时间 + 12.5个ADC时钟周期

例如：
ADC时钟 = 84MHz / 8 = 10.5MHz
采样时间 = 15个时钟周期
总转换时间 = (15 + 12.5) / 10.5MHz ≈ 2.6μs
```

---

## 电压转换算法

### 1. 基本电压转换
```c
/*!
    \brief      将ADC值转换为电压(mV)
    \param[in]  adc_val: ADC转换值 (0-4095)
    \param[out] none
    \retval     电压值(mV)
*/
uint32_t adc_to_voltage_mv(uint16_t adc_val)
{
    // 基本公式：Voltage = (ADC_Value / 4095) × 3300mV
    return (uint32_t)((uint64_t)adc_val * 3300 / 4095);
}
```

### 2. 带分压比的电压转换
```c
/*!
    \brief      将ADC值转换为实际电压(考虑分压比)
    \param[in]  adc_val: ADC转换值
    \param[in]  ratio: 分压比 (×100, 例如199表示1.99)
    \param[out] none
    \retval     实际电压值(mV)
*/
uint32_t adc_to_actual_voltage_mv(uint16_t adc_val, uint16_t ratio)
{
    // 公式：Actual_Voltage = ADC_Voltage × Ratio / 100
    uint64_t temp_calc = (uint64_t)adc_val * ratio * 3300;
    return (uint32_t)(temp_calc / (4095 * 100));
}
```

### 3. 高精度电压转换
```c
/*!
    \brief      高精度电压转换(保留小数)
    \param[in]  adc_val: ADC转换值
    \param[in]  ratio: 分压比 (×100)
    \param[out] voltage_int: 电压整数部分
    \param[out] voltage_frac: 电压小数部分(×100)
    \retval     none
*/
void adc_to_voltage_precise(uint16_t adc_val, uint16_t ratio, 
                           uint16_t *voltage_int, uint16_t *voltage_frac)
{
    uint64_t temp_calc = (uint64_t)adc_val * ratio * 3300;
    uint32_t voltage_mv = temp_calc / (4095 * 100);
    
    *voltage_int = voltage_mv / 1000;           // 整数部分(V)
    *voltage_frac = (voltage_mv % 1000) / 10;   // 小数部分(×100)
}
```

### 4. 滤波算法
```c
/*!
    \brief      ADC数据滑动平均滤波
    \param[in]  new_value: 新的ADC值
    \param[out] none
    \retval     滤波后的ADC值
*/
uint16_t adc_moving_average_filter(uint16_t new_value)
{
    #define FILTER_SIZE 8
    static uint16_t filter_buffer[FILTER_SIZE] = {0};
    static uint8_t filter_index = 0;
    static uint32_t filter_sum = 0;
    static uint8_t filter_count = 0;
    
    // 移除旧值
    filter_sum -= filter_buffer[filter_index];
    
    // 添加新值
    filter_buffer[filter_index] = new_value;
    filter_sum += new_value;
    
    // 更新索引
    filter_index = (filter_index + 1) % FILTER_SIZE;
    
    // 更新计数
    if(filter_count < FILTER_SIZE)
        filter_count++;
    
    // 返回平均值
    return filter_sum / filter_count;
}
```

---

## 应用层实现

### 1. 应用层头文件 (adc_app.h)

```c
#ifndef __ADC_APP_H__
#define __ADC_APP_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ADC相关变量声明 */
extern uint16_t adc_value[1];

/* 函数声明 */
void adc_task(void);
uint32_t get_voltage_mv(void);
uint16_t get_filtered_adc_value(void);
void adc_calibration(void);

#ifdef __cplusplus
}
#endif

#endif
```

### 2. 应用层实现文件 (adc_app.c)

```c
#include "mcu_cmic_gd32f470vet6.h"
#include "adc_app.h"

/* 外部变量 */
extern uint16_t adc_value[1];

/* 内部变量 */
static uint16_t filtered_adc_value = 0;
static uint32_t voltage_mv = 0;

/*!
    \brief      ADC任务函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void adc_task(void)
{
    // 获取滤波后的ADC值
    filtered_adc_value = adc_moving_average_filter(adc_value[0]);
    
    // 转换为电压值
    voltage_mv = adc_to_voltage_mv(filtered_adc_value);
}

/*!
    \brief      获取当前电压值
    \param[in]  none
    \param[out] none
    \retval     电压值(mV)
*/
uint32_t get_voltage_mv(void)
{
    return voltage_mv;
}

/*!
    \brief      获取滤波后的ADC值
    \param[in]  none
    \param[out] none
    \retval     滤波后的ADC值
*/
uint16_t get_filtered_adc_value(void)
{
    return filtered_adc_value;
}

/*!
    \brief      ADC校准函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void adc_calibration(void)
{
    // 禁用ADC
    adc_disable(ADC0);
    
    // 重新校准
    adc_calibration_enable(ADC0);
    
    // 重新使能ADC
    adc_enable(ADC0);
    delay_1ms(1);
    
    // 重新启动转换
    adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);
}
```

---

## 使用示例

### 1. 基本初始化和使用
```c
int main(void)
{
    // 系统初始化
    system_init();
    
    // ADC初始化
    bsp_adc_init();
    
    // 主循环
    while(1)
    {
        adc_task();  // 处理ADC数据
        
        // 获取电压值
        uint32_t voltage = get_voltage_mv();
        printf("Voltage: %lu.%02lu V\r\n", voltage/1000, (voltage%1000)/10);
        
        delay_ms(100);
    }
}
```

### 2. 电压监测示例
```c
void voltage_monitor_example(void)
{
    static uint32_t last_voltage = 0;
    uint32_t current_voltage = get_voltage_mv();
    
    // 电压变化检测
    if(abs(current_voltage - last_voltage) > 50)  // 50mV变化阈值
    {
        printf("Voltage changed: %lu.%02lu V\r\n", 
               current_voltage/1000, (current_voltage%1000)/10);
        last_voltage = current_voltage;
    }
    
    // 电压报警
    if(current_voltage > 3000)  // 3.0V报警
    {
        printf("WARNING: High voltage detected!\r\n");
        LED2_ON;  // 点亮报警LED
    }
    else
    {
        LED2_OFF;
    }
}
```

### 3. 数据采集示例
```c
#define SAMPLE_SIZE 100

void data_acquisition_example(void)
{
    static uint16_t sample_buffer[SAMPLE_SIZE];
    static uint16_t sample_index = 0;
    static uint32_t sample_sum = 0;
    
    // 采集数据
    uint16_t current_adc = get_filtered_adc_value();
    
    // 移除旧数据
    sample_sum -= sample_buffer[sample_index];
    
    // 添加新数据
    sample_buffer[sample_index] = current_adc;
    sample_sum += current_adc;
    
    // 更新索引
    sample_index = (sample_index + 1) % SAMPLE_SIZE;
    
    // 计算平均值
    uint16_t average_adc = sample_sum / SAMPLE_SIZE;
    uint32_t average_voltage = adc_to_voltage_mv(average_adc);
    
    printf("Average Voltage: %lu.%02lu V\r\n", 
           average_voltage/1000, (average_voltage%1000)/10);
}
```

### 4. 多通道ADC示例
```c
// 如果需要多通道ADC
#define ADC_CHANNEL_COUNT 3
uint16_t adc_multi_value[ADC_CHANNEL_COUNT];

void multi_channel_adc_init(void)
{
    // 配置多通道ADC
    adc_channel_length_config(ADC0, ADC_ROUTINE_CHANNEL, ADC_CHANNEL_COUNT);
    adc_routine_channel_config(ADC0, 0, ADC_CHANNEL_10, ADC_SAMPLETIME_15);
    adc_routine_channel_config(ADC0, 1, ADC_CHANNEL_11, ADC_SAMPLETIME_15);
    adc_routine_channel_config(ADC0, 2, ADC_CHANNEL_12, ADC_SAMPLETIME_15);
    
    // 更新DMA配置
    dma_single_data_parameter.number = ADC_CHANNEL_COUNT;
    dma_single_data_parameter.memory0_addr = (uint32_t)(adc_multi_value);
}

void multi_channel_process(void)
{
    for(int i = 0; i < ADC_CHANNEL_COUNT; i++)
    {
        uint32_t voltage = adc_to_voltage_mv(adc_multi_value[i]);
        printf("CH%d: %lu.%02lu V  ", i, voltage/1000, (voltage%1000)/10);
    }
    printf("\r\n");
}
```

### 5. 在调度器中使用
```c
// 在scheduler.c中添加ADC任务
static task_t scheduler_task[] =
{
     {led_task,  1,    0}      // 1ms周期执行LED任务
    ,{adc_task,  100,  0}      // 100ms周期执行ADC任务
    ,{btn_task,  5,    0}      // 5ms周期执行按键任务
    ,{uart_task, 5,    0}      // 5ms周期执行串口任务
};
```

---

## 常见问题与解决方案

### 1. ADC读数不稳定
**可能原因**：
- 参考电压不稳定
- 输入信号有噪声
- 采样时间过短

**解决方案**：
```c
// 增加采样时间
adc_routine_channel_config(ADC0, 0, ADC_CHANNEL_10, ADC_SAMPLETIME_480);

// 添加软件滤波
uint16_t filtered_value = adc_moving_average_filter(adc_value[0]);

// 添加硬件滤波电容
// 在ADC输入端并联100nF电容
```

### 2. ADC转换值为0
**可能原因**：
- GPIO配置错误
- ADC未正确初始化
- DMA配置错误

**解决方案**：
```c
// 检查GPIO配置
gpio_mode_set(ADC1_PORT, GPIO_MODE_ANALOG, GPIO_PUPD_NONE, ADC1_PIN);

// 检查ADC使能
adc_enable(ADC0);
adc_calibration_enable(ADC0);

// 检查DMA配置
dma_channel_enable(DMA1, DMA_CH0);
adc_dma_mode_enable(ADC0);
```

### 3. 电压转换不准确
**可能原因**：
- 参考电压偏差
- 分压比计算错误
- ADC非线性误差

**解决方案**：
```c
// 校准参考电压
#define VREF_ACTUAL 3300  // 实际测量的参考电压(mV)
uint32_t voltage_mv = (uint64_t)adc_val * VREF_ACTUAL / 4095;

// 校准分压比
// 使用万用表测量实际分压比
#define ACTUAL_RATIO 199  // 实际测量的分压比×100

// 多点校准
typedef struct {
    uint16_t adc_val;
    uint16_t actual_mv;
} calibration_point_t;

calibration_point_t cal_points[] = {
    {0,    0},
    {1024, 825},
    {2048, 1650},
    {3072, 2475},
    {4095, 3300}
};
```

### 4. DMA传输异常
**可能原因**：
- DMA通道冲突
- 内存地址错误
- DMA配置错误

**解决方案**：
```c
// 检查DMA通道是否被其他外设占用
dma_deinit(DMA1, DMA_CH0);

// 确保内存地址对齐
uint16_t adc_value[1] __attribute__((aligned(4)));

// 检查DMA配置
dma_single_data_parameter.periph_memory_width = DMA_PERIPH_WIDTH_16BIT;
dma_single_data_parameter.direction = DMA_PERIPH_TO_MEMORY;
```

### 5. 采样频率过低
**可能原因**：
- ADC时钟分频过大
- 采样时间过长
- 连续转换模式未使能

**解决方案**：
```c
// 减少ADC时钟分频
adc_clock_config(ADC_ADCCK_PCLK2_DIV4);  // 改为4分频

// 减少采样时间
adc_routine_channel_config(ADC0, 0, ADC_CHANNEL_10, ADC_SAMPLETIME_3);

// 使能连续转换
adc_special_function_config(ADC0, ADC_CONTINUOUS_MODE, ENABLE);
```

---

## 总结

本文档详细介绍了ADC模块的完整配置和实现过程，包括：

1. **硬件层面**: 引脚分配、电路连接、分压电路设计
2. **配置层面**: CubeMX配置、DMA设置、时钟配置
3. **驱动层面**: BSP底层驱动、DMA自动传输
4. **算法层面**: 电压转换算法、滤波算法
5. **应用层面**: 数据处理、电压监测、多通道采集
6. **实践层面**: 各种使用示例和问题解决

通过本文档，您可以：
- 完全掌握ADC模块的配置方法
- 理解ADC和DMA的工作原理
- 实现精确的电压测量功能
- 处理各种ADC相关问题
- 构建完整的数据采集系统

建议在实际使用时，根据应用需求选择合适的采样频率和精度，并合理设计滤波算法来提高测量稳定性。
