# OLED显示模块保姆级配置与实现文档

## 目录
1. [硬件连接说明](#硬件连接说明)
2. [CubeMX配置步骤](#cubemx配置步骤)
3. [I2C配置详解](#i2c配置详解)
4. [底层BSP配置](#底层bsp配置)
5. [OLED工作原理](#oled工作原理)
6. [字体和显示原理](#字体和显示原理)
7. [应用层实现](#应用层实现)
8. [中文显示支持](#中文显示支持)
9. [使用示例](#使用示例)
10. [常见问题与解决方案](#常见问题与解决方案)

---

## 硬件连接说明

### OLED硬件配置
本开发板使用I2C接口的SSD1306 OLED显示屏：

| 功能 | GPIO端口 | GPIO引脚 | I2C功能 | 说明 |
|------|----------|----------|---------|------|
| I2C_SCL | GPIOB | PB8 | I2C0_SCL | 时钟线 |
| I2C_SDA | GPIOB | PB9 | I2C0_SDA | 数据线 |

### OLED屏幕规格
- **分辨率**: 128×64像素
- **控制器**: SSD1306
- **接口**: I2C (地址: 0x78)
- **电源**: 3.3V
- **显示颜色**: 单色(蓝色/白色)
- **可视角度**: >160°

### 电路连接
```
MCU          OLED
PB8(SCL) --- SCL
PB9(SDA) --- SDA
3.3V     --- VCC
GND      --- GND
```

---

## CubeMX配置步骤

### 1. 基本I2C配置

#### 启用I2C0
1. 在Pinout视图中找到PB8和PB9
2. 设置PB8为I2C0_SCL
3. 设置PB9为I2C0_SDA

#### 配置I2C参数
1. 进入Configuration → Connectivity → I2C0
2. 设置基本参数：
   ```
   I2C Speed Mode: Fast Mode
   I2C Clock Speed: 400000 Hz
   Clock No Stretch Mode: Disabled
   General Call Mode: Disabled
   No Stretch Mode: Disabled
   ```

### 2. DMA配置（可选）

#### 配置DMA0
如果需要DMA传输：
1. 进入Configuration → System Core → DMA
2. 配置DMA0 Channel 6：
   ```
   Request: I2C0_TX
   Direction: Memory To Peripheral
   Priority: Low
   Mode: Normal
   ```

### 3. GPIO配置确认
确认GPIO配置：
```
PB8 (I2C0_SCL):
  Mode: Alternate Function Open Drain
  Pull-up/Pull-down: Pull-up
  Maximum output speed: High
  
PB9 (I2C0_SDA):
  Mode: Alternate Function Open Drain
  Pull-up/Pull-down: Pull-up
  Maximum output speed: High
```

---

## I2C配置详解

### 1. I2C工作原理
I2C是两线制串行总线，用于连接微控制器和外设：

```
START → 设备地址 → 寄存器地址 → 数据 → STOP
```

### 2. SSD1306 I2C协议
```
设备地址: 0x78 (7位地址0x3C左移1位)
命令格式: [设备地址] [0x00] [命令字节]
数据格式: [设备地址] [0x40] [数据字节]
```

### 3. I2C时序配置
```c
/* I2C时钟配置 */
#define I2C_SPEED           400000      // 400kHz快速模式
#define I2C0_OWN_ADDRESS7   0x72        // 主机地址

void i2c_config(void)
{
    /* 配置I2C时钟 */
    i2c_clock_config(I2C0, I2C_SPEED, I2C_DTCY_2);
    
    /* 配置I2C地址模式 */
    i2c_mode_addr_config(I2C0, I2C_I2CMODE_ENABLE, I2C_ADDFORMAT_7BITS, I2C0_OWN_ADDRESS7);
    
    /* 使能I2C */
    i2c_enable(I2C0);
    
    /* 使能应答 */
    i2c_ack_config(I2C0, I2C_ACK_ENABLE);
}
```

---

## 底层BSP配置

### 1. 头文件定义 (mcu_cmic_gd32f470vet6.h)

```c
/* OLED相关定义 */
#define OLED_PORT           GPIOB
#define OLED_CLK_PORT       RCU_GPIOB
#define OLED_DAT_PIN        GPIO_PIN_9  // SDA
#define OLED_CLK_PIN        GPIO_PIN_8  // SCL

#define I2C0_OWN_ADDRESS7   0x72
#define OLED_ADDRESS        0x78

/* OLED缓冲区 */
extern __IO uint8_t oled_cmd_buf[2];
extern __IO uint8_t oled_data_buf[2];

/* 函数声明 */
void bsp_oled_init(void);
void I2C_Bus_Reset(void);
```

### 2. BSP初始化函数 (mcu_cmic_gd32f470vet6.c)

```c
/* OLED命令和数据缓冲区 */
__IO uint8_t oled_cmd_buf[2] = {0x00, 0x00};   // 命令缓冲区
__IO uint8_t oled_data_buf[2] = {0x40, 0x00};  // 数据缓冲区

void bsp_oled_init(void)
{
    dma_single_data_parameter_struct dma_init_struct;
    
    /* 使能GPIO时钟 */
    rcu_periph_clock_enable(OLED_CLK_PORT);
    /* 使能I2C0时钟 */
    rcu_periph_clock_enable(RCU_I2C0);
    /* 使能DMA0时钟 */
    rcu_periph_clock_enable(RCU_DMA0);
    
    /* 配置GPIO复用功能 */
    gpio_af_set(OLED_PORT, GPIO_AF_4, OLED_DAT_PIN);  // PB9 -> I2C0_SDA
    gpio_af_set(OLED_PORT, GPIO_AF_4, OLED_CLK_PIN);  // PB8 -> I2C0_SCL
    
    /* 配置SDA引脚 */
    gpio_mode_set(OLED_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, OLED_DAT_PIN);
    gpio_output_options_set(OLED_PORT, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, OLED_DAT_PIN);
    
    /* 配置SCL引脚 */
    gpio_mode_set(OLED_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, OLED_CLK_PIN);
    gpio_output_options_set(OLED_PORT, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, OLED_CLK_PIN);
    
    /* 配置DMA用于I2C发送 */
    dma_deinit(DMA0, DMA_CH6);
    dma_init_struct.direction = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.memory0_addr = (uint32_t)oled_cmd_buf;
    dma_init_struct.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.number = 2;
    dma_init_struct.periph_addr = (uint32_t)&I2C_DATA(I2C0);
    dma_init_struct.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority = DMA_PRIORITY_LOW;
    dma_single_data_mode_init(DMA0, DMA_CH6, &dma_init_struct);
    dma_channel_subperipheral_select(DMA0, DMA_CH6, DMA_SUBPERI1);
    
    /* 配置I2C0时钟 */
    i2c_clock_config(I2C0, 400000, I2C_DTCY_2);
    /* 配置I2C0地址 */
    i2c_mode_addr_config(I2C0, I2C_I2CMODE_ENABLE, I2C_ADDFORMAT_7BITS, I2C0_OWN_ADDRESS7);
    /* 使能I2C0 */
    i2c_enable(I2C0);
    /* 使能应答 */
    i2c_ack_config(I2C0, I2C_ACK_ENABLE);
    
    /* 初始化OLED */
    OLED_Init();
}

/* I2C总线复位函数 */
void I2C_Bus_Reset(void)
{
    /* 禁用I2C */
    i2c_disable(I2C0);
    
    /* 将引脚配置为GPIO模式进行手动复位 */
    gpio_mode_set(GPIOB, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, GPIO_PIN_8 | GPIO_PIN_9);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, GPIO_PIN_8 | GPIO_PIN_9);
    
    /* 生成时钟脉冲清除总线 */
    for(int i = 0; i < 9; i++) {
        gpio_bit_reset(GPIOB, GPIO_PIN_8);  // SCL低
        delay_ms(1);
        gpio_bit_set(GPIOB, GPIO_PIN_8);    // SCL高
        delay_ms(1);
    }
    
    /* 生成STOP条件 */
    gpio_bit_set(GPIOB, GPIO_PIN_8);        // SCL高
    gpio_bit_reset(GPIOB, GPIO_PIN_9);      // SDA低
    delay_ms(5);
    gpio_bit_set(GPIOB, GPIO_PIN_9);        // SDA高，产生STOP
    delay_ms(5);
    
    /* 恢复I2C引脚为AF模式 */
    gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_8 | GPIO_PIN_9);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_OD, GPIO_OSPEED_50MHZ, GPIO_PIN_8 | GPIO_PIN_9);
    
    /* 重新初始化I2C */
    i2c_deinit(I2C0);
    i2c_clock_config(I2C0, 400000, I2C_DTCY_2);
    i2c_mode_addr_config(I2C0, I2C_I2CMODE_ENABLE, I2C_ADDFORMAT_7BITS, 0x72);
    i2c_enable(I2C0);
    i2c_ack_config(I2C0, I2C_ACK_ENABLE);
    
    delay_ms(10);
}
```

---

## OLED工作原理

### 1. SSD1306控制器
SSD1306是OLED显示屏的控制器芯片：
- 内置显存(GDDRAM): 128×64位
- 支持多种接口: I2C、SPI
- 内置电荷泵: 无需外部高压
- 256级亮度控制

### 2. 显存组织
```
页(Page)组织: 8页，每页128列
每页高度: 8像素
总分辨率: 128×64像素

Page 0: 第0-7行
Page 1: 第8-15行
Page 2: 第16-23行
Page 3: 第24-31行
Page 4: 第32-39行
Page 5: 第40-47行
Page 6: 第48-55行
Page 7: 第56-63行
```

### 3. 坐标系统
```
(0,0) ────────────────── (127,0)
  │                         │
  │                         │
  │                         │
  │                         │
(0,63) ──────────────────(127,63)
```

### 4. 基本操作函数

```c
/* OLED初始化命令序列 */
uint8_t initcmd1[] = {
    0xae,       // 显示关闭
    0x20, 0x00, // 设置内存地址模式
    0xb0,       // 设置页地址
    0xc8,       // 设置COM扫描方向
    0x00,       // 设置低列地址
    0x10,       // 设置高列地址
    0x40,       // 设置显示开始行
    0x81, 0xff, // 设置对比度
    0xa1,       // 设置段重映射
    0xa6,       // 设置正常显示
    0xa8, 0x3f, // 设置复用比
    0xa4,       // 全局显示开启
    0xd3, 0x00, // 设置显示偏移
    0xd5, 0xf0, // 设置显示时钟分频
    0xd9, 0x22, // 设置预充电周期
    0xda, 0x12, // 设置COM引脚配置
    0xdb, 0x20, // 设置VCOMH电压
    0x8d, 0x14, // 使能电荷泵
    0xaf        // 显示开启
};

/* 写命令函数 */
void OLED_Write_cmd(uint8_t cmd)
{
    oled_cmd_buf[0] = 0x00;  // 命令控制字节
    oled_cmd_buf[1] = cmd;   // 命令数据
    
    // 使用DMA发送或直接I2C发送
    // ... I2C发送代码
}

/* 写数据函数 */
void OLED_Write_data(uint8_t data)
{
    oled_data_buf[0] = 0x40; // 数据控制字节
    oled_data_buf[1] = data; // 显示数据
    
    // 使用DMA发送或直接I2C发送
    // ... I2C发送代码
}

/* 设置显示位置 */
void OLED_Set_Position(uint8_t x, uint8_t y)
{
    OLED_Write_cmd(0xb0 + y);           // 设置页地址
    OLED_Write_cmd(((x & 0xf0) >> 4) | 0x10); // 设置高4位列地址
    OLED_Write_cmd((x & 0x0f) | 0x00);  // 设置低4位列地址
}
```

---

## 字体和显示原理

### 1. 字体数据结构

#### 6×8字体 (F6X8)
```c
// 每个字符占用6字节，高8像素
const uint8_t F6X8[][6] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, // 空格
    {0x00, 0x00, 0x00, 0x2f, 0x00, 0x00}, // !
    {0x00, 0x00, 0x07, 0x00, 0x07, 0x00}, // "
    // ... 更多字符
};
```

#### 8×16字体 (F8X16)
```c
// 每个字符占用16字节，高16像素，分为上下两部分
const uint8_t F8X16[] = {
    // 字符'A'的字体数据
    0x00,0x10,0x0C,0x0E,0x0A,0x0C,0x14,0x00, // 上半部分
    0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00, // 下半部分
    // ... 更多字符
};
```

### 2. 字符显示函数

```c
/*!
    \brief      显示单个字符
    \param[in]  x: X坐标 (0-127)
    \param[in]  y: Y坐标 (0-3，以页为单位)
    \param[in]  ch: 要显示的字符
    \param[in]  fontsize: 字体大小 (8或16)
    \param[out] none
    \retval     none
*/
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t ch, uint8_t fontsize)
{
    uint8_t c = 0, i = 0;
    c = ch - ' ';  // 转换为字体数组索引

    if (x > 127) // 超出右边界
    {
        x = 0;
        y++;
    }

    if (fontsize == 16)
    {
        // 显示16像素高字符的上半部分
        OLED_Set_Position(x, y);
        for (i = 0; i < 8; i++)
        {
            OLED_Write_data(F8X16[c * 16 + i]);
        }
        // 显示16像素高字符的下半部分
        OLED_Set_Position(x, y + 1);
        for (i = 0; i < 8; i++)
        {
            OLED_Write_data(F8X16[c * 16 + i + 8]);
        }
    }
    else
    {
        // 显示8像素高字符
        OLED_Set_Position(x, y);
        for (i = 0; i < 6; i++)
        {
            OLED_Write_data(F6X8[c][i]);
        }
    }
}

/*!
    \brief      显示字符串
    \param[in]  x: X坐标
    \param[in]  y: Y坐标
    \param[in]  str: 字符串
    \param[in]  fontsize: 字体大小
    \param[out] none
    \retval     none
*/
void OLED_ShowStr(uint8_t x, uint8_t y, char *str, uint8_t fontsize)
{
    uint8_t j = 0;
    while (str[j])
    {
        OLED_ShowChar(x, y, str[j], fontsize);
        x += (fontsize == 16) ? 8 : 6;
        if (x > 120)
        {
            x = 0;
            y += (fontsize == 16) ? 2 : 1;
        }
        j++;
    }
}
```

### 3. 基本图形函数

```c
/*!
    \brief      画点
    \param[in]  x: X坐标 (0-127)
    \param[in]  y: Y坐标 (0-63)
    \param[in]  mode: 1-点亮，0-熄灭
    \param[out] none
    \retval     none
*/
void OLED_DrawPoint(uint8_t x, uint8_t y, uint8_t mode)
{
    uint8_t page, remainder;
    page = y / 8;
    remainder = y % 8;
    
    OLED_Set_Position(x, page);
    if (mode)
        OLED_Write_data(1 << remainder);
    else
        OLED_Write_data(0);
}

/*!
    \brief      画线
    \param[in]  x1,y1: 起点坐标
    \param[in]  x2,y2: 终点坐标
    \param[out] none
    \retval     none
*/
void OLED_DrawLine(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2)
{
    uint8_t t;
    int xerr = 0, yerr = 0, delta_x, delta_y, distance;
    int incx, incy, uRow, uCol;
    
    delta_x = x2 - x1;
    delta_y = y2 - y1;
    uRow = x1;
    uCol = y1;
    
    if (delta_x > 0) incx = 1;
    else if (delta_x == 0) incx = 0;
    else { incx = -1; delta_x = -delta_x; }
    
    if (delta_y > 0) incy = 1;
    else if (delta_y == 0) incy = 0;
    else { incy = -1; delta_y = -delta_y; }
    
    if (delta_x > delta_y) distance = delta_x;
    else distance = delta_y;
    
    for (t = 0; t <= distance + 1; t++)
    {
        OLED_DrawPoint(uRow, uCol, 1);
        xerr += delta_x;
        yerr += delta_y;
        if (xerr > distance)
        {
            xerr -= distance;
            uRow += incx;
        }
        if (yerr > distance)
        {
            yerr -= distance;
            uCol += incy;
        }
    }
}
```

---

## 应用层实现

### 1. 应用层头文件 (oled_app.h)

```c
#ifndef __OLED_APP_H__
#define __OLED_APP_H__

#include "stdint.h"
#include "stdarg.h"

#ifdef __cplusplus
extern "C" {
#endif

/* OLED应用层函数声明 */
void oled_task(void);
int oled_printf(uint8_t x, uint8_t y, const char *format, ...);
void oled_display_system_info(void);
void oled_display_voltage(uint32_t voltage_mv);

#ifdef __cplusplus
}
#endif

#endif
```

### 2. 应用层实现文件 (oled_app.c)

```c
#include "mcu_cmic_gd32f470vet6.h"
#include "oled_app.h"
#include <stdio.h>
#include <stdarg.h>

/*!
    \brief      OLED printf函数
    \param[in]  x: X坐标
    \param[in]  y: Y坐标
    \param[in]  format: 格式化字符串
    \param[out] none
    \retval     打印的字符数
*/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
    char buffer[128];
    va_list arg;
    int len;

    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    OLED_ShowStr(x, y, buffer, 8);
    return len;
}

/*!
    \brief      OLED任务函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void oled_task(void)
{
    static uint32_t last_update_time = 0;
    uint32_t current_time = get_system_ms();
    
    // 每500ms更新一次显示
    if(current_time - last_update_time >= 500)
    {
        last_update_time = current_time;
        
        if(get_sampling_status())
        {
            // 采样模式：显示时间和电压
            extern rtc_parameter_struct rtc_initpara;
            extern uint16_t adc_value[1];
            
            // 获取当前时间
            rtc_current_time_get(&rtc_initpara);
            uint8_t hour = ((rtc_initpara.hour >> 4) * 10) + (rtc_initpara.hour & 0x0F);
            uint8_t minute = ((rtc_initpara.minute >> 4) * 10) + (rtc_initpara.minute & 0x0F);
            uint8_t second = ((rtc_initpara.second >> 4) * 10) + (rtc_initpara.second & 0x0F);
            
            oled_printf(0, 0, "%02d:%02d:%02d", hour, minute, second);
            
            // 计算并显示电压
            uint32_t voltage_mv = adc_to_voltage_mv(adc_value[0]);
            oled_printf(0, 1, "%lu.%02luV      ", voltage_mv/1000, (voltage_mv%1000)/10);
        }
        else
        {
            // 空闲模式：显示系统状态
            oled_printf(0, 0, "system idle");
            oled_printf(0, 1, "");  // 清除第1行
        }
    }
}

/*!
    \brief      显示系统信息
    \param[in]  none
    \param[out] none
    \retval     none
*/
void oled_display_system_info(void)
{
    OLED_Clear();
    oled_printf(0, 0, "GD32F470VET6");
    oled_printf(0, 1, "System Ready");
    oled_printf(0, 2, "Version 1.0");
    oled_printf(0, 3, "2025-01-01");
}

/*!
    \brief      显示电压值
    \param[in]  voltage_mv: 电压值(mV)
    \param[out] none
    \retval     none
*/
void oled_display_voltage(uint32_t voltage_mv)
{
    oled_printf(0, 1, "V:%lu.%02luV", voltage_mv/1000, (voltage_mv%1000)/10);
}
```

---

## 中文显示支持

### 1. 中文字体数据结构
中文字符通常使用16×16像素点阵，需要32字节存储：

```c
// 16×16中文字体结构
typedef struct {
    uint8_t data[32];  // 32字节点阵数据
} chinese_font_t;

// 常用中文字符字库
const chinese_font_t chinese_chars[] = {
    // "温" 字的点阵数据
    {{0x10,0x60,0x02,0x8C,0x00,0x00,0xFE,0x92,0x92,0x92,0x92,0x92,0xFE,0x00,0x00,0x00,
      0x04,0x04,0x7E,0x01,0x40,0x7E,0x42,0x42,0x7E,0x42,0x7E,0x42,0x42,0x7E,0x40,0x00}},

    // "度" 字的点阵数据
    {{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
      0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00}},

    // 更多中文字符...
};

// 中文字符索引表
typedef struct {
    uint16_t unicode;     // Unicode编码
    uint8_t index;        // 字库索引
} chinese_index_t;

const chinese_index_t chinese_index[] = {
    {0x6E29, 0},  // "温"
    {0x5EA6, 1},  // "度"
    // 更多字符索引...
};
```

### 2. 中文字符显示函数

```c
/*!
    \brief      显示16×16中文字符
    \param[in]  x: X坐标 (0-127)
    \param[in]  y: Y坐标 (0-3，以页为单位)
    \param[in]  unicode: 中文字符Unicode编码
    \param[out] none
    \retval     none
*/
void OLED_ShowChinese(uint8_t x, uint8_t y, uint16_t unicode)
{
    uint8_t char_index = 0xFF;

    // 查找字符索引
    for(int i = 0; i < sizeof(chinese_index)/sizeof(chinese_index_t); i++)
    {
        if(chinese_index[i].unicode == unicode)
        {
            char_index = chinese_index[i].index;
            break;
        }
    }

    if(char_index == 0xFF) return;  // 字符未找到

    // 显示上半部分 (前16字节)
    OLED_Set_Position(x, y);
    for(int i = 0; i < 16; i++)
    {
        OLED_Write_data(chinese_chars[char_index].data[i]);
    }

    // 显示下半部分 (后16字节)
    OLED_Set_Position(x, y + 1);
    for(int i = 16; i < 32; i++)
    {
        OLED_Write_data(chinese_chars[char_index].data[i]);
    }
}

/*!
    \brief      显示中文字符串
    \param[in]  x: X坐标
    \param[in]  y: Y坐标
    \param[in]  str: UTF-8编码的中文字符串
    \param[out] none
    \retval     none
*/
void OLED_ShowChineseString(uint8_t x, uint8_t y, const char *str)
{
    uint8_t pos_x = x;
    uint8_t pos_y = y;

    while(*str)
    {
        if((*str & 0x80) == 0)  // ASCII字符
        {
            OLED_ShowChar(pos_x, pos_y, *str, 8);
            pos_x += 6;
            str++;
        }
        else  // 中文字符 (UTF-8编码)
        {
            uint16_t unicode = utf8_to_unicode(str);
            OLED_ShowChinese(pos_x, pos_y, unicode);
            pos_x += 16;
            str += 3;  // UTF-8中文字符占3字节
        }

        // 换行处理
        if(pos_x > 112)  // 128-16=112
        {
            pos_x = 0;
            pos_y += 2;  // 中文字符高度为2页
        }
    }
}
```

### 3. UTF-8转Unicode函数

```c
/*!
    \brief      UTF-8转Unicode
    \param[in]  utf8_str: UTF-8字符串指针
    \param[out] none
    \retval     Unicode编码
*/
uint16_t utf8_to_unicode(const char *utf8_str)
{
    uint8_t byte1 = utf8_str[0];
    uint8_t byte2 = utf8_str[1];
    uint8_t byte3 = utf8_str[2];

    // 3字节UTF-8转Unicode
    if((byte1 & 0xF0) == 0xE0)
    {
        uint16_t unicode = ((byte1 & 0x0F) << 12) |
                          ((byte2 & 0x3F) << 6) |
                          (byte3 & 0x3F);
        return unicode;
    }

    return 0;  // 转换失败
}
```

---

## 使用示例

### 1. 基本初始化和使用
```c
int main(void)
{
    // 系统初始化
    system_init();
    
    // OLED初始化
    bsp_oled_init();
    
    // 显示启动信息
    oled_display_system_info();
    delay_ms(2000);
    
    // 主循环
    while(1)
    {
        oled_task();  // 更新OLED显示
        delay_ms(10);
    }
}
```

### 2. 实时数据显示
```c
void realtime_data_display(void)
{
    static uint32_t counter = 0;
    
    OLED_Clear();
    
    // 显示计数器
    oled_printf(0, 0, "Count: %lu", counter++);
    
    // 显示系统时间
    uint32_t system_time = get_system_ms();
    oled_printf(0, 1, "Time: %lu.%03lus", system_time/1000, system_time%1000);
    
    // 显示ADC值
    extern uint16_t adc_value[1];
    oled_printf(0, 2, "ADC: %d", adc_value[0]);
    
    // 显示电压
    uint32_t voltage = adc_to_voltage_mv(adc_value[0]);
    oled_printf(0, 3, "V: %lu.%02luV", voltage/1000, (voltage%1000)/10);
}
```

### 3. 菜单系统示例
```c
typedef enum {
    MENU_MAIN,
    MENU_SETTINGS,
    MENU_DATA,
    MENU_ABOUT
} menu_state_t;

menu_state_t current_menu = MENU_MAIN;
uint8_t menu_index = 0;

void oled_menu_display(void)
{
    OLED_Clear();
    
    switch(current_menu)
    {
        case MENU_MAIN:
            oled_printf(0, 0, "=== MAIN MENU ===");
            oled_printf(0, 1, menu_index == 0 ? ">Settings" : " Settings");
            oled_printf(0, 2, menu_index == 1 ? ">Data View" : " Data View");
            oled_printf(0, 3, menu_index == 2 ? ">About" : " About");
            break;
            
        case MENU_SETTINGS:
            oled_printf(0, 0, "=== SETTINGS ===");
            oled_printf(0, 1, "Ratio: 1.99");
            oled_printf(0, 2, "Limit: 10.11V");
            oled_printf(0, 3, "Press KEY6 to exit");
            break;
            
        case MENU_DATA:
            oled_printf(0, 0, "=== DATA VIEW ===");
            oled_printf(0, 1, "Current: 2.45V");
            oled_printf(0, 2, "Max: 3.30V");
            oled_printf(0, 3, "Min: 0.00V");
            break;
            
        case MENU_ABOUT:
            oled_printf(0, 0, "=== ABOUT ===");
            oled_printf(0, 1, "GD32F470VET6");
            oled_printf(0, 2, "Version: 1.0");
            oled_printf(0, 3, "2025-01-01");
            break;
    }
}
```

### 4. 图形显示示例
```c
void oled_graphics_demo(void)
{
    OLED_Clear();
    
    // 绘制边框
    for(int i = 0; i < 128; i++)
    {
        OLED_DrawPoint(i, 0, 1);
        OLED_DrawPoint(i, 63, 1);
    }
    for(int i = 0; i < 64; i++)
    {
        OLED_DrawPoint(0, i, 1);
        OLED_DrawPoint(127, i, 1);
    }
    
    // 绘制对角线
    OLED_DrawLine(0, 0, 127, 63);
    OLED_DrawLine(0, 63, 127, 0);
    
    // 绘制中心文字
    oled_printf(40, 2, "GRAPHICS");
    oled_printf(45, 3, "DEMO");
}
```

### 5. 在调度器中使用
```c
// 在scheduler.c中添加OLED任务
static task_t scheduler_task[] =
{
     {led_task,  1,    0}      // 1ms周期执行LED任务
    ,{adc_task,  100,  0}      // 100ms周期执行ADC任务
    ,{oled_task, 500,  0}      // 500ms周期执行OLED任务
    ,{btn_task,  5,    0}      // 5ms周期执行按键任务
    ,{uart_task, 5,    0}      // 5ms周期执行串口任务
};
```

---

## 常见问题与解决方案

### 1. OLED无显示
**可能原因**：
- I2C连接错误
- 电源供电问题
- 初始化失败

**解决方案**：
```c
// 检查I2C连接
// SCL -> PB8, SDA -> PB9

// 检查电源
// VCC -> 3.3V, GND -> GND

// 检查初始化
bsp_oled_init();
OLED_Init();
OLED_Clear();
OLED_Display_On();
```

### 2. I2C通信失败
**可能原因**：
- 总线冲突
- 时钟配置错误
- 地址错误

**解决方案**：
```c
// 复位I2C总线
I2C_Bus_Reset();

// 检查I2C地址
#define OLED_ADDRESS 0x78  // 7位地址0x3C左移1位

// 检查I2C时钟
i2c_clock_config(I2C0, 400000, I2C_DTCY_2);
```

### 3. 显示乱码
**可能原因**：
- 字体数据错误
- 坐标超出范围
- 字符编码问题

**解决方案**：
```c
// 检查坐标范围
if(x > 127) x = 0;
if(y > 3) y = 0;

// 检查字符范围
if(ch < ' ' || ch > '~') ch = ' ';

// 使用正确的字体数组
OLED_ShowChar(x, y, ch, 8);  // 使用8像素字体
```

### 4. 显示闪烁
**可能原因**：
- 刷新频率过高
- 清屏操作过频繁
- I2C传输不稳定

**解决方案**：
```c
// 控制刷新频率
static uint32_t last_update = 0;
if(get_system_ms() - last_update >= 100)  // 100ms刷新一次
{
    last_update = get_system_ms();
    // 更新显示
}

// 避免频繁清屏
// 只在必要时调用OLED_Clear()

// 添加I2C超时处理
uint32_t timeout = 10000;
while(i2c_flag_get(I2C0, I2C_FLAG_I2CBSY) && (--timeout > 0));
```

### 5. 部分区域不显示
**可能原因**：
- 页地址设置错误
- 列地址设置错误
- 显存数据错误

**解决方案**：
```c
// 正确设置显示位置
void OLED_Set_Position(uint8_t x, uint8_t y)
{
    OLED_Write_cmd(0xb0 + y);                    // 设置页地址(0-7)
    OLED_Write_cmd(((x & 0xf0) >> 4) | 0x10);   // 设置高4位列地址
    OLED_Write_cmd((x & 0x0f) | 0x00);          // 设置低4位列地址
}

// 检查页范围
if(y > 7) y = 7;

// 检查列范围
if(x > 127) x = 127;
```

---

## 总结

本文档详细介绍了OLED显示模块的完整配置和实现过程，包括：

1. **硬件层面**: I2C连接、电路配置、引脚分配
2. **配置层面**: CubeMX配置、I2C参数设置
3. **驱动层面**: BSP底层驱动、I2C通信协议
4. **显示层面**: 字体系统、图形绘制、坐标系统
5. **应用层面**: 菜单系统、数据显示、图形演示
6. **高级功能**: U8G2图形库集成
7. **实践层面**: 各种使用示例和问题解决

通过本文档，您可以：
- 完全掌握OLED模块的配置方法
- 理解I2C通信和SSD1306控制器原理
- 实现各种显示功能（文字、图形、菜单）
- 处理各种OLED显示问题
- 构建完整的人机交互界面

建议在实际使用时，根据应用需求选择合适的字体大小和刷新频率，并合理设计显示布局来提高用户体验。
