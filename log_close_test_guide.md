# Log文件手动关闭功能测试指南

## 功能概述

新增了手动关闭log文件的功能，支持通过串口命令精确控制log文件的关闭时机。

## 新增命令

### 命令格式
```
log[N] close
```

### 支持的命令
- `log0 close` - 关闭log0.txt文件
- `log1 close` - 关闭log1.txt文件  
- `log2 close` - 关闭log2.txt文件
- `log3 close` - 关闭log3.txt文件

## 测试步骤

### 1. 验证自动创建功能
1. **第一次上电**：
   - 系统启动后应显示：`====system init====`
   - 显示设备ID：`Device_ID:2025-CMIC-2025916750`
   - 显示：`====system ready====`
   - 此时应该没有自动创建log文件（因为未初始化）

2. **执行test命令**：
   - 输入：`test`
   - 系统应该创建log0.txt并写入初始化信息
   - 完成后自动创建log1.txt用于后续操作记录

### 2. 测试手动关闭功能
1. **测试正确的关闭命令**：
   - 输入：`log1 close`（假设当前打开的是log1.txt）
   - 预期响应：`log1.txt closed successfully`
   - 文件应该被正确关闭

2. **测试错误的log编号**：
   - 输入：`log0 close`（当前打开的是log1.txt）
   - 预期响应：`Current open file is log1.txt, not log0.txt`

3. **测试无效的log编号**：
   - 输入：`log5 close`
   - 预期响应：`Invalid log number. Use log0-log3`

4. **测试格式错误**：
   - 输入：`log close`
   - 预期响应：`Invalid format. Use: log[N] close (e.g., log0 close)`

5. **测试没有打开文件时的关闭**：
   - 在没有log文件打开时输入：`log0 close`
   - 预期响应：`No log file is currently open`

### 3. 验证上电自动创建序列
1. **第二次上电**：
   - 系统应该自动创建log2.txt（boot_count=2）
   - 可以使用`log2 close`关闭

2. **第三次上电**：
   - 系统应该自动创建log3.txt（boot_count=3）
   - 可以使用`log3 close`关闭

## 预期行为

### 文件关闭时的操作
1. 在文件中写入关闭标记：`=== log[N].txt manually closed ===`
2. 执行文件同步和关闭操作
3. 输出确认信息到串口

### 错误处理
- 命令格式验证
- log编号范围检查（0-3）
- 当前打开文件匹配验证
- 文件状态检查

## 扩展性

该实现为未来扩展预留了空间：
- 可以轻松支持更多log文件编号
- 可以添加其他log管理命令（如`log status`, `log list`等）
- 命令解析框架可以复用于其他功能

## 注意事项

1. 只能关闭当前打开的log文件
2. log编号必须在0-3范围内
3. 命令格式必须严格匹配：`log[数字] close`
4. 关闭后的文件不会自动重新打开，需要重新上电或执行相应操作
