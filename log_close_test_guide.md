# Log文件手动关闭功能测试指南

## 功能概述

新增了手动关闭log文件的功能，支持通过串口命令精确控制log文件的关闭时机。

## 新增命令

### 命令格式
```
log[N] close
```

### 支持的命令
- `log0 close` - 关闭log0.txt文件
- `log1 close` - 关闭log1.txt文件  
- `log2 close` - 关闭log2.txt文件
- `log3 close` - 关闭log3.txt文件

## 测试步骤

### 1. 验证自动创建功能
1. **第一次上电**：
   - 系统启动后应显示：`====system init====`
   - 显示设备ID：`Device_ID:2025-CMIC-2025916750`
   - 显示：`====system ready====`
   - 此时应该没有自动创建log文件（因为未初始化）

2. **执行test命令**：
   - 输入：`test`
   - 系统应该创建log0.txt并写入初始化信息
   - 完成后自动创建log1.txt用于后续操作记录

### 2. 测试手动关闭功能
1. **测试正确的关闭命令**：
   - 输入：`log1 close`（假设当前打开的是log1.txt）
   - 预期响应：`log1.txt closed successfully`
   - 文件应该被正确关闭

2. **测试错误的log编号**：
   - 输入：`log0 close`（当前打开的是log1.txt）
   - 预期响应：`Current open file is log1.txt, not log0.txt`

3. **测试无效的log编号**：
   - 输入：`log5 close`
   - 预期响应：`Invalid log number. Use log0-log3`

4. **测试格式错误**：
   - 输入：`log close`
   - 预期响应：`Invalid format. Use: log[N] close (e.g., log0 close)`

5. **测试没有打开文件时的关闭**：
   - 在没有log文件打开时输入：`log0 close`
   - 预期响应：`No log file is currently open`

### 3. 验证上电自动创建序列
1. **第二次上电**：
   - 系统应该自动创建log2.txt（boot_count=2）
   - 可以使用`log2 close`关闭

2. **第三次上电**：
   - 系统应该自动创建log3.txt（boot_count=3）
   - 可以使用`log3 close`关闭

## 预期行为

### 文件关闭时的操作
1. 在文件中写入关闭标记：`=== log[N].txt manually closed ===`
2. 执行文件同步和关闭操作
3. 输出确认信息到串口

### 错误处理
- 命令格式验证
- log编号范围检查（0-3）
- 当前打开文件匹配验证
- 文件状态检查

## 扩展性

该实现为未来扩展预留了空间：
- 可以轻松支持更多log文件编号
- 可以添加其他log管理命令（如`log status`, `log list`等）
- 命令解析框架可以复用于其他功能

## 注意事项

1. 只能关闭当前打开的log文件
2. log编号必须在0-3范围内
3. 命令格式必须严格匹配：`log[数字] close`
4. 关闭后的文件不会自动重新打开，需要重新上电或执行相应操作

## 修复的问题

### 问题：RTC Config命令无法识别
**原因**：process_command函数不完整，缺少大部分命令处理逻辑

**解决方案**：
1. 添加了缺失的全局变量：
   - `waiting_for_ratio_input`, `waiting_for_limit_input`, `waiting_for_rtc_input`
   - `current_ratio`, `current_limit`, `hide_mode`

2. 恢复了完整的命令处理逻辑：
   - `ratio` - 设置变比参数
   - `limit` - 设置阈值参数
   - `conf` - 从TF卡读取配置文件
   - `config save` - 保存参数到Flash
   - `config read` - 从Flash读取参数
   - `start` - 开始数据采集
   - `stop` - 停止数据采集
   - `hide` - 启用数据加密
   - `unhide` - 关闭数据加密
   - `RTC now` - 显示当前时间
   - `RTC Config` - 配置RTC时间

3. 添加了输入状态管理，支持多步骤命令处理

### 测试RTC Config命令
1. 输入：`RTC Config`
2. 预期响应：`Input Datatime`
3. 输入时间：`2025 01 15 14:30:00`
4. 预期响应：`RTC Config success` 和 `Time:2025-01-15 14:30:00`

现在所有命令都应该能正常工作了！

## 编译错误修复

### 问题：变量重复定义
**错误信息**：
```
error: #148: variable "current_ratio" has already been initialized
error: #148: variable "current_limit" has already been initialized
error: #148: variable "waiting_for_ratio_input" has already been initialized
```

**原因**：在添加新功能时，意外地重复定义了已存在的全局变量。

**解决方案**：
1. 删除了第87-94行的重复变量定义
2. 保留了第235-246行的原始变量定义
3. 添加了文件末尾的换行符

### 编译成功确认
- 所有编译错误已修复
- 代码结构完整
- 功能实现正确

现在可以正常编译和测试所有功能了！

## 特殊的log0处理逻辑

### 实际上电流程
1. **第一次上电**：RTC配置等信息写入Flash（无TF卡）
2. **第二次上电**：插入TF卡，需要先完成log0.txt，再进行log1.txt操作
3. **第三次上电**：创建log2.txt
4. **第四次上电**：创建log3.txt

### 特殊命令行为

#### `log0 close` 命令的特殊处理
当输入`log0 close`时，系统会：
1. **检查当前状态**：如果当前不是log0.txt
2. **自动创建log0.txt**：临时设置boot_count=0
3. **写入Flash数据**：调用`flush_pre_test_buffer_to_log()`
4. **完成log0.txt**：写入完成标记并关闭
5. **恢复正常状态**：恢复原boot_count并创建对应的log文件

**测试示例**：
```
当前状态：log1.txt打开
输入：log0 close
输出：
Creating log0.txt first...
log0.txt created and closed successfully
log1.txt opened for current operations
```

#### 新增命令

**`log[N] open` 命令**：
- `log1 open` - 打开log1.txt文件
- `log2 open` - 打开log2.txt文件
- `log3 open` - 打开log3.txt文件
- 注意：log0不支持open命令，只能通过`log0 close`处理

**测试示例**：
```
输入：log1 open
输出：log1.txt opened successfully

输入：log0 open
输出：log0.txt is handled by 'log0 close' command
```

### 完整命令列表
- `log0 close` - 特殊处理：创建并完成log0.txt，然后打开对应的log文件
- `log1 close` - 关闭log1.txt
- `log2 close` - 关闭log2.txt
- `log3 close` - 关闭log3.txt
- `log1 open` - 打开log1.txt
- `log2 open` - 打开log2.txt
- `log3 open` - 打开log3.txt

这样的设计完美解决了log0.txt需要在第二次上电时处理的问题！
