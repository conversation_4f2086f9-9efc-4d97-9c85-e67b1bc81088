# Flash存储模块保姆级配置与实现文档

## 目录
1. [硬件连接说明](#硬件连接说明)
2. [CubeMX配置步骤](#cubemx配置步骤)
3. [SPI配置详解](#spi配置详解)
4. [底层BSP配置](#底层bsp配置)
5. [Flash工作原理](#flash工作原理)
6. [基本操作函数](#基本操作函数)
7. [高级功能实现](#高级功能实现)
8. [应用层实现](#应用层实现)
9. [使用示例](#使用示例)
10. [常见问题与解决方案](#常见问题与解决方案)

---

## 硬件连接说明

### Flash硬件配置
本开发板使用SPI接口的GD25Q系列Flash存储芯片：

| 功能 | GPIO端口 | GPIO引脚 | SPI功能 | 说明 |
|------|----------|----------|---------|------|
| SPI_SCK | GPIOB | PB3 | SPI1_SCK | 时钟线 |
| SPI_MISO | GPIOB | PB4 | SPI1_MISO | 主入从出 |
| SPI_MOSI | GPIOB | PB5 | SPI1_MOSI | 主出从入 |
| SPI_CS | GPIOB | PB12 | GPIO_Output | 片选信号 |

### Flash芯片规格
- **型号**: GD25Q64C/GD25Q128C
- **容量**: 8MB/16MB
- **接口**: SPI (最高50MHz)
- **电源**: 3.3V
- **扇区大小**: 4KB
- **页大小**: 256字节
- **擦除时间**: 扇区擦除45ms，整片擦除20s

### 电路连接
```
MCU          Flash
PB3(SCK)  --- CLK
PB4(MISO) --- DO
PB5(MOSI) --- DI
PB12(CS)  --- CS#
3.3V      --- VCC
GND       --- GND
```

---

## CubeMX配置步骤

### 1. 基本SPI配置

#### 启用SPI1
1. 在Pinout视图中找到PB3、PB4、PB5
2. 设置PB3为SPI1_SCK
3. 设置PB4为SPI1_MISO
4. 设置PB5为SPI1_MOSI
5. 设置PB12为GPIO_Output（片选）

#### 配置SPI参数
1. 进入Configuration → Connectivity → SPI1
2. 设置基本参数：
   ```
   Mode: Full-Duplex Master
   Hardware NSS Signal: Disable
   Frame Format: Motorola
   Data Size: 8 Bits
   First Bit: MSB First
   ```

#### 时钟和极性配置
```
Prescaler: 8 (10.5MHz，适合Flash通信)
Clock Polarity (CPOL): High
Clock Phase (CPHA): 2 Edge
CRC Calculation: Disabled
NSS Signal Type: Software
```

### 2. GPIO配置

#### 配置片选引脚
```
PB12 (Flash_CS):
  Mode: GPIO_Output
  GPIO Pull-up/Pull-down: No pull-up and no pull-down
  Maximum output speed: High
  User Label: Flash_CS
```

#### 确认SPI引脚配置
```
PB3 (SPI1_SCK):
  Mode: Alternate Function Push Pull
  Pull-up/Pull-down: No pull-up and no pull-down
  Maximum output speed: High
  
PB4 (SPI1_MISO):
  Mode: Alternate Function Push Pull
  Pull-up/Pull-down: Pull-up
  Maximum output speed: High
  
PB5 (SPI1_MOSI):
  Mode: Alternate Function Push Pull
  Pull-up/Pull-down: No pull-up and no pull-down
  Maximum output speed: High
```

---

## SPI配置详解

### 1. SPI工作原理
SPI是全双工同步串行通信协议：

```
主机发送: MOSI ──→ 从机接收
主机接收: MISO ←── 从机发送
时钟信号: SCK  ──→ 同步时钟
片选信号: CS   ──→ 设备选择
```

### 2. Flash SPI时序
```
CS ──┐     ┌─────────────────────────────────┐
     └─────┘                                 └───

SCK ───┐ ┌─┐ ┌─┐ ┌─┐ ┌─┐ ┌─┐ ┌─┐ ┌─┐ ┌─┐───
       └─┘ └─┘ └─┘ └─┘ └─┘ └─┘ └─┘ └─┘ └─┘

MOSI ──[CMD][A23-A16][A15-A8][A7-A0][DATA]──

MISO ──────────────────────────────[DATA]────
```

### 3. Flash命令集
```c
/* Flash命令定义 */
#define WREN            0x06    // 写使能
#define WRDI            0x04    // 写禁止
#define RDSR            0x05    // 读状态寄存器
#define WRSR            0x01    // 写状态寄存器
#define READ            0x03    // 读数据
#define FAST_READ       0x0B    // 快速读数据
#define PP              0x02    // 页编程
#define SE              0x20    // 扇区擦除(4KB)
#define BE              0xC7    // 整片擦除
#define RDID            0x9F    // 读ID
#define DUMMY_BYTE      0xFF    // 虚拟字节
```

---

## 底层BSP配置

### 1. 头文件定义 (mcu_cmic_gd32f470vet6.h)

```c
/* Flash SPI相关定义 */
#define SPI_FLASH           SPI1
#define SPI_FLASH_PORT      GPIOB
#define SPI_FLASH_CLK_PORT  RCU_GPIOB
#define SPI_FLASH_SCK_PIN   GPIO_PIN_3
#define SPI_FLASH_MISO_PIN  GPIO_PIN_4
#define SPI_FLASH_MOSI_PIN  GPIO_PIN_5
#define SPI_FLASH_CS_PIN    GPIO_PIN_12

/* Flash控制宏定义 */
#define SPI_FLASH_CS_LOW()  gpio_bit_reset(SPI_FLASH_PORT, SPI_FLASH_CS_PIN)
#define SPI_FLASH_CS_HIGH() gpio_bit_set(SPI_FLASH_PORT, SPI_FLASH_CS_PIN)

/* Flash规格定义 */
#define SPI_FLASH_PAGE_SIZE         256
#define SPI_FLASH_SECTOR_SIZE       4096
#define SPI_FLASH_SECTOR_COUNT      2048
#define SPI_FLASH_PAGE_COUNT        32768
#define SPI_FLASH_SIZE              (8 * 1024 * 1024)  // 8MB

/* 函数声明 */
void bsp_spi_flash_init(void);
```

### 2. BSP初始化函数 (mcu_cmic_gd32f470vet6.c)

```c
void bsp_spi_flash_init(void)
{
    spi_parameter_struct spi_init_struct;
    
    /* 使能GPIO时钟 */
    rcu_periph_clock_enable(SPI_FLASH_CLK_PORT);
    /* 使能SPI1时钟 */
    rcu_periph_clock_enable(RCU_SPI1);
    
    /* 配置GPIO复用功能 */
    gpio_af_set(SPI_FLASH_PORT, GPIO_AF_5, SPI_FLASH_SCK_PIN);
    gpio_af_set(SPI_FLASH_PORT, GPIO_AF_5, SPI_FLASH_MISO_PIN);
    gpio_af_set(SPI_FLASH_PORT, GPIO_AF_5, SPI_FLASH_MOSI_PIN);
    
    /* 配置SPI引脚 */
    gpio_mode_set(SPI_FLASH_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, 
                  SPI_FLASH_SCK_PIN | SPI_FLASH_MOSI_PIN);
    gpio_output_options_set(SPI_FLASH_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, 
                           SPI_FLASH_SCK_PIN | SPI_FLASH_MOSI_PIN);
    
    gpio_mode_set(SPI_FLASH_PORT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, SPI_FLASH_MISO_PIN);
    gpio_output_options_set(SPI_FLASH_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, SPI_FLASH_MISO_PIN);
    
    /* 配置片选引脚 */
    gpio_mode_set(SPI_FLASH_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, SPI_FLASH_CS_PIN);
    gpio_output_options_set(SPI_FLASH_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, SPI_FLASH_CS_PIN);
    
    /* 配置SPI参数 */
    spi_init_struct.trans_mode           = SPI_TRANSMODE_FULLDUPLEX;
    spi_init_struct.device_mode          = SPI_MASTER;
    spi_init_struct.frame_size           = SPI_FRAMESIZE_8BIT;
    spi_init_struct.clock_polarity_phase = SPI_CK_PL_HIGH_PH_2EDGE;
    spi_init_struct.nss                  = SPI_NSS_SOFT;
    spi_init_struct.prescale             = SPI_PSC_8;
    spi_init_struct.endian               = SPI_ENDIAN_MSB;
    spi_init(SPI1, &spi_init_struct);
    
    /* 初始化SPI Flash */
    spi_flash_init();
}
```

---

## Flash工作原理

### 1. Flash存储结构
```
Flash芯片 (8MB)
├── 扇区0 (4KB)
│   ├── 页0 (256B)
│   ├── 页1 (256B)
│   ├── ...
│   └── 页15 (256B)
├── 扇区1 (4KB)
├── ...
└── 扇区2047 (4KB)
```

### 2. 操作特性
- **读操作**: 可以随机读取任意地址
- **写操作**: 只能将1变为0，必须按页(256字节)写入
- **擦除操作**: 将0变为1，必须按扇区(4KB)或整片擦除
- **写保护**: 支持软件和硬件写保护

### 3. 操作流程
```
写数据流程:
1. 发送写使能命令(WREN)
2. 发送页编程命令(PP) + 地址 + 数据
3. 等待写操作完成

擦除流程:
1. 发送写使能命令(WREN)
2. 发送扇区擦除命令(SE) + 地址
3. 等待擦除操作完成

读数据流程:
1. 发送读命令(READ) + 地址
2. 连续读取数据
3. 拉高CS结束读取
```

---

## 基本操作函数

### 1. 底层通信函数

```c
/*!
    \brief      发送单个字节并接收响应
    \param[in]  byte: 要发送的字节
    \param[out] none
    \retval     接收到的字节
*/
uint8_t spi_flash_send_byte(uint8_t byte)
{
    /* 等待发送缓冲区空 */
    while(RESET == spi_flag_get(SPI_FLASH, SPI_FLAG_TBE));
    
    /* 发送数据 */
    spi_data_transmit(SPI_FLASH, byte);
    
    /* 等待接收缓冲区非空 */
    while(RESET == spi_flag_get(SPI_FLASH, SPI_FLAG_RBNE));
    
    /* 返回接收到的数据 */
    return spi_data_receive(SPI_FLASH);
}

/*!
    \brief      使能Flash写操作
    \param[in]  none
    \param[out] none
    \retval     none
*/
void spi_flash_write_enable(void)
{
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(WREN);
    SPI_FLASH_CS_HIGH();
}

/*!
    \brief      等待Flash写操作完成
    \param[in]  none
    \param[out] none
    \retval     none
*/
void spi_flash_wait_for_write_end(void)
{
    uint8_t status = 0;
    
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(RDSR);
    
    do {
        status = spi_flash_send_byte(DUMMY_BYTE);
    } while ((status & 0x01) == 0x01);  // 检查忙标志位
    
    SPI_FLASH_CS_HIGH();
}
```

### 2. Flash ID读取

```c
/*!
    \brief      读取Flash ID
    \param[in]  none
    \param[out] none
    \retval     Flash ID (24位)
*/
uint32_t spi_flash_read_id(void)
{
    uint32_t temp = 0, temp0 = 0, temp1 = 0, temp2 = 0;
    
    SPI_FLASH_CS_LOW();
    
    spi_flash_send_byte(RDID);
    temp0 = spi_flash_send_byte(DUMMY_BYTE);
    temp1 = spi_flash_send_byte(DUMMY_BYTE);
    temp2 = spi_flash_send_byte(DUMMY_BYTE);
    
    SPI_FLASH_CS_HIGH();
    
    temp = (temp0 << 16) | (temp1 << 8) | temp2;
    
    return temp;
}
```

### 3. 扇区擦除

```c
/*!
    \brief      擦除Flash扇区
    \param[in]  sector_addr: 扇区地址
    \param[out] none
    \retval     none
*/
void spi_flash_sector_erase(uint32_t sector_addr)
{
    spi_flash_write_enable();
    
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(SE);
    spi_flash_send_byte((sector_addr & 0xFF0000) >> 16);
    spi_flash_send_byte((sector_addr & 0xFF00) >> 8);
    spi_flash_send_byte(sector_addr & 0xFF);
    SPI_FLASH_CS_HIGH();
    
    spi_flash_wait_for_write_end();
}

/*!
    \brief      整片擦除Flash
    \param[in]  none
    \param[out] none
    \retval     none
*/
void spi_flash_bulk_erase(void)
{
    spi_flash_write_enable();
    
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(BE);
    SPI_FLASH_CS_HIGH();
    
    spi_flash_wait_for_write_end();
}
```

### 4. 页编程

```c
/*!
    \brief      Flash页编程
    \param[in]  pbuffer: 数据缓冲区指针
    \param[in]  write_addr: 写入地址
    \param[in]  num_byte_to_write: 写入字节数(≤256)
    \param[out] none
    \retval     none
*/
void spi_flash_page_write(uint8_t *pbuffer, uint32_t write_addr, uint16_t num_byte_to_write)
{
    spi_flash_write_enable();
    
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(PP);
    spi_flash_send_byte((write_addr & 0xFF0000) >> 16);
    spi_flash_send_byte((write_addr & 0xFF00) >> 8);
    spi_flash_send_byte(write_addr & 0xFF);
    
    while (num_byte_to_write--)
    {
        spi_flash_send_byte(*pbuffer);
        pbuffer++;
    }
    
    SPI_FLASH_CS_HIGH();
    
    spi_flash_wait_for_write_end();
}
```

### 5. 数据读取

```c
/*!
    \brief      从Flash读取数据
    \param[in]  pbuffer: 数据缓冲区指针
    \param[in]  read_addr: 读取地址
    \param[in]  num_byte_to_read: 读取字节数
    \param[out] none
    \retval     none
*/
void spi_flash_buffer_read(uint8_t *pbuffer, uint32_t read_addr, uint16_t num_byte_to_read)
{
    SPI_FLASH_CS_LOW();
    
    spi_flash_send_byte(READ);
    spi_flash_send_byte((read_addr & 0xFF0000) >> 16);
    spi_flash_send_byte((read_addr & 0xFF00) >> 8);
    spi_flash_send_byte(read_addr & 0xFF);
    
    while (num_byte_to_read--)
    {
        *pbuffer = spi_flash_send_byte(DUMMY_BYTE);
        pbuffer++;
    }
    
    SPI_FLASH_CS_HIGH();
}
```

---

## 高级功能实现

### 1. 跨页写入

```c
/*!
    \brief      Flash缓冲区写入(支持跨页)
    \param[in]  pbuffer: 数据缓冲区指针
    \param[in]  write_addr: 写入地址
    \param[in]  num_byte_to_write: 写入字节数
    \param[out] none
    \retval     none
*/
void spi_flash_buffer_write(uint8_t *pbuffer, uint32_t write_addr, uint16_t num_byte_to_write)
{
    uint8_t num_of_page = 0, num_of_single = 0, addr = 0, count = 0, temp = 0;
    
    addr = write_addr % SPI_FLASH_PAGE_SIZE;
    count = SPI_FLASH_PAGE_SIZE - addr;
    num_of_page = num_byte_to_write / SPI_FLASH_PAGE_SIZE;
    num_of_single = num_byte_to_write % SPI_FLASH_PAGE_SIZE;
    
    if (addr == 0) /* 地址对齐到页边界 */
    {
        if (num_of_page == 0) /* 数据长度小于页大小 */
        {
            spi_flash_page_write(pbuffer, write_addr, num_byte_to_write);
        }
        else /* 数据长度大于页大小 */
        {
            while (num_of_page--)
            {
                spi_flash_page_write(pbuffer, write_addr, SPI_FLASH_PAGE_SIZE);
                write_addr += SPI_FLASH_PAGE_SIZE;
                pbuffer += SPI_FLASH_PAGE_SIZE;
            }
            
            spi_flash_page_write(pbuffer, write_addr, num_of_single);
        }
    }
    else /* 地址未对齐到页边界 */
    {
        if (num_of_page == 0)
        {
            if (num_of_single > count) /* 跨页写入 */
            {
                temp = num_of_single - count;
                
                spi_flash_page_write(pbuffer, write_addr, count);
                write_addr += count;
                pbuffer += count;
                
                spi_flash_page_write(pbuffer, write_addr, temp);
            }
            else
            {
                spi_flash_page_write(pbuffer, write_addr, num_byte_to_write);
            }
        }
        else /* 多页写入 */
        {
            num_byte_to_write -= count;
            num_of_page = num_byte_to_write / SPI_FLASH_PAGE_SIZE;
            num_of_single = num_byte_to_write % SPI_FLASH_PAGE_SIZE;
            
            spi_flash_page_write(pbuffer, write_addr, count);
            write_addr += count;
            pbuffer += count;
            
            while (num_of_page--)
            {
                spi_flash_page_write(pbuffer, write_addr, SPI_FLASH_PAGE_SIZE);
                write_addr += SPI_FLASH_PAGE_SIZE;
                pbuffer += SPI_FLASH_PAGE_SIZE;
            }
            
            if (num_of_single != 0)
            {
                spi_flash_page_write(pbuffer, write_addr, num_of_single);
            }
        }
    }
}
```

### 2. 安全写入(带擦除)

```c
/*!
    \brief      安全写入数据(自动擦除)
    \param[in]  pbuffer: 数据缓冲区指针
    \param[in]  write_addr: 写入地址
    \param[in]  num_byte_to_write: 写入字节数
    \param[out] none
    \retval     0-成功，其他-失败
*/
int spi_flash_safe_write(uint8_t *pbuffer, uint32_t write_addr, uint16_t num_byte_to_write)
{
    uint32_t sector_addr = write_addr & 0xFFFFF000;  // 扇区对齐
    uint32_t end_addr = write_addr + num_byte_to_write;
    uint32_t current_sector = sector_addr;
    
    /* 检查地址范围 */
    if (end_addr > SPI_FLASH_SIZE)
    {
        return -1;  // 地址超出范围
    }
    
    /* 擦除涉及的扇区 */
    while (current_sector < end_addr)
    {
        spi_flash_sector_erase(current_sector);
        current_sector += SPI_FLASH_SECTOR_SIZE;
    }
    
    /* 写入数据 */
    spi_flash_buffer_write(pbuffer, write_addr, num_byte_to_write);
    
    return 0;
}
```

### 3. 数据校验

```c
/*!
    \brief      校验Flash数据
    \param[in]  pbuffer: 比较数据缓冲区
    \param[in]  read_addr: 读取地址
    \param[in]  num_byte_to_read: 读取字节数
    \param[out] none
    \retval     0-校验成功，其他-校验失败
*/
int spi_flash_verify_data(uint8_t *pbuffer, uint32_t read_addr, uint16_t num_byte_to_read)
{
    uint8_t read_buffer[256];
    uint16_t bytes_to_verify;
    uint16_t offset = 0;
    
    while (num_byte_to_read > 0)
    {
        bytes_to_verify = (num_byte_to_read > 256) ? 256 : num_byte_to_read;
        
        spi_flash_buffer_read(read_buffer, read_addr + offset, bytes_to_verify);
        
        for (uint16_t i = 0; i < bytes_to_verify; i++)
        {
            if (read_buffer[i] != pbuffer[offset + i])
            {
                return -1;  // 校验失败
            }
        }
        
        offset += bytes_to_verify;
        num_byte_to_read -= bytes_to_verify;
    }
    
    return 0;  // 校验成功
}
```

---

## 应用层实现

### 1. 应用层头文件 (flash_app.h)

```c
#ifndef __FLASH_APP_H__
#define __FLASH_APP_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 配置参数结构体 */
typedef struct {
    uint32_t magic;                 // 魔数，用于校验
    uint8_t sampling_enabled;       // 采样使能
    uint8_t hide_mode;              // 加密模式
    uint16_t sampling_period;       // 采样周期(秒)
    uint16_t voltage_ratio;         // 电压比例(×100)
    uint16_t voltage_limit;         // 电压限制(×100)
    uint32_t sample_count;          // 采样计数
    uint32_t error_count;           // 错误计数
    uint32_t crc32;                 // CRC校验
} system_config_t;

/* 配置存储地址 */
#define CONFIG_FLASH_ADDR       0x000000    // 配置存储地址
#define CONFIG_BACKUP_ADDR      0x001000    // 备份配置地址
#define DATA_FLASH_START_ADDR   0x002000    // 数据存储起始地址
#define CONFIG_MAGIC            0x12345678  // 配置魔数

/* 函数声明 */
void flash_task(void);
int flash_save_config(system_config_t *config);
int flash_read_config(system_config_t *config);
int flash_save_data(uint8_t *data, uint32_t length);
int flash_format(void);

#ifdef __cplusplus
}
#endif

#endif
```

### 2. 应用层实现文件 (flash_app.c)

```c
#include "mcu_cmic_gd32f470vet6.h"
#include "flash_app.h"
#include <string.h>

/* CRC32计算表 */
static const uint32_t crc32_table[256] = {
    // CRC32查找表...
};

/*!
    \brief      计算CRC32
    \param[in]  data: 数据指针
    \param[in]  length: 数据长度
    \param[out] none
    \retval     CRC32值
*/
uint32_t calculate_crc32(uint8_t *data, uint32_t length)
{
    uint32_t crc = 0xFFFFFFFF;
    
    for (uint32_t i = 0; i < length; i++)
    {
        crc = crc32_table[(crc ^ data[i]) & 0xFF] ^ (crc >> 8);
    }
    
    return crc ^ 0xFFFFFFFF;
}

/*!
    \brief      保存系统配置到Flash
    \param[in]  config: 配置结构体指针
    \param[out] none
    \retval     0-成功，其他-失败
*/
int flash_save_config(system_config_t *config)
{
    uint8_t buffer[sizeof(system_config_t)];
    
    /* 设置魔数 */
    config->magic = CONFIG_MAGIC;
    
    /* 计算CRC */
    config->crc32 = calculate_crc32((uint8_t*)config, sizeof(system_config_t) - 4);
    
    /* 复制到缓冲区 */
    memcpy(buffer, config, sizeof(system_config_t));
    
    /* 写入主配置区 */
    if (spi_flash_safe_write(buffer, CONFIG_FLASH_ADDR, sizeof(system_config_t)) != 0)
    {
        return -1;
    }
    
    /* 写入备份配置区 */
    if (spi_flash_safe_write(buffer, CONFIG_BACKUP_ADDR, sizeof(system_config_t)) != 0)
    {
        return -2;
    }
    
    printf("Configuration saved to Flash\r\n");
    return 0;
}

/*!
    \brief      从Flash读取系统配置
    \param[in]  config: 配置结构体指针
    \param[out] none
    \retval     0-成功，其他-失败
*/
int flash_read_config(system_config_t *config)
{
    uint8_t buffer[sizeof(system_config_t)];
    uint32_t calculated_crc;
    
    /* 尝试读取主配置 */
    spi_flash_buffer_read(buffer, CONFIG_FLASH_ADDR, sizeof(system_config_t));
    memcpy(config, buffer, sizeof(system_config_t));
    
    /* 检查魔数 */
    if (config->magic != CONFIG_MAGIC)
    {
        /* 尝试读取备份配置 */
        spi_flash_buffer_read(buffer, CONFIG_BACKUP_ADDR, sizeof(system_config_t));
        memcpy(config, buffer, sizeof(system_config_t));
        
        if (config->magic != CONFIG_MAGIC)
        {
            printf("No valid configuration found\r\n");
            return -1;
        }
    }
    
    /* 校验CRC */
    calculated_crc = calculate_crc32((uint8_t*)config, sizeof(system_config_t) - 4);
    if (calculated_crc != config->crc32)
    {
        printf("Configuration CRC error\r\n");
        return -2;
    }
    
    printf("Configuration loaded from Flash\r\n");
    return 0;
}

/*!
    \brief      Flash任务函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void flash_task(void)
{
    static uint32_t last_check_time = 0;
    uint32_t current_time = get_system_ms();
    
    /* 每分钟检查一次Flash状态 */
    if (current_time - last_check_time >= 60000)
    {
        last_check_time = current_time;
        
        /* 读取Flash ID验证通信 */
        uint32_t flash_id = spi_flash_read_id();
        if (flash_id == 0x000000 || flash_id == 0xFFFFFF)
        {
            printf("Flash communication error, ID: 0x%06lX\r\n", flash_id);
        }
    }
}
```

---

## 使用示例

### 1. 基本初始化和使用
```c
int main(void)
{
    system_config_t config;
    
    // 系统初始化
    system_init();
    
    // Flash初始化
    bsp_spi_flash_init();
    
    // 读取Flash ID
    uint32_t flash_id = spi_flash_read_id();
    printf("Flash ID: 0x%06lX\r\n", flash_id);
    
    // 加载配置
    if (flash_read_config(&config) != 0)
    {
        // 使用默认配置
        memset(&config, 0, sizeof(config));
        config.sampling_period = 5;
        config.voltage_ratio = 199;
        config.voltage_limit = 1011;
        
        // 保存默认配置
        flash_save_config(&config);
    }
    
    // 主循环
    while(1)
    {
        flash_task();
        delay_ms(10);
    }
}
```

### 2. 数据记录示例
```c
void record_sample_data(uint32_t voltage_mv, uint32_t timestamp)
{
    typedef struct {
        uint32_t timestamp;
        uint32_t voltage_mv;
        uint16_t adc_raw;
        uint8_t status;
        uint8_t reserved;
    } sample_record_t;
    
    static uint32_t record_address = DATA_FLASH_START_ADDR;
    sample_record_t record;
    
    /* 填充记录数据 */
    record.timestamp = timestamp;
    record.voltage_mv = voltage_mv;
    record.adc_raw = adc_value[0];
    record.status = g_system_status.sampling_enabled ? 1 : 0;
    record.reserved = 0;
    
    /* 检查地址范围 */
    if (record_address + sizeof(record) >= SPI_FLASH_SIZE)
    {
        printf("Flash storage full\r\n");
        return;
    }
    
    /* 写入记录 */
    if (spi_flash_safe_write((uint8_t*)&record, record_address, sizeof(record)) == 0)
    {
        record_address += sizeof(record);
        printf("Data recorded at 0x%06lX\r\n", record_address - sizeof(record));
    }
    else
    {
        printf("Flash write error\r\n");
    }
}
```

### 3. 配置管理示例
```c
void handle_config_commands(char *cmd)
{
    system_config_t config;
    
    if (strcmp(cmd, "config save") == 0)
    {
        if (flash_save_config(&g_system_status) == 0)
        {
            printf("Configuration saved\r\n");
        }
        else
        {
            printf("Save failed\r\n");
        }
    }
    else if (strcmp(cmd, "config load") == 0)
    {
        if (flash_read_config(&config) == 0)
        {
            memcpy(&g_system_status, &config, sizeof(config));
            printf("Configuration loaded\r\n");
        }
        else
        {
            printf("Load failed\r\n");
        }
    }
    else if (strcmp(cmd, "config reset") == 0)
    {
        /* 恢复默认配置 */
        memset(&g_system_status, 0, sizeof(g_system_status));
        g_system_status.sampling_period = 5;
        g_system_status.voltage_ratio = 199;
        g_system_status.voltage_limit = 1011;
        
        flash_save_config(&g_system_status);
        printf("Configuration reset to default\r\n");
    }
}
```

### 4. Flash测试示例
```c
void flash_test(void)
{
    uint8_t write_buffer[256];
    uint8_t read_buffer[256];
    uint32_t test_addr = 0x100000;  // 测试地址
    
    printf("Starting Flash test...\r\n");
    
    /* 准备测试数据 */
    for (int i = 0; i < 256; i++)
    {
        write_buffer[i] = i;
    }
    
    /* 擦除测试扇区 */
    spi_flash_sector_erase(test_addr);
    
    /* 写入测试数据 */
    spi_flash_buffer_write(write_buffer, test_addr, 256);
    
    /* 读取测试数据 */
    spi_flash_buffer_read(read_buffer, test_addr, 256);
    
    /* 校验数据 */
    if (spi_flash_verify_data(write_buffer, test_addr, 256) == 0)
    {
        printf("Flash test PASSED\r\n");
    }
    else
    {
        printf("Flash test FAILED\r\n");
    }
}
```

### 5. 在调度器中使用
```c
// 在scheduler.c中添加Flash任务
static task_t scheduler_task[] =
{
     {led_task,   1,    0}      // 1ms周期执行LED任务
    ,{flash_task, 60000, 0}     // 60s周期执行Flash任务
    ,{adc_task,   100,  0}      // 100ms周期执行ADC任务
    ,{other_task, 500,  0}      // 其他任务
};
```

---

## 常见问题与解决方案

### 1. Flash无法识别
**可能原因**：
- SPI配置错误
- 连接线路问题
- 电源供电不稳定

**解决方案**：
```c
// 检查Flash ID
uint32_t flash_id = spi_flash_read_id();
printf("Flash ID: 0x%06lX\r\n", flash_id);

// 常见Flash ID
// GD25Q64C: 0xC84017
// GD25Q128C: 0xC84018
// W25Q64: 0xEF4017
// W25Q128: 0xEF4018

if (flash_id == 0x000000 || flash_id == 0xFFFFFF)
{
    printf("Flash communication failed\r\n");
    // 检查硬件连接和SPI配置
}
```

### 2. 写入失败
**可能原因**：
- 未发送写使能命令
- Flash处于写保护状态
- 地址未对齐

**解决方案**：
```c
// 检查写保护状态
uint8_t status = spi_flash_read_status();
if (status & 0x1C)  // 检查写保护位
{
    printf("Flash is write protected\r\n");
    // 清除写保护
    spi_flash_write_status(0x00);
}

// 确保写使能
spi_flash_write_enable();
uint8_t status_after = spi_flash_read_status();
if (!(status_after & 0x02))  // 检查WEL位
{
    printf("Write enable failed\r\n");
}
```

### 3. 数据校验失败
**可能原因**：
- 写入过程中断
- Flash坏块
- 电源干扰

**解决方案**：
```c
// 重试机制
int flash_write_with_retry(uint8_t *data, uint32_t addr, uint16_t len)
{
    int retry_count = 3;
    
    while (retry_count--)
    {
        spi_flash_buffer_write(data, addr, len);
        
        if (spi_flash_verify_data(data, addr, len) == 0)
        {
            return 0;  // 成功
        }
        
        printf("Write retry %d\r\n", 3 - retry_count);
        delay_ms(10);
    }
    
    return -1;  // 失败
}
```

### 4. 擦除时间过长
**可能原因**：
- Flash老化
- 温度影响
- 电压不稳定

**解决方案**：
```c
// 带超时的擦除等待
int spi_flash_wait_with_timeout(uint32_t timeout_ms)
{
    uint32_t start_time = get_system_ms();
    uint8_t status;
    
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(RDSR);
    
    do {
        status = spi_flash_send_byte(DUMMY_BYTE);
        
        if (get_system_ms() - start_time > timeout_ms)
        {
            SPI_FLASH_CS_HIGH();
            return -1;  // 超时
        }
        
    } while (status & 0x01);
    
    SPI_FLASH_CS_HIGH();
    return 0;  // 成功
}
```

### 5. 存储空间管理
**可能原因**：
- 存储空间不足
- 地址分配混乱
- 垃圾回收不及时

**解决方案**：
```c
// 存储空间管理
typedef struct {
    uint32_t config_start;      // 配置区起始地址
    uint32_t config_size;       // 配置区大小
    uint32_t data_start;        // 数据区起始地址
    uint32_t data_size;         // 数据区大小
    uint32_t current_addr;      // 当前写入地址
} flash_layout_t;

flash_layout_t flash_layout = {
    .config_start = 0x000000,
    .config_size = 0x002000,    // 8KB配置区
    .data_start = 0x002000,
    .data_size = 0x7FE000,      // 剩余数据区
    .current_addr = 0x002000
};

int check_storage_space(uint32_t required_size)
{
    uint32_t available = flash_layout.data_start + flash_layout.data_size - flash_layout.current_addr;
    
    if (available < required_size)
    {
        printf("Storage space insufficient: %lu bytes available, %lu required\r\n", 
               available, required_size);
        return -1;
    }
    
    return 0;
}
```

---

## 总结

本文档详细介绍了Flash存储模块的完整配置和实现过程，包括：

1. **硬件层面**: SPI接口连接、Flash芯片规格、电路设计
2. **配置层面**: CubeMX配置、SPI参数设置、GPIO配置
3. **驱动层面**: BSP底层驱动、SPI通信协议、Flash命令集
4. **功能层面**: 基本读写操作、扇区擦除、页编程
5. **应用层面**: 配置管理、数据记录、存储空间管理
6. **高级功能**: 跨页写入、安全写入、数据校验
7. **实践层面**: 各种使用示例和问题解决

通过本文档，您可以：
- 完全掌握Flash存储模块的配置方法
- 理解SPI通信和Flash工作原理
- 实现可靠的数据存储功能
- 处理各种Flash操作问题
- 构建完整的配置管理系统

Flash存储是嵌入式系统中重要的非易失性存储解决方案，正确使用Flash可以实现参数保存、数据记录、固件升级等重要功能。建议在实际使用时注意写入次数限制和数据备份策略。
