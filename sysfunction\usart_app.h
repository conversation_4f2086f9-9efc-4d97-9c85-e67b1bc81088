/* 许可证
* 公司: MCUSTUDIO
* 作者: Ahypnis.
* 版本: V0.10
* 时间: 2025/06/05
* 说明: USART应用程序头文件
*/
#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// 全局变量声明
extern uint32_t current_ratio;
extern uint32_t current_limit;

// 函数声明
int my_printf(uint32_t usart_periph, const char *format, ...);
uint8_t test_flash(void);
uint8_t test_tf_card(void);
uint8_t parse_and_set_rtc_time(char* time_str);
uint8_t process_ratio_input(char* input_str);
uint8_t process_limit_input(char* input_str);
uint8_t save_config_to_flash(void);
uint8_t read_config_from_flash(void);
uint8_t load_config_from_flash_on_startup(void);

// 全局变量
extern uint8_t pre_test_logging;
extern uint16_t pre_test_buffer_pos;

// 测试前缓冲区函数（基于Flash存储）
void add_to_pre_test_buffer(const char* message);
void flush_pre_test_buffer_to_log(void);
void load_pre_test_buffer_from_flash(void);
void save_pre_test_buffer_to_flash(void);
void clear_pre_test_buffer(void);
uint8_t write_sampling_cycle_to_flash(uint8_t cycle_seconds);
uint8_t read_sampling_cycle_from_flash(void);
void start_sampling(void);
void stop_sampling(void);
uint8_t get_sampling_status(void);
uint32_t get_sampling_period(void);
void toggle_sampling(void);
void set_sampling_period(uint8_t period_seconds);
void sampling_task(void);
uint32_t rtc_to_unix_timestamp(void);
uint8_t write_device_id_to_flash(void);
uint8_t read_and_display_device_id(void);
uint8_t write_config_to_flash(uint32_t ratio, uint32_t limit);
uint8_t read_line_from_file(FIL* file, char* buffer, uint16_t buffer_size);
uint8_t read_config_from_tf_card(void);
void process_command(char* cmd_str);
void uart_task(void);

#ifdef __cplusplus
}
#endif

#endif /* __USART_APP_H__ */
