# 按键模块保姆级配置与实现文档

## 目录
1. [硬件连接说明](#硬件连接说明)
2. [CubeMX配置步骤](#cubemx配置步骤)
3. [底层BSP配置](#底层bsp配置)
4. [按键扫描原理](#按键扫描原理)
5. [消抖处理机制](#消抖处理机制)
6. [应用层实现](#应用层实现)
7. [按键应用实例](#按键应用实例)
8. [使用示例](#使用示例)
9. [常见问题与解决方案](#常见问题与解决方案)

---

## 硬件连接说明

### 按键硬件分布
本开发板共有7个按键，分布在两个GPIO端口上：

| 按键编号 | GPIO端口 | GPIO引脚 | 物理位置 | 触发逻辑 |
|----------|----------|----------|----------|----------|
| KEY1     | GPIOD    | PD8      | 板载按键1 | 低电平触发 |
| KEY2     | GPIOD    | PD9      | 板载按键2 | 低电平触发 |
| KEY3     | GPIOD    | PD10     | 板载按键3 | 低电平触发 |
| KEY4     | GPIOD    | PD11     | 板载按键4 | 低电平触发 |
| KEY5     | GPIOD    | PD12     | 板载按键5 | 低电平触发 |
| KEY6     | GPIOD    | PD13     | 板载按键6 | 低电平触发 |
| KEYW     | GPIOA    | PA0      | 唤醒按键  | 低电平触发 |

### 电路特性
- **触发逻辑**: 所有按键均为低电平触发（按下时为低电平）
- **上拉电阻**: 使用内部上拉电阻，平时为高电平
- **消抖需求**: 机械按键存在抖动，需要软件消抖
- **电压等级**: 3.3V逻辑电平

---

## CubeMX配置步骤

### 1. 打开CubeMX工程
1. 启动STM32CubeMX
2. 选择芯片型号：GD32F470VET6（或对应的STM32F4系列）
3. 打开现有工程或创建新工程

### 2. GPIO配置步骤

#### 配置GPIOD端口（KEY1-6）
1. **选择引脚**：
   - 在Pinout视图中找到PD8、PD9、PD10、PD11、PD12、PD13
   - 右键点击每个引脚，选择"GPIO_Input"

2. **配置参数**：
   ```
   GPIO Mode: Input mode
   GPIO Pull-up/Pull-down: Pull-up
   User Label: 
     - PD8  → KEY1
     - PD9  → KEY2  
     - PD10 → KEY3
     - PD11 → KEY4
     - PD12 → KEY5
     - PD13 → KEY6
   ```

#### 配置GPIOA端口（KEYW）
1. **选择引脚**：
   - 找到PA0引脚
   - 右键点击，选择"GPIO_Input"

2. **配置参数**：
   ```
   GPIO Mode: Input mode
   GPIO Pull-up/Pull-down: Pull-up
   User Label: KEYW
   ```

### 3. 中断配置（可选）
如果需要中断方式检测按键：
1. 选择引脚，设置为"GPIO_EXTI"
2. 配置中断触发方式为"Falling edge"
3. 在NVIC中使能对应的外部中断

### 4. 代码生成
1. 进入Project Manager
2. 点击"GENERATE CODE"

---

## 底层BSP配置

### 1. 头文件定义 (mcu_cmic_gd32f470vet6.h)

```c
/* 按键GPIO端口和时钟定义 */
#define KEYD_PORT        GPIOD
#define KEYA_PORT        GPIOA
#define KEYD_CLK_PORT    RCU_GPIOD
#define KEYA_CLK_PORT    RCU_GPIOA

/* 按键引脚定义 */
#define KEY1_PIN        GPIO_PIN_8
#define KEY2_PIN        GPIO_PIN_9
#define KEY3_PIN        GPIO_PIN_10
#define KEY4_PIN        GPIO_PIN_11
#define KEY5_PIN        GPIO_PIN_12
#define KEY6_PIN        GPIO_PIN_13
#define KEYW_PIN        GPIO_PIN_0

/* 按键读取宏定义 */
#define KEY1_READ       gpio_input_bit_get(KEYD_PORT, KEY1_PIN)
#define KEY2_READ       gpio_input_bit_get(KEYD_PORT, KEY2_PIN)
#define KEY3_READ       gpio_input_bit_get(KEYD_PORT, KEY3_PIN)
#define KEY4_READ       gpio_input_bit_get(KEYD_PORT, KEY4_PIN)
#define KEY5_READ       gpio_input_bit_get(KEYD_PORT, KEY5_PIN)
#define KEY6_READ       gpio_input_bit_get(KEYD_PORT, KEY6_PIN)
#define KEYW_READ       gpio_input_bit_get(KEYA_PORT, KEYW_PIN)

/* 函数声明 */
void bsp_btn_init(void);
```

### 2. BSP初始化函数 (mcu_cmic_gd32f470vet6.c)

```c
//按键初始化函数
void bsp_btn_init(void)
{
    /* 使能按键相关GPIO时钟 */
    rcu_periph_clock_enable(KEYD_CLK_PORT);     // 使能GPIOD时钟
    rcu_periph_clock_enable(KEYA_CLK_PORT);     // 使能GPIOA时钟
    
    /* 配置按键GPIO端口模式 */ 
    gpio_mode_set(KEYD_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, 
                  KEY1_PIN | KEY2_PIN | KEY3_PIN | KEY4_PIN | KEY5_PIN | KEY6_PIN);
    
    gpio_mode_set(KEYA_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, KEYW_PIN);
}
```

**关键参数说明**：
- `GPIO_MODE_INPUT`: 设置为输入模式
- `GPIO_PUPD_PULLUP`: 使能内部上拉电阻
- 按键按下时读取到低电平(RESET)，松开时读取到高电平(SET)

---

## 按键扫描原理

### 1. 基本扫描原理
```c
uint8_t key_read(void)
{
    uint8_t temp = 0;
    
    // GPIO读取函数，注意按键按下时为低电平
    if(KEY1_READ == RESET) temp = 1;
    if(KEY2_READ == RESET) temp = 2;
    if(KEY3_READ == RESET) temp = 3;
    if(KEY4_READ == RESET) temp = 4;
    if(KEY5_READ == RESET) temp = 5;
    if(KEY6_READ == RESET) temp = 6;
    
    return temp;
}
```

### 2. 状态检测算法
```c
// 按键状态变量
uint8_t key_val, key_old, key_down, key_up;

void key_task(void)
{
    key_val = key_read();                           // 读取当前按键值
    key_down = key_val & (key_old ^ key_val);       // 检测按下沿
    key_up = ~key_val & (key_old ^ key_val);        // 检测松开沿
    key_old = key_val;                              // 保存当前状态
}
```

**算法解析**：
- `key_val`: 当前按键状态
- `key_old`: 上一次按键状态  
- `key_down`: 按下沿检测（从0到1的变化）
- `key_up`: 松开沿检测（从1到0的变化）
- `key_old ^ key_val`: 异或运算检测状态变化

---

## 消抖处理机制

### 1. 软件消抖原理
机械按键在按下和松开时会产生抖动，需要通过软件滤波：

```c
#define KEY_DEBOUNCE_TIME   20  // 消抖时间20ms

uint8_t key_debounce_read(void)
{
    static uint8_t key_state = 0;
    static uint32_t last_time = 0;
    uint8_t current_key = key_read();
    uint32_t current_time = get_system_ms();
    
    // 状态改变时开始计时
    if(current_key != key_state)
    {
        if(current_time - last_time >= KEY_DEBOUNCE_TIME)
        {
            key_state = current_key;
            last_time = current_time;
        }
    }
    
    return key_state;
}
```

### 2. 状态机消抖
```c
typedef enum {
    KEY_STATE_IDLE,
    KEY_STATE_DEBOUNCE,
    KEY_STATE_PRESSED,
    KEY_STATE_RELEASE_DEBOUNCE
} key_state_t;

uint8_t key_state_machine(void)
{
    static key_state_t state = KEY_STATE_IDLE;
    static uint32_t debounce_timer = 0;
    static uint8_t key_result = 0;
    uint8_t current_key = key_read();
    uint32_t current_time = get_system_ms();
    
    switch(state)
    {
        case KEY_STATE_IDLE:
            if(current_key != 0)
            {
                debounce_timer = current_time;
                state = KEY_STATE_DEBOUNCE;
            }
            break;
            
        case KEY_STATE_DEBOUNCE:
            if(current_key == 0)
            {
                state = KEY_STATE_IDLE;
            }
            else if(current_time - debounce_timer >= KEY_DEBOUNCE_TIME)
            {
                key_result = current_key;
                state = KEY_STATE_PRESSED;
            }
            break;
            
        case KEY_STATE_PRESSED:
            if(current_key == 0)
            {
                debounce_timer = current_time;
                state = KEY_STATE_RELEASE_DEBOUNCE;
            }
            break;
            
        case KEY_STATE_RELEASE_DEBOUNCE:
            if(current_key != 0)
            {
                state = KEY_STATE_PRESSED;
            }
            else if(current_time - debounce_timer >= KEY_DEBOUNCE_TIME)
            {
                key_result = 0;
                state = KEY_STATE_IDLE;
            }
            break;
    }
    
    return key_result;
}
```

---

## 应用层实现

### 1. 应用层头文件 (btn_app.h)

```c
#ifndef __BTN_APP_H__
#define __BTN_APP_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 按键状态变量声明 */
extern uint8_t key_val, key_old, key_down, key_up;

/* 函数声明 */
uint8_t key_read(void);
void key_task(void);
void btn_task(void);

#ifdef __cplusplus
}
#endif

#endif
```

### 2. 应用层实现文件 (btn_app.c)

```c
#include "cmic_gd32f470vet6.h"
#include "usart_app.h"

// 按键状态变量
uint8_t key_val, key_old, key_down, key_up;

extern uint8_t ucLed[6];

// 按键读取函数
uint8_t key_read(void)
{
    uint8_t temp = 0;

    // GPIO读取函数，注意按键按下时为低电平
    if(KEY1_READ == RESET) temp = 1;
    if(KEY2_READ == RESET) temp = 2;
    if(KEY3_READ == RESET) temp = 3;
    if(KEY4_READ == RESET) temp = 4;
    if(KEY5_READ == RESET) temp = 5;
    if(KEY6_READ == RESET) temp = 6;

    return temp;
}

// 按键处理函数
void key_task(void)
{
    key_val = key_read();
    key_down = key_val & (key_old ^ key_val);
    key_up = ~key_val & (key_old ^ key_val);
    key_old = key_val;

    // 按键1：切换采样状态
    if(key_down == 1)
    {
        toggle_sampling();
    }
    // 按键2：设置采样周期为5秒
    if(key_down == 2)
    {
        set_sampling_period(5);
    }
    // 按键3：设置采样周期为10秒
    if(key_down == 3)
    {
        set_sampling_period(10);
    }
    // 按键4：设置采样周期为15秒
    if(key_down == 4)
    {
        set_sampling_period(15);
    }
}

// 按键任务函数，调用内部的key_task
void btn_task(void)
{
    key_task();
}
```

---

## 按键应用实例

### 1. 基本按键功能
基于前面的按键扫描算法，我们可以实现各种按键功能：
- 单击检测
- 长按检测
- 连击检测
- 组合键检测

### 2. 长按检测实现
```c
#define LONG_PRESS_TIME 2000  // 长按时间2秒

void key_long_press_detect(void)
{
    static uint32_t press_start_time[7] = {0};  // 每个按键的按下时间
    static uint8_t long_press_flag[7] = {0};    // 长按标志
    uint32_t current_time = get_system_ms();

    for(int i = 1; i <= 6; i++)
    {
        // 检测按键按下
        if(key_down == i)
        {
            press_start_time[i] = current_time;
            long_press_flag[i] = 0;
        }

        // 检测长按
        if(key_val == i && !long_press_flag[i])
        {
            if(current_time - press_start_time[i] >= LONG_PRESS_TIME)
            {
                printf("Key %d long pressed\n", i);
                long_press_flag[i] = 1;
                // 执行长按操作
                handle_long_press(i);
            }
        }

        // 检测松开
        if(key_up == i)
        {
            if(!long_press_flag[i])
            {
                printf("Key %d short pressed\n", i);
                // 执行短按操作
                handle_short_press(i);
            }
        }
    }
}

void handle_long_press(uint8_t key_num)
{
    switch(key_num)
    {
        case 1:
            // 长按KEY1：系统复位
            printf("System reset...\n");
            NVIC_SystemReset();
            break;
        case 2:
            // 长按KEY2：进入配置模式
            enter_config_mode();
            break;
        default:
            break;
    }
}

void handle_short_press(uint8_t key_num)
{
    switch(key_num)
    {
        case 1:
            // 短按KEY1：切换采样状态
            toggle_sampling();
            break;
        case 2:
            // 短按KEY2：设置采样周期
            set_sampling_period(5);
            break;
        default:
            break;
    }
}
```

### 3. 连击检测实现
```c
#define DOUBLE_CLICK_TIME 500  // 双击间隔时间

void key_double_click_detect(void)
{
    static uint32_t last_click_time[7] = {0};
    static uint8_t click_count[7] = {0};
    uint32_t current_time = get_system_ms();

    for(int i = 1; i <= 6; i++)
    {
        if(key_down == i)
        {
            if(current_time - last_click_time[i] <= DOUBLE_CLICK_TIME)
            {
                click_count[i]++;
                if(click_count[i] >= 2)
                {
                    printf("Key %d double clicked\n", i);
                    handle_double_click(i);
                    click_count[i] = 0;
                }
            }
            else
            {
                click_count[i] = 1;
            }
            last_click_time[i] = current_time;
        }

        // 超时清除计数
        if(current_time - last_click_time[i] > DOUBLE_CLICK_TIME && click_count[i] == 1)
        {
            printf("Key %d single clicked\n", i);
            handle_single_click(i);
            click_count[i] = 0;
        }
    }
}
```

---

## 使用示例

### 1. 基本按键检测
```c
int main(void)
{
    // 系统初始化
    system_init();
    
    // 按键初始化
    bsp_btn_init();
    
    // 主循环
    while(1)
    {
        btn_task();  // 按键扫描
        
        // 检测按键按下
        if(key_down == 1)
        {
            printf("Key 1 pressed\n");
            LED1_TOGGLE;
        }
        
        delay_ms(5);  // 5ms扫描周期
    }
}
```

### 2. 按键控制LED
```c
void key_led_control(void)
{
    // 按键1-6分别控制LED1-6
    if(key_down == 1) ucLed[0] ^= 1;  // 翻转LED1
    if(key_down == 2) ucLed[1] ^= 1;  // 翻转LED2
    if(key_down == 3) ucLed[2] ^= 1;  // 翻转LED3
    if(key_down == 4) ucLed[3] ^= 1;  // 翻转LED4
    if(key_down == 5) ucLed[4] ^= 1;  // 翻转LED5
    if(key_down == 6) ucLed[5] ^= 1;  // 翻转LED6
}
```

### 3. 按键菜单系统
```c
typedef enum {
    MENU_MAIN,
    MENU_SETTINGS,
    MENU_DATA
} menu_state_t;

menu_state_t current_menu = MENU_MAIN;

void key_menu_process(void)
{
    switch(current_menu)
    {
        case MENU_MAIN:
            if(key_down == 1) current_menu = MENU_SETTINGS;
            if(key_down == 2) current_menu = MENU_DATA;
            break;
            
        case MENU_SETTINGS:
            if(key_down == 1) adjust_setting_up();
            if(key_down == 2) adjust_setting_down();
            if(key_down == 6) current_menu = MENU_MAIN;  // 返回键
            break;
            
        case MENU_DATA:
            if(key_down == 1) show_next_data();
            if(key_down == 2) show_prev_data();
            if(key_down == 6) current_menu = MENU_MAIN;  // 返回键
            break;
    }
}
```

### 4. 长按检测
```c
#define LONG_PRESS_TIME 2000  // 长按时间2秒

void key_long_press_detect(void)
{
    static uint32_t press_start_time = 0;
    static uint8_t long_press_flag = 0;
    uint32_t current_time = get_system_ms();
    
    // 检测按键按下
    if(key_down == 1)
    {
        press_start_time = current_time;
        long_press_flag = 0;
    }
    
    // 检测长按
    if(key_val == 1 && !long_press_flag)
    {
        if(current_time - press_start_time >= LONG_PRESS_TIME)
        {
            printf("Long press detected\n");
            long_press_flag = 1;
            // 执行长按操作
            system_reset();
        }
    }
    
    // 检测松开
    if(key_up == 1)
    {
        if(!long_press_flag)
        {
            printf("Short press detected\n");
            // 执行短按操作
            LED1_TOGGLE;
        }
    }
}
```

### 5. 在调度器中使用
```c
// 在scheduler.c中添加按键任务
static task_t scheduler_task[] =
{
     {led_task,  1,    0}      // 1ms周期执行LED任务
    ,{btn_task,  5,    0}      // 5ms周期执行按键任务
    ,{other_task, 100, 0}      // 其他任务
};
```

---

## 常见问题与解决方案

### 1. 按键无响应
**可能原因**：
- GPIO时钟未使能
- 上拉电阻未配置
- 引脚配置错误

**解决方案**：
```c
// 检查时钟使能
rcu_periph_clock_enable(KEYD_CLK_PORT);

// 检查上拉电阻配置
gpio_mode_set(KEYD_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, KEY1_PIN);

// 检查引脚读取
uint8_t pin_state = gpio_input_bit_get(KEYD_PORT, KEY1_PIN);
printf("Pin state: %d\n", pin_state);
```

### 2. 按键抖动严重
**可能原因**：
- 没有消抖处理
- 扫描频率过高
- 硬件问题

**解决方案**：
```c
// 添加软件消抖
#define DEBOUNCE_TIME 20
static uint32_t last_scan_time = 0;
uint32_t current_time = get_system_ms();

if(current_time - last_scan_time >= DEBOUNCE_TIME)
{
    key_task();
    last_scan_time = current_time;
}
```

### 3. 按键响应延迟
**可能原因**：
- 扫描周期过长
- 消抖时间过长
- 主循环阻塞

**解决方案**：
```c
// 减少扫描周期
{btn_task, 5, 0}  // 改为5ms扫描

// 减少消抖时间
#define DEBOUNCE_TIME 10  // 改为10ms

// 避免主循环阻塞
// 使用非阻塞延时或调度器
```

### 4. 多按键冲突
**可能原因**：
- 按键扫描逻辑错误
- 同时按下多个按键

**解决方案**：
```c
// 修改扫描逻辑，支持多按键
uint8_t key_read_multi(void)
{
    uint8_t temp = 0;
    
    if(KEY1_READ == RESET) temp |= 0x01;
    if(KEY2_READ == RESET) temp |= 0x02;
    if(KEY3_READ == RESET) temp |= 0x04;
    if(KEY4_READ == RESET) temp |= 0x08;
    if(KEY5_READ == RESET) temp |= 0x10;
    if(KEY6_READ == RESET) temp |= 0x20;
    
    return temp;
}
```

### 5. 中断方式按键问题
**可能原因**：
- 中断配置错误
- 中断处理函数问题

**解决方案**：
```c
// 配置外部中断
void EXTI9_5_IRQHandler(void)
{
    if(EXTI_GetITStatus(EXTI_Line8) != RESET)
    {
        // 按键中断处理
        key_interrupt_flag = 1;
        EXTI_ClearITPendingBit(EXTI_Line8);
    }
}

// 在主循环中处理
if(key_interrupt_flag)
{
    key_interrupt_flag = 0;
    delay_ms(20);  // 消抖延时
    if(KEY1_READ == RESET)
    {
        // 确认按键按下
        key_process();
    }
}
```

---

## 总结

本文档详细介绍了按键模块的完整配置和实现过程，包括：

1. **硬件层面**: 按键分布、电路特性、触发逻辑
2. **配置层面**: CubeMX配置步骤、GPIO设置
3. **算法层面**: 按键扫描原理、消抖处理机制
4. **驱动层面**: BSP底层驱动实现
5. **应用层面**: 应用层封装和高级功能
6. **实践层面**: 各种使用示例和问题解决

通过本文档，您可以：
- 完全掌握按键模块的配置方法
- 理解按键扫描和消抖的原理
- 实现各种按键功能（单击、长按、组合键）
- 解决常见的按键检测问题
- 选择合适的按键处理方案（轮询vs中断）

建议在实际使用时，根据应用需求选择合适的按键处理方式，并注意消抖处理和扫描周期的设置。
