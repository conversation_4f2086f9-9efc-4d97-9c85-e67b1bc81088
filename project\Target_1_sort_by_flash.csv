File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,19.087454%,12709,96,12158,551,0,96
ff.o,10.998302%,7323,520,7174,141,8,512
usart_app.o,9.559497%,6365,1576,6190,143,32,1544
sdio_sdcard.o,9.503927%,6328,64,6296,0,32,32
oled.o,5.812294%,3870,22,1136,2712,22,0
btod.o,3.232056%,2152,0,2152,0,0,0
sd_app.o,2.135680%,1422,2516,1280,110,32,2484
fz_wm.l,1.946443%,1296,0,1280,16,0,0
scanf_fp.o,1.910398%,1272,0,1272,0,0,0
cmic_gd32f470vet6.o,1.787243%,1190,574,1172,0,18,556
log_app.o,1.706141%,1136,656,1128,0,8,648
_printf_fp_dec.o,1.582987%,1054,0,1054,0,0,0
gd25qxx.o,1.339681%,892,0,892,0,0,0
_scanf.o,1.327666%,884,0,884,0,0,0
m_wm.l,1.204512%,802,0,802,0,0,0
_printf_fp_hex.o,1.204512%,802,0,764,38,0,0
scanf_hexfp.o,1.201508%,800,0,800,0,0,0
gd32f4xx_rcu.o,1.111395%,740,0,740,0,0,0
gd32f4xx_dma.o,1.054323%,702,0,702,0,0,0
perf_counter.o,0.988240%,658,64,590,4,64,0
gd32f4xx_usart.o,0.862082%,574,0,574,0,0,0
gd32f4xx_adc.o,0.826037%,550,0,550,0,0,0
gd32f4xx_sdio.o,0.820029%,546,0,546,0,0,0
system_gd32f4xx.o,0.777976%,518,4,514,0,4,0
startup_gd32f450_470.o,0.738927%,492,8192,64,428,0,8192
gd32f4xx_i2c.o,0.657826%,438,0,438,0,0,0
__printf_flags_ss_wp.o,0.614271%,409,0,392,17,0,0
gd32f4xx_rtc.o,0.612769%,408,0,408,0,0,0
oled_app.o,0.606762%,404,16,388,0,16,0
bigflt0.o,0.564709%,376,0,228,148,0,0
diskio.o,0.540679%,360,0,360,0,0,0
dmul.o,0.510641%,340,0,340,0,0,0
rtc_app.o,0.510641%,340,0,340,0,0,0
_scanf_int.o,0.498626%,332,0,332,0,0,0
lc_ctype_c.o,0.474596%,316,0,44,272,0,0
scanf_infnan.o,0.462581%,308,0,308,0,0,0
narrow.o,0.399501%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.396498%,264,0,264,0,0,0
lludivv7m.o,0.357449%,238,0,238,0,0,0
ldexp.o,0.342430%,228,0,228,0,0,0
_printf_wctomb.o,0.294369%,196,0,188,8,0,0
main.o,0.288362%,192,0,192,0,0,0
_printf_hex_int_ll_ptr.o,0.282354%,188,0,148,40,0,0
btn_app.o,0.282354%,188,4,184,0,4,0
_printf_intcommon.o,0.267336%,178,0,178,0,0,0
gd32f4xx_misc.o,0.264332%,176,0,176,0,0,0
strtod.o,0.246309%,164,0,164,0,0,0
led_app.o,0.244807%,163,7,156,0,7,0
scheduler.o,0.240302%,160,88,72,0,88,0
dnaninf.o,0.234294%,156,0,156,0,0,0
strncmp.o,0.225283%,150,0,150,0,0,0
systick.o,0.222279%,148,4,144,0,4,0
gd32f4xx_it.o,0.222279%,148,0,148,0,0,0
frexp.o,0.210264%,140,0,140,0,0,0
rt_memcpy_v6.o,0.207260%,138,0,138,0,0,0
lludiv10.o,0.207260%,138,0,138,0,0,0
strcmpv7m.o,0.192241%,128,0,128,0,0,0
_printf_fp_infnan.o,0.192241%,128,0,128,0,0,0
_printf_longlong_dec.o,0.186234%,124,0,124,0,0,0
perfc_port_default.o,0.183230%,122,0,122,0,0,0
dleqf.o,0.180226%,120,0,120,0,0,0
deqf.o,0.180226%,120,0,120,0,0,0
_printf_dec.o,0.180226%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.168211%,112,0,112,0,0,0
drleqf.o,0.162204%,108,0,108,0,0,0
gd32f4xx_spi.o,0.156196%,104,0,104,0,0,0
retnan.o,0.150188%,100,0,100,0,0,0
rt_memcpy_w.o,0.150188%,100,0,100,0,0,0
d2f.o,0.147185%,98,0,98,0,0,0
scalbn.o,0.138173%,92,0,92,0,0,0
__dczerorl2.o,0.135170%,90,0,90,0,0,0
_printf_str.o,0.123155%,82,0,82,0,0,0
rt_memclr_w.o,0.117147%,78,0,78,0,0,0
_printf_pad.o,0.117147%,78,0,78,0,0,0
sys_stackheap_outer.o,0.111139%,74,0,74,0,0,0
strcpy.o,0.108136%,72,0,72,0,0,0
llsdiv.o,0.108136%,72,0,72,0,0,0
lc_numeric_c.o,0.108136%,72,0,44,28,0,0
rt_memclr.o,0.102128%,68,0,68,0,0,0
dunder.o,0.096121%,64,0,64,0,0,0
_wcrtomb.o,0.096121%,64,0,64,0,0,0
_sgetc.o,0.096121%,64,0,64,0,0,0
strlen.o,0.093117%,62,0,62,0,0,0
__0sscanf.o,0.090113%,60,0,60,0,0,0
atof.o,0.084106%,56,0,56,0,0,0
__2snprintf.o,0.084106%,56,0,56,0,0,0
vsnprintf.o,0.078098%,52,0,52,0,0,0
__scatter.o,0.078098%,52,0,52,0,0,0
fpclassify.o,0.072090%,48,0,48,0,0,0
trapv.o,0.072090%,48,0,48,0,0,0
_printf_char_common.o,0.072090%,48,0,48,0,0,0
scanf_char.o,0.066083%,44,0,44,0,0,0
_printf_wchar.o,0.066083%,44,0,44,0,0,0
_printf_char.o,0.066083%,44,0,44,0,0,0
_printf_charcount.o,0.060075%,40,0,40,0,0,0
llshl.o,0.057072%,38,0,38,0,0,0
libinit2.o,0.057072%,38,0,38,0,0,0
strstr.o,0.054068%,36,0,36,0,0,0
init_aeabi.o,0.054068%,36,0,36,0,0,0
_printf_truncate.o,0.054068%,36,0,36,0,0,0
systick_wrapper_ual.o,0.048060%,32,0,32,0,0,0
_chval.o,0.042053%,28,0,28,0,0,0
__scatter_zi.o,0.042053%,28,0,28,0,0,0
dcmpi.o,0.036045%,24,0,24,0,0,0
_rserrno.o,0.033041%,22,0,22,0,0,0
isspace.o,0.027034%,18,0,18,0,0,0
exit.o,0.027034%,18,0,18,0,0,0
fpconst.o,0.024030%,16,0,0,16,0,0
dcheck1.o,0.024030%,16,0,16,0,0,0
rt_ctype_table.o,0.024030%,16,0,16,0,0,0
_snputc.o,0.024030%,16,0,16,0,0,0
gd32f4xx_pmu.o,0.024030%,16,0,16,0,0,0
__printf_wp.o,0.021026%,14,0,14,0,0,0
dretinf.o,0.018023%,12,0,12,0,0,0
sys_exit.o,0.018023%,12,0,12,0,0,0
__rtentry2.o,0.018023%,12,0,12,0,0,0
fretinf.o,0.015019%,10,0,10,0,0,0
fpinit.o,0.015019%,10,0,10,0,0,0
rtexit2.o,0.015019%,10,0,10,0,0,0
_sputc.o,0.015019%,10,0,10,0,0,0
_printf_ll.o,0.015019%,10,0,10,0,0,0
_printf_l.o,0.015019%,10,0,10,0,0,0
scanf2.o,0.012015%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.012015%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.012015%,8,0,8,0,0,0
libspace.o,0.012015%,8,96,8,0,0,96
__main.o,0.012015%,8,0,8,0,0,0
istatus.o,0.009011%,6,0,6,0,0,0
heapauxi.o,0.009011%,6,0,6,0,0,0
_printf_x.o,0.009011%,6,0,6,0,0,0
_printf_u.o,0.009011%,6,0,6,0,0,0
_printf_s.o,0.009011%,6,0,6,0,0,0
_printf_p.o,0.009011%,6,0,6,0,0,0
_printf_o.o,0.009011%,6,0,6,0,0,0
_printf_n.o,0.009011%,6,0,6,0,0,0
_printf_ls.o,0.009011%,6,0,6,0,0,0
_printf_llx.o,0.009011%,6,0,6,0,0,0
_printf_llu.o,0.009011%,6,0,6,0,0,0
_printf_llo.o,0.009011%,6,0,6,0,0,0
_printf_lli.o,0.009011%,6,0,6,0,0,0
_printf_lld.o,0.009011%,6,0,6,0,0,0
_printf_lc.o,0.009011%,6,0,6,0,0,0
_printf_i.o,0.009011%,6,0,6,0,0,0
_printf_g.o,0.009011%,6,0,6,0,0,0
_printf_f.o,0.009011%,6,0,6,0,0,0
_printf_e.o,0.009011%,6,0,6,0,0,0
_printf_d.o,0.009011%,6,0,6,0,0,0
_printf_c.o,0.009011%,6,0,6,0,0,0
_printf_a.o,0.009011%,6,0,6,0,0,0
__rtentry4.o,0.009011%,6,0,6,0,0,0
scanf1.o,0.006008%,4,0,4,0,0,0
printf2.o,0.006008%,4,0,4,0,0,0
printf1.o,0.006008%,4,0,4,0,0,0
_printf_percent_end.o,0.006008%,4,0,4,0,0,0
use_no_semi.o,0.003004%,2,0,2,0,0,0
rtexit.o,0.003004%,2,0,2,0,0,0
libshutdown2.o,0.003004%,2,0,2,0,0,0
libshutdown.o,0.003004%,2,0,2,0,0,0
libinit.o,0.003004%,2,0,2,0,0,0
