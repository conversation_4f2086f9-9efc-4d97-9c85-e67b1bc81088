File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,18.786243%,9881,96,9330,551,0,96
ff.o,12.367625%,6505,520,6356,141,8,512
sdio_sdcard.o,12.031104%,6328,64,6296,0,32,32
oled.o,7.357834%,3870,22,1136,2712,22,0
usart_app.o,6.340666%,3335,1572,3164,143,28,1544
btod.o,3.680818%,1936,0,1936,0,0,0
sd_app.o,2.703576%,1422,2516,1280,110,32,2484
cmic_gd32f470vet6.o,2.262486%,1190,574,1172,0,18,556
log_app.o,2.159819%,1136,656,1128,0,8,648
_printf_fp_dec.o,2.003917%,1054,0,1054,0,0,0
gd25qxx.o,1.695914%,892,0,892,0,0,0
_scanf.o,1.680704%,884,0,884,0,0,0
_printf_fp_hex.o,1.524802%,802,0,764,38,0,0
gd32f4xx_rcu.o,1.406924%,740,0,740,0,0,0
gd32f4xx_dma.o,1.334677%,702,0,702,0,0,0
perf_counter.o,1.251022%,658,64,590,4,64,0
gd32f4xx_usart.o,1.091317%,574,0,574,0,0,0
gd32f4xx_adc.o,1.045687%,550,0,550,0,0,0
gd32f4xx_sdio.o,1.038082%,546,0,546,0,0,0
system_gd32f4xx.o,0.984847%,518,4,514,0,4,0
startup_gd32f450_470.o,0.935415%,492,8192,64,428,0,8192
gd32f4xx_i2c.o,0.832747%,438,0,438,0,0,0
__printf_flags_ss_wp.o,0.777611%,409,0,392,17,0,0
oled_app.o,0.768105%,404,16,388,0,16,0
bigflt0.o,0.714870%,376,0,228,148,0,0
diskio.o,0.684450%,360,0,360,0,0,0
_scanf_int.o,0.631215%,332,0,332,0,0,0
lc_ctype_c.o,0.600795%,316,0,44,272,0,0
gd32f4xx_gpio.o,0.501930%,264,0,264,0,0,0
lludivv7m.o,0.452497%,238,0,238,0,0,0
_printf_wctomb.o,0.372645%,196,0,188,8,0,0
main.o,0.365040%,192,0,192,0,0,0
_printf_hex_int_ll_ptr.o,0.357435%,188,0,148,40,0,0
btn_app.o,0.357435%,188,4,184,0,4,0
_printf_intcommon.o,0.338422%,178,0,178,0,0,0
gd32f4xx_rtc.o,0.334620%,176,0,176,0,0,0
gd32f4xx_misc.o,0.334620%,176,0,176,0,0,0
led_app.o,0.309904%,163,7,156,0,7,0
scheduler.o,0.304200%,160,88,72,0,88,0
rtc_app.o,0.292792%,154,0,154,0,0,0
strncmp.o,0.285187%,150,0,150,0,0,0
systick.o,0.281385%,148,4,144,0,4,0
gd32f4xx_it.o,0.281385%,148,0,148,0,0,0
rt_memcpy_v6.o,0.262372%,138,0,138,0,0,0
lludiv10.o,0.262372%,138,0,138,0,0,0
strcmpv7m.o,0.243360%,128,0,128,0,0,0
_printf_fp_infnan.o,0.243360%,128,0,128,0,0,0
_printf_longlong_dec.o,0.235755%,124,0,124,0,0,0
perfc_port_default.o,0.231952%,122,0,122,0,0,0
_printf_dec.o,0.228150%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.212940%,112,0,112,0,0,0
gd32f4xx_spi.o,0.197730%,104,0,104,0,0,0
rt_memcpy_w.o,0.190125%,100,0,100,0,0,0
__dczerorl2.o,0.171112%,90,0,90,0,0,0
_printf_str.o,0.155902%,82,0,82,0,0,0
rt_memclr_w.o,0.148297%,78,0,78,0,0,0
_printf_pad.o,0.148297%,78,0,78,0,0,0
sys_stackheap_outer.o,0.140692%,74,0,74,0,0,0
strcpy.o,0.136890%,72,0,72,0,0,0
llsdiv.o,0.136890%,72,0,72,0,0,0
lc_numeric_c.o,0.136890%,72,0,44,28,0,0
rt_memclr.o,0.129285%,68,0,68,0,0,0
_wcrtomb.o,0.121680%,64,0,64,0,0,0
_sgetc.o,0.121680%,64,0,64,0,0,0
strlen.o,0.117877%,62,0,62,0,0,0
__0sscanf.o,0.114075%,60,0,60,0,0,0
__2snprintf.o,0.106470%,56,0,56,0,0,0
vsnprintf.o,0.098865%,52,0,52,0,0,0
__scatter.o,0.098865%,52,0,52,0,0,0
m_wm.l,0.091260%,48,0,48,0,0,0
fpclassify.o,0.091260%,48,0,48,0,0,0
_printf_char_common.o,0.091260%,48,0,48,0,0,0
scanf_char.o,0.083655%,44,0,44,0,0,0
_printf_wchar.o,0.083655%,44,0,44,0,0,0
_printf_char.o,0.083655%,44,0,44,0,0,0
_printf_charcount.o,0.076050%,40,0,40,0,0,0
libinit2.o,0.072247%,38,0,38,0,0,0
strstr.o,0.068445%,36,0,36,0,0,0
init_aeabi.o,0.068445%,36,0,36,0,0,0
_printf_truncate.o,0.068445%,36,0,36,0,0,0
systick_wrapper_ual.o,0.060840%,32,0,32,0,0,0
_chval.o,0.053235%,28,0,28,0,0,0
__scatter_zi.o,0.053235%,28,0,28,0,0,0
fz_wm.l,0.034222%,18,0,18,0,0,0
isspace.o,0.034222%,18,0,18,0,0,0
exit.o,0.034222%,18,0,18,0,0,0
rt_ctype_table.o,0.030420%,16,0,16,0,0,0
_snputc.o,0.030420%,16,0,16,0,0,0
gd32f4xx_pmu.o,0.030420%,16,0,16,0,0,0
__printf_wp.o,0.026617%,14,0,14,0,0,0
sys_exit.o,0.022815%,12,0,12,0,0,0
__rtentry2.o,0.022815%,12,0,12,0,0,0
fpinit.o,0.019012%,10,0,10,0,0,0
rtexit2.o,0.019012%,10,0,10,0,0,0
_sputc.o,0.019012%,10,0,10,0,0,0
_printf_ll.o,0.019012%,10,0,10,0,0,0
_printf_l.o,0.019012%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.015210%,8,0,8,0,0,0
libspace.o,0.015210%,8,96,8,0,0,96
__main.o,0.015210%,8,0,8,0,0,0
heapauxi.o,0.011407%,6,0,6,0,0,0
_printf_x.o,0.011407%,6,0,6,0,0,0
_printf_u.o,0.011407%,6,0,6,0,0,0
_printf_s.o,0.011407%,6,0,6,0,0,0
_printf_p.o,0.011407%,6,0,6,0,0,0
_printf_o.o,0.011407%,6,0,6,0,0,0
_printf_n.o,0.011407%,6,0,6,0,0,0
_printf_ls.o,0.011407%,6,0,6,0,0,0
_printf_llx.o,0.011407%,6,0,6,0,0,0
_printf_llu.o,0.011407%,6,0,6,0,0,0
_printf_llo.o,0.011407%,6,0,6,0,0,0
_printf_lli.o,0.011407%,6,0,6,0,0,0
_printf_lld.o,0.011407%,6,0,6,0,0,0
_printf_lc.o,0.011407%,6,0,6,0,0,0
_printf_i.o,0.011407%,6,0,6,0,0,0
_printf_g.o,0.011407%,6,0,6,0,0,0
_printf_f.o,0.011407%,6,0,6,0,0,0
_printf_e.o,0.011407%,6,0,6,0,0,0
_printf_d.o,0.011407%,6,0,6,0,0,0
_printf_c.o,0.011407%,6,0,6,0,0,0
_printf_a.o,0.011407%,6,0,6,0,0,0
__rtentry4.o,0.011407%,6,0,6,0,0,0
printf2.o,0.007605%,4,0,4,0,0,0
printf1.o,0.007605%,4,0,4,0,0,0
_printf_percent_end.o,0.007605%,4,0,4,0,0,0
use_no_semi.o,0.003802%,2,0,2,0,0,0
rtexit.o,0.003802%,2,0,2,0,0,0
libshutdown2.o,0.003802%,2,0,2,0,0,0
libshutdown.o,0.003802%,2,0,2,0,0,0
libinit.o,0.003802%,2,0,2,0,0,0
