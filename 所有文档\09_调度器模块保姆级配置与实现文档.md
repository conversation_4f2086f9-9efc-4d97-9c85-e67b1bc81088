# 调度器模块保姆级配置与实现文档

## 目录
1. [调度器概述](#调度器概述)
2. [调度器原理](#调度器原理)
3. [数据结构设计](#数据结构设计)
4. [调度器实现](#调度器实现)
5. [任务管理](#任务管理)
6. [时间管理](#时间管理)
7. [应用层实现](#应用层实现)
8. [使用示例](#使用示例)
9. [性能优化](#性能优化)
10. [常见问题与解决方案](#常见问题与解决方案)

---

## 调度器概述

### 1. 什么是调度器
调度器是一个轻量级的任务管理系统，用于在单片机中实现多任务的协作式调度。它不是真正的多线程系统，而是通过时间片轮询的方式，让多个任务按照预定的时间间隔依次执行。

### 2. 调度器特点
- **协作式调度**: 任务主动让出CPU控制权
- **时间驱动**: 基于系统时钟进行任务调度
- **零开销切换**: 无上下文切换开销
- **确定性执行**: 任务执行顺序和时间完全可预测
- **资源高效**: 相比RTOS节省大量RAM和Flash空间

### 3. 适用场景
- 对实时性要求不严格的应用
- 资源受限的嵌入式系统
- 任务执行时间较短且可预测
- 不需要复杂任务间同步的场景

### 4. 与RTOS的区别

| 特性 | 调度器 | RTOS |
|------|--------|------|
| 任务切换 | 无上下文切换 | 有上下文切换 |
| 内存开销 | 极小 | 较大 |
| 实时性 | 软实时 | 硬实时 |
| 优先级 | 无 | 有 |
| 任务同步 | 简单标志位 | 信号量/互斥锁 |
| 学习成本 | 低 | 高 |

---

## 调度器原理

### 1. 基本工作原理
```
主循环 → 检查任务1 → 检查任务2 → ... → 检查任务N → 主循环
   ↑                                                      ↓
   ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

### 2. 时间片调度
每个任务都有自己的执行周期，调度器根据系统时间判断是否到了任务的执行时间：

```
任务A: 每1ms执行一次    |A|A|A|A|A|A|A|A|A|A|
任务B: 每5ms执行一次    |B| | | | |B| | | | |B|
任务C: 每10ms执行一次   |C| | | | | | | | | |C|
时间轴(ms):             0 1 2 3 4 5 6 7 8 9 10
```

### 3. 调度算法
```c
for (每个任务)
{
    if (当前时间 >= 上次执行时间 + 执行周期)
    {
        执行任务();
        更新上次执行时间 = 当前时间;
    }
}
```

### 4. 任务执行要求
- **非阻塞**: 任务不能包含阻塞操作（如delay）
- **快速返回**: 任务执行时间要尽可能短
- **状态机**: 复杂任务应使用状态机实现
- **原子操作**: 关键数据访问要保证原子性

---

## 数据结构设计

### 1. 任务结构体定义

```c
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;           // 任务执行周期（毫秒）
    uint32_t last_run;          // 上次执行时间（毫秒）
} task_t;
```

**字段说明**：
- `task_func`: 指向任务函数的指针
- `rate_ms`: 任务的执行周期，单位为毫秒
- `last_run`: 记录任务上次执行的时间戳

### 2. 任务数组定义

```c
// 静态任务数组，每个元素包含任务函数、执行周期、上次运行时间
static task_t scheduler_task[] =
{
     {led_task,  1,    0}      // LED任务，1ms周期
    ,{adc_task,  100,  0}      // ADC任务，100ms周期
    ,{oled_task, 500,  0}      // OLED任务，500ms周期
    ,{btn_task,  5,    0}      // 按键任务，5ms周期
    ,{uart_task, 5,    0}      // 串口任务，5ms周期
    ,{rtc_task,  500,  0}      // RTC任务，500ms周期
};
```

### 3. 全局变量

```c
// 全局变量，用于存储任务数量
uint8_t task_num;
```

---

## 调度器实现

### 1. 头文件定义 (scheduler.h)

```c
#ifndef __SCHEDULER_H__
#define __SCHEDULER_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 任务结构体定义 */
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;           // 任务执行周期（毫秒）
    uint32_t last_run;          // 上次执行时间（毫秒）
} task_t;

/* 函数声明 */
void scheduler_init(void);
void scheduler_run(void);
uint32_t get_system_ms(void);

/* 任务函数声明 */
void led_task(void);
void adc_task(void);
void oled_task(void);
void btn_task(void);
void uart_task(void);
void rtc_task(void);

#ifdef __cplusplus
}
#endif

#endif
```

### 2. 调度器实现文件 (scheduler.c)

```c
#include "mcu_cmic_gd32f470vet6.h"
#include "scheduler.h"

// 全局变量，用于存储任务数量
uint8_t task_num;

// 静态任务数组，每个元素包含任务函数、执行周期、上次运行时间
static task_t scheduler_task[] =
{
     {led_task,  1,    0}      // LED任务，1ms周期
    ,{adc_task,  100,  0}      // ADC任务，100ms周期
    ,{oled_task, 500,  0}      // OLED任务，500ms周期
    ,{btn_task,  5,    0}      // 按键任务，5ms周期
    ,{uart_task, 5,    0}      // 串口任务，5ms周期
    ,{rtc_task,  500,  0}      // RTC任务，500ms周期
};

/**
 * @brief 调度器初始化函数
 * 计算任务数组元素个数，并将结果存储在 task_num 中
 */
void scheduler_init(void)
{
    // 计算任务数组元素个数，并将结果存储在 task_num 中
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief 调度器运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经达到任务的执行周期，则执行该任务并更新上次运行时间
 */
void scheduler_run(void)
{
    // 遍历任务数组中的所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 获取当前的系统时间（毫秒）
        uint32_t now_time = get_system_ms();

        // 检查当前时间是否达到任务的执行时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // 更新任务的上次运行时间为当前时间
            scheduler_task[i].last_run = now_time;

            // 执行任务函数
            scheduler_task[i].task_func();
        }
    }
}
```

### 3. 系统时间获取函数

```c
// 系统毫秒计数器
static volatile uint32_t system_ms_counter = 0;

/**
 * @brief 获取系统毫秒时间
 * @return 系统运行时间（毫秒）
 */
uint32_t get_system_ms(void)
{
    return system_ms_counter;
}

/**
 * @brief 系统毫秒计时器中断处理函数
 * 通常在SysTick中断中调用
 */
void system_ms_tick(void)
{
    system_ms_counter++;
}

/**
 * @brief SysTick中断处理函数
 */
void SysTick_Handler(void)
{
    system_ms_tick();
}
```

---

## 任务管理

### 1. 任务设计原则

#### 非阻塞原则
```c
// ❌ 错误的任务实现（阻塞）
void bad_task(void)
{
    LED1_ON;
    delay_ms(100);  // 阻塞操作，会影响其他任务
    LED1_OFF;
}

// ✅ 正确的任务实现（非阻塞）
void good_task(void)
{
    static uint8_t state = 0;
    static uint32_t last_time = 0;
    uint32_t current_time = get_system_ms();
    
    switch(state)
    {
        case 0:
            LED1_ON;
            last_time = current_time;
            state = 1;
            break;
            
        case 1:
            if(current_time - last_time >= 100)
            {
                LED1_OFF;
                state = 0;
            }
            break;
    }
}
```

#### 状态机设计
```c
typedef enum {
    TASK_STATE_IDLE,
    TASK_STATE_WORKING,
    TASK_STATE_WAITING,
    TASK_STATE_COMPLETE
} task_state_t;

void state_machine_task(void)
{
    static task_state_t state = TASK_STATE_IDLE;
    static uint32_t timer = 0;
    uint32_t current_time = get_system_ms();
    
    switch(state)
    {
        case TASK_STATE_IDLE:
            // 等待触发条件
            if(trigger_condition)
            {
                state = TASK_STATE_WORKING;
                timer = current_time;
            }
            break;
            
        case TASK_STATE_WORKING:
            // 执行工作
            do_work();
            state = TASK_STATE_WAITING;
            break;
            
        case TASK_STATE_WAITING:
            // 等待完成
            if(current_time - timer >= WORK_TIMEOUT)
            {
                state = TASK_STATE_COMPLETE;
            }
            break;
            
        case TASK_STATE_COMPLETE:
            // 完成处理
            cleanup();
            state = TASK_STATE_IDLE;
            break;
    }
}
```

### 2. 任务优先级管理

虽然调度器本身不支持优先级，但可以通过调整任务在数组中的位置和执行周期来实现优先级效果：

```c
// 高优先级任务放在前面，执行周期短
static task_t scheduler_task[] =
{
     {critical_task, 1,   0}   // 最高优先级，1ms
    ,{urgent_task,   5,   0}   // 高优先级，5ms
    ,{normal_task,   50,  0}   // 普通优先级，50ms
    ,{low_task,      500, 0}   // 低优先级，500ms
};
```

### 3. 动态任务管理

```c
#define MAX_TASKS 10

typedef struct {
    task_t tasks[MAX_TASKS];
    uint8_t task_count;
} dynamic_scheduler_t;

dynamic_scheduler_t scheduler;

/**
 * @brief 添加任务
 */
int scheduler_add_task(void (*func)(void), uint32_t period_ms)
{
    if(scheduler.task_count >= MAX_TASKS)
        return -1;  // 任务数量已满
    
    scheduler.tasks[scheduler.task_count].task_func = func;
    scheduler.tasks[scheduler.task_count].rate_ms = period_ms;
    scheduler.tasks[scheduler.task_count].last_run = 0;
    scheduler.task_count++;
    
    return 0;  // 成功
}

/**
 * @brief 删除任务
 */
int scheduler_remove_task(void (*func)(void))
{
    for(uint8_t i = 0; i < scheduler.task_count; i++)
    {
        if(scheduler.tasks[i].task_func == func)
        {
            // 将后面的任务前移
            for(uint8_t j = i; j < scheduler.task_count - 1; j++)
            {
                scheduler.tasks[j] = scheduler.tasks[j + 1];
            }
            scheduler.task_count--;
            return 0;  // 成功
        }
    }
    return -1;  // 未找到任务
}
```

---

## 时间管理

### 1. 系统时钟配置

```c
/**
 * @brief 配置SysTick定时器为1ms中断
 */
void systick_config(void)
{
    /* 配置SysTick为1ms中断 */
    if (SysTick_Config(SystemCoreClock / 1000U))
    {
        /* 配置失败处理 */
        while(1);
    }
    
    /* 设置SysTick中断优先级 */
    NVIC_SetPriority(SysTick_IRQn, 0x00U);
}
```

### 2. 时间戳管理

```c
/**
 * @brief 获取时间差（处理溢出）
 */
uint32_t get_time_diff(uint32_t start_time, uint32_t end_time)
{
    if(end_time >= start_time)
        return end_time - start_time;
    else
        return (0xFFFFFFFF - start_time) + end_time + 1;  // 处理溢出
}

/**
 * @brief 检查超时
 */
uint8_t is_timeout(uint32_t start_time, uint32_t timeout_ms)
{
    uint32_t current_time = get_system_ms();
    return (get_time_diff(start_time, current_time) >= timeout_ms);
}
```

### 3. 延时函数实现

```c
/**
 * @brief 非阻塞延时
 */
uint8_t delay_ms_non_blocking(uint32_t delay_ms)
{
    static uint32_t start_time = 0;
    static uint8_t delay_active = 0;
    
    if(!delay_active)
    {
        start_time = get_system_ms();
        delay_active = 1;
        return 0;  // 延时开始
    }
    
    if(get_system_ms() - start_time >= delay_ms)
    {
        delay_active = 0;
        return 1;  // 延时完成
    }
    
    return 0;  // 延时进行中
}
```

---

## 应用层实现

### 1. 主函数实现

```c
int main(void)
{
    // 系统初始化
    system_init();
    
    // 配置SysTick
    systick_config();
    
    // 初始化各模块
    bsp_led_init();
    bsp_btn_init();
    bsp_oled_init();
    bsp_usart_init();
    bsp_adc_init();
    bsp_rtc_init();
    
    // 初始化调度器
    scheduler_init();
    
    // 主循环
    while(1)
    {
        scheduler_run();  // 运行调度器
        
        // 可以在这里添加其他非周期性任务
        // 或者进入低功耗模式
    }
}
```

### 2. 任务函数示例

```c
/**
 * @brief LED任务 - 1ms周期
 */
void led_task(void)
{
    led_disp(ucLed);  // 更新LED显示
}

/**
 * @brief ADC任务 - 100ms周期
 */
void adc_task(void)
{
    // 处理ADC数据
    filtered_adc_value = adc_moving_average_filter(adc_value[0]);
    
    // 转换为电压值
    voltage_mv = adc_to_voltage_mv(filtered_adc_value);
}

/**
 * @brief 按键任务 - 5ms周期
 */
void btn_task(void)
{
    key_val = key_read();
    key_down = key_val & (key_old ^ key_val);
    key_up = ~key_val & (key_old ^ key_val);
    key_old = key_val;
    
    // 处理按键事件
    if(key_down == 1)
    {
        toggle_sampling();
    }
}

/**
 * @brief 串口任务 - 5ms周期
 */
void uart_task(void)
{
    if(rx_flag)
    {
        rx_flag = 0;
        uart_command_parser();
    }
}

/**
 * @brief OLED任务 - 500ms周期
 */
void oled_task(void)
{
    if(get_sampling_status())
    {
        // 显示采样数据
        oled_display_sampling_data();
    }
    else
    {
        // 显示系统状态
        oled_display_system_status();
    }
}
```

---

## 使用示例

### 1. 基本使用示例

```c
// 定义任务函数
void task1(void)
{
    static uint32_t counter = 0;
    printf("Task1 executed: %lu\r\n", counter++);
}

void task2(void)
{
    static uint8_t led_state = 0;
    led_state ^= 1;
    LED1_SET(led_state);
}

// 在scheduler.c中添加任务
static task_t scheduler_task[] =
{
     {task1, 1000, 0}  // 每1秒执行一次
    ,{task2, 500,  0}  // 每0.5秒执行一次
};
```

### 2. 复杂任务示例

```c
/**
 * @brief 数据采集任务
 */
void data_acquisition_task(void)
{
    typedef enum {
        STATE_IDLE,
        STATE_START_CONVERSION,
        STATE_WAIT_CONVERSION,
        STATE_READ_DATA,
        STATE_PROCESS_DATA
    } adc_state_t;
    
    static adc_state_t state = STATE_IDLE;
    static uint32_t conversion_start_time = 0;
    
    switch(state)
    {
        case STATE_IDLE:
            if(sampling_enabled)
            {
                state = STATE_START_CONVERSION;
            }
            break;
            
        case STATE_START_CONVERSION:
            adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);
            conversion_start_time = get_system_ms();
            state = STATE_WAIT_CONVERSION;
            break;
            
        case STATE_WAIT_CONVERSION:
            if(get_system_ms() - conversion_start_time >= 1)  // 等待1ms
            {
                state = STATE_READ_DATA;
            }
            break;
            
        case STATE_READ_DATA:
            raw_adc_value = adc_value[0];
            state = STATE_PROCESS_DATA;
            break;
            
        case STATE_PROCESS_DATA:
            processed_value = process_adc_data(raw_adc_value);
            store_data(processed_value);
            state = STATE_IDLE;
            break;
    }
}
```

### 3. 任务间通信示例

```c
// 全局变量用于任务间通信
volatile uint8_t data_ready_flag = 0;
volatile uint16_t shared_data = 0;

/**
 * @brief 数据生产者任务
 */
void producer_task(void)
{
    static uint16_t counter = 0;
    
    if(!data_ready_flag)
    {
        shared_data = counter++;
        data_ready_flag = 1;  // 通知消费者数据准备好
    }
}

/**
 * @brief 数据消费者任务
 */
void consumer_task(void)
{
    if(data_ready_flag)
    {
        uint16_t local_data = shared_data;
        data_ready_flag = 0;  // 清除标志
        
        // 处理数据
        process_data(local_data);
    }
}
```

### 4. 条件执行示例

```c
/**
 * @brief 条件执行任务
 */
void conditional_task(void)
{
    static uint32_t execution_count = 0;
    
    // 只在特定条件下执行
    if(system_ready && !error_occurred)
    {
        execution_count++;
        
        // 每10次执行一次特殊操作
        if(execution_count % 10 == 0)
        {
            special_operation();
        }
        
        normal_operation();
    }
}
```

---

## 性能优化

### 1. 执行时间优化

```c
/**
 * @brief 测量任务执行时间
 */
void measure_task_execution_time(void)
{
    uint32_t start_time, end_time, execution_time;
    
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = get_system_ms();
        
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            start_time = get_system_ms();
            
            scheduler_task[i].task_func();
            
            end_time = get_system_ms();
            execution_time = end_time - start_time;
            
            // 记录或输出执行时间
            printf("Task %d execution time: %lu ms\r\n", i, execution_time);
            
            scheduler_task[i].last_run = now_time;
        }
    }
}
```

### 2. 内存优化

```c
// 使用位域减少内存占用
typedef struct {
    void (*task_func)(void);
    uint16_t rate_ms;       // 减少到16位，支持最大65秒周期
    uint16_t last_run_low;  // 只保存低16位时间戳
} compact_task_t;

// 或者使用相对时间
typedef struct {
    void (*task_func)(void);
    uint16_t rate_ms;
    uint16_t countdown;     // 倒计时，减少到0时执行
} countdown_task_t;
```

### 3. 调度算法优化

```c
/**
 * @brief 优化的调度器运行函数
 */
void scheduler_run_optimized(void)
{
    static uint8_t last_task_index = 0;
    uint32_t now_time = get_system_ms();
    
    // 从上次执行的任务开始检查，避免总是从第一个任务开始
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint8_t task_index = (last_task_index + i) % task_num;
        
        if (now_time >= scheduler_task[task_index].rate_ms + scheduler_task[task_index].last_run)
        {
            scheduler_task[task_index].last_run = now_time;
            scheduler_task[task_index].task_func();
            last_task_index = task_index;
            break;  // 每次只执行一个任务，提高响应性
        }
    }
}
```

---

## 常见问题与解决方案

### 1. 任务执行时间过长
**问题现象**: 某些任务执行时间过长，影响其他任务的实时性

**解决方案**:
```c
// 将长任务分解为多个短任务
void long_task_split(void)
{
    static uint8_t step = 0;
    
    switch(step)
    {
        case 0:
            do_step1();
            step = 1;
            break;
        case 1:
            do_step2();
            step = 2;
            break;
        case 2:
            do_step3();
            step = 0;  // 重置到开始
            break;
    }
}
```

### 2. 任务周期不准确
**问题现象**: 任务实际执行周期与设定周期不符

**解决方案**:
```c
// 使用绝对时间而不是相对时间
void scheduler_run_accurate(void)
{
    static uint32_t scheduler_start_time = 0;
    
    if(scheduler_start_time == 0)
        scheduler_start_time = get_system_ms();
    
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = get_system_ms();
        uint32_t expected_time = scheduler_start_time + 
                                (now_time - scheduler_start_time) / scheduler_task[i].rate_ms * scheduler_task[i].rate_ms;
        
        if (now_time >= expected_time + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = expected_time;
            scheduler_task[i].task_func();
        }
    }
}
```

### 3. 系统时间溢出
**问题现象**: 32位时间戳在49天后溢出

**解决方案**:
```c
// 使用相对时间比较，避免溢出问题
uint8_t is_time_to_run(uint32_t last_run, uint32_t period, uint32_t now)
{
    return ((now - last_run) >= period);
}
```

### 4. 任务饥饿
**问题现象**: 某些任务长时间得不到执行

**解决方案**:
```c
// 添加任务执行监控
typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
    uint32_t max_delay;     // 最大允许延迟
    uint32_t miss_count;    // 错过执行次数
} monitored_task_t;

void scheduler_run_monitored(void)
{
    uint32_t now_time = get_system_ms();
    
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t delay = now_time - monitored_tasks[i].last_run;
        
        if (delay >= monitored_tasks[i].rate_ms)
        {
            if (delay > monitored_tasks[i].max_delay)
            {
                monitored_tasks[i].miss_count++;
                // 记录或报告任务饥饿
            }
            
            monitored_tasks[i].last_run = now_time;
            monitored_tasks[i].task_func();
        }
    }
}
```

### 5. 中断与任务冲突
**问题现象**: 中断处理影响任务调度

**解决方案**:
```c
// 在关键代码段禁用中断
void critical_task(void)
{
    __disable_irq();  // 禁用中断
    
    // 关键代码
    critical_operation();
    
    __enable_irq();   // 重新使能中断
}

// 或者使用原子操作
volatile uint8_t shared_flag = 0;

void set_flag_atomic(void)
{
    __disable_irq();
    shared_flag = 1;
    __enable_irq();
}
```

---

## 总结

本文档详细介绍了调度器模块的完整设计和实现过程，包括：

1. **理论层面**: 调度器原理、时间片调度、协作式调度
2. **设计层面**: 数据结构设计、任务管理、时间管理
3. **实现层面**: 调度器核心代码、任务函数设计
4. **应用层面**: 实际使用示例、任务间通信
5. **优化层面**: 性能优化、内存优化、算法优化
6. **实践层面**: 常见问题和解决方案

通过本文档，您可以：
- 完全理解调度器的工作原理和设计思路
- 掌握调度器的实现方法和关键技术
- 学会设计高效的任务函数和状态机
- 解决调度器使用中的各种问题
- 根据需求优化调度器性能

调度器是嵌入式系统中非常实用的工具，它提供了一种轻量级的多任务解决方案，特别适合资源受限的单片机应用。正确使用调度器可以大大提高代码的组织性和系统的可维护性。
