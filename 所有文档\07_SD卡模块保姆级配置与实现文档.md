# SD卡模块保姆级配置与实现文档

## 目录
1. [硬件连接说明](#硬件连接说明)
2. [CubeMX配置步骤](#cubemx配置步骤)
3. [SDIO配置详解](#sdio配置详解)
4. [底层BSP配置](#底层bsp配置)
5. [FATFS文件系统](#fatfs文件系统)
6. [基本文件操作](#基本文件操作)
7. [高级功能实现](#高级功能实现)
8. [应用层实现](#应用层实现)
9. [使用示例](#使用示例)
10. [常见问题与解决方案](#常见问题与解决方案)

---

## 硬件连接说明

### SD卡硬件配置
本开发板使用SDIO接口连接SD卡：

| 功能 | GPIO端口 | GPIO引脚 | SDIO功能 | 说明 |
|------|----------|----------|----------|------|
| SDIO_CLK | GPIOC | PC12 | SDIO_CK | 时钟线 |
| SDIO_CMD | GPIOD | PD2 | SDIO_CMD | 命令线 |
| SDIO_DAT0 | GPIOC | PC8 | SDIO_D0 | 数据线0 |
| SDIO_DAT1 | GPIOC | PC9 | SDIO_D1 | 数据线1 |
| SDIO_DAT2 | GPIOC | PC10 | SDIO_D2 | 数据线2 |
| SDIO_DAT3 | GPIOC | PC11 | SDIO_D3 | 数据线3 |

### SD卡规格支持
- **接口**: SDIO 4位总线模式
- **容量**: 支持SD、SDHC、SDXC卡
- **速度**: 最高25MHz时钟频率
- **电压**: 3.3V供电
- **文件系统**: FAT16/FAT32

### 电路连接
```
MCU          SD卡
PC12(CLK) --- CLK
PD2(CMD)  --- CMD
PC8(DAT0) --- DAT0
PC9(DAT1) --- DAT1
PC10(DAT2)--- DAT2
PC11(DAT3)--- DAT3
3.3V      --- VDD
GND       --- VSS
```

---

## CubeMX配置步骤

### 1. 基本SDIO配置

#### 启用SDIO
1. 在Pinout视图中找到SDIO外设
2. 设置Mode为"SD 4 bits Wide bus"
3. 自动分配引脚：
   - PC12 → SDIO_CK
   - PD2 → SDIO_CMD
   - PC8-PC11 → SDIO_D0-D3

#### 配置SDIO参数
1. 进入Configuration → Connectivity → SDIO
2. 设置基本参数：
   ```
   Clock Divide Factor: 2 (SDIO_CK = SDIOCLK/4)
   Clock Power Save: Disabled
   Clock Edge: Rising
   Bus Wide: 4 Wide
   Hardware Flow Control: Disabled
   ```

### 2. DMA配置

#### 配置DMA2
1. 进入Configuration → System Core → DMA
2. 添加DMA请求：
   ```
   DMA2 Stream 3:
     Request: SDIO_RX
     Direction: Peripheral To Memory
     Priority: Very High
     Mode: Normal
     
   DMA2 Stream 6:
     Request: SDIO_TX
     Direction: Memory To Peripheral
     Priority: Very High
     Mode: Normal
   ```

### 3. 中断配置

#### NVIC设置
1. 进入Configuration → System Core → NVIC
2. 使能中断：
   ```
   SDIO global interrupt: Enabled
   DMA2 stream3 global interrupt: Enabled
   DMA2 stream6 global interrupt: Enabled
   ```

### 4. FATFS配置

#### 使能FATFS
1. 在Middleware中找到FATFS
2. 设置User-defined为Enabled
3. 配置参数：
   ```
   Use dma template: Enabled
   Set Defines: 
     - MAX_SS: 512
     - FS_LOCK: 0
     - FF_USE_LFN: 1 (Long filename support)
   ```

---

## SDIO配置详解

### 1. SDIO工作原理
SDIO是安全数字输入输出接口，支持：
- 命令/响应机制
- 多位数据传输
- DMA高速传输
- 中断驱动操作

### 2. SD卡初始化流程
```
上电 → 复位 → 识别 → 初始化 → 传输模式
```

### 3. SDIO时钟配置
```c
/* SDIO时钟分频定义 */
#define SD_CLK_DIV_INIT     0x76    // 初始化时钟分频(400kHz)
#define SD_CLK_DIV_TRANS    0x02    // 传输时钟分频(25MHz)

/* 时钟计算公式 */
// SDIO_CK = SDIOCLK / (2 + CLK_DIV)
// 初始化: 48MHz / (2 + 0x76) = 400kHz
// 传输: 48MHz / (2 + 0x02) = 12MHz
```

---

## 底层BSP配置

### 1. 头文件定义 (mcu_cmic_gd32f470vet6.h)

```c
/* SDIO相关定义 */
#define SDIO_PORT_CLK       GPIOC
#define SDIO_PORT_CMD       GPIOD
#define SDIO_PORT_DAT       GPIOC
#define SDIO_CLK_PORT       RCU_GPIOC
#define SDIO_CMD_CLK_PORT   RCU_GPIOD

#define SDIO_CLK_PIN        GPIO_PIN_12
#define SDIO_CMD_PIN        GPIO_PIN_2
#define SDIO_DAT0_PIN       GPIO_PIN_8
#define SDIO_DAT1_PIN       GPIO_PIN_9
#define SDIO_DAT2_PIN       GPIO_PIN_10
#define SDIO_DAT3_PIN       GPIO_PIN_11

/* SD卡状态定义 */
typedef enum {
    SD_OK = 0,
    SD_ERROR,
    SD_TIMEOUT,
    SD_UNSUPPORTED_FEATURE,
    SD_PARAMETER_ERROR
} sd_error_enum;

/* 函数声明 */
void bsp_sd_init(void);
sd_error_enum sd_init(void);
```

### 2. BSP初始化函数 (mcu_cmic_gd32f470vet6.c)

```c
void bsp_sd_init(void)
{
    /* 使能GPIO时钟 */
    rcu_periph_clock_enable(SDIO_CLK_PORT);
    rcu_periph_clock_enable(SDIO_CMD_CLK_PORT);
    
    /* 使能SDIO时钟 */
    rcu_periph_clock_enable(RCU_SDIO);
    
    /* 使能DMA2时钟 */
    rcu_periph_clock_enable(RCU_DMA1);
    
    /* 配置GPIO复用功能 */
    gpio_af_set(SDIO_PORT_CLK, GPIO_AF_12, SDIO_CLK_PIN);
    gpio_af_set(SDIO_PORT_CMD, GPIO_AF_12, SDIO_CMD_PIN);
    gpio_af_set(SDIO_PORT_DAT, GPIO_AF_12, 
                SDIO_DAT0_PIN | SDIO_DAT1_PIN | SDIO_DAT2_PIN | SDIO_DAT3_PIN);
    
    /* 配置SDIO引脚 */
    gpio_mode_set(SDIO_PORT_CLK, GPIO_MODE_AF, GPIO_PUPD_NONE, SDIO_CLK_PIN);
    gpio_output_options_set(SDIO_PORT_CLK, GPIO_OTYPE_PP, GPIO_OSPEED_25MHZ, SDIO_CLK_PIN);
    
    gpio_mode_set(SDIO_PORT_CMD, GPIO_MODE_AF, GPIO_PUPD_PULLUP, SDIO_CMD_PIN);
    gpio_output_options_set(SDIO_PORT_CMD, GPIO_OTYPE_PP, GPIO_OSPEED_25MHZ, SDIO_CMD_PIN);
    
    gpio_mode_set(SDIO_PORT_DAT, GPIO_MODE_AF, GPIO_PUPD_PULLUP, 
                  SDIO_DAT0_PIN | SDIO_DAT1_PIN | SDIO_DAT2_PIN | SDIO_DAT3_PIN);
    gpio_output_options_set(SDIO_PORT_DAT, GPIO_OTYPE_PP, GPIO_OSPEED_25MHZ, 
                           SDIO_DAT0_PIN | SDIO_DAT1_PIN | SDIO_DAT2_PIN | SDIO_DAT3_PIN);
    
    /* 配置NVIC */
    nvic_irq_enable(SDIO_IRQn, 0, 0);
    
    /* 初始化SD卡 */
    sd_init();
}

/*!
    \brief      SD卡初始化
    \param[in]  none
    \param[out] none
    \retval     sd_error_enum
*/
sd_error_enum sd_init(void)
{
    sd_error_enum status = SD_OK;
    
    /* 配置RCU和GPIO，去初始化SDIO */
    sdio_deinit();
    
    /* 配置时钟和工作电压 */
    status = sd_power_on();
    if(SD_OK != status){
        return status;
    }
    
    /* 初始化卡并获取CID和CSD */
    status = sd_card_init();
    if(SD_OK != status){
        return status;
    }
    
    /* 配置SDIO外设 */
    sdio_clock_config(SDIO_SDIOCLKEDGE_RISING, SDIO_CLOCKBYPASS_DISABLE, 
                      SDIO_CLOCKPWRSAVE_DISABLE, SD_CLK_DIV_TRANS);
    sdio_bus_mode_set(SDIO_BUSMODE_4BIT);
    sdio_hardware_clock_disable();
    
    return status;
}
```

---

## FATFS文件系统

### 1. FATFS架构
```
应用层
    ↓
FATFS API
    ↓
磁盘I/O层
    ↓
SDIO驱动层
    ↓
硬件层
```

### 2. FATFS配置文件 (ffconf.h)

基于试题代码的实际配置：

```c
/* 长文件名支持配置 */
#define FF_USE_LFN          1       // 启用长文件名支持
#define FF_MAX_LFN          255     // 最大长文件名长度
/*
/ 0: 禁用LFN
/ 1: 启用LFN，使用BSS上的静态工作缓冲区（非线程安全）
/ 2: 启用LFN，使用栈上的动态工作缓冲区
/ 3: 启用LFN，使用堆上的动态工作缓冲区
*/

/* 基本功能配置 */
#define FF_FS_MINIMIZE      0       // 功能最小化级别
#define FF_USE_STRFUNC      1       // 字符串函数支持
#define FF_USE_FIND         0       // 查找功能
#define FF_USE_MKFS         1       // 格式化功能
#define FF_USE_FASTSEEK     0       // 快速定位
#define FF_USE_EXPAND       0       // 文件扩展
#define FF_USE_CHMOD        1       // 属性修改
#define FF_USE_LABEL        0       // 卷标功能
#define FF_USE_FORWARD      0       // 数据转发

/* 系统配置 */
#define FF_VOLUMES          1       // 逻辑驱动器数量
#define FF_STR_VOLUME_ID    0       // 数字卷ID
#define FF_MULTI_PARTITION  0       // 多分区支持
#define FF_MIN_SS           512     // 扇区大小
#define FF_MAX_SS           512
#define FF_LBA64            0       // 64位LBA支持
#define FF_FS_EXFAT         0       // exFAT支持

/* 代码页配置 */
#define FF_CODE_PAGE        437     // ASCII (美国)
// #define FF_CODE_PAGE     936     // 简体中文GBK
```

### 3. 磁盘I/O接口

基于试题代码的实际实现：

```c
/* 磁盘状态定义 */
#define STA_NOINIT      0x01    // 驱动器未初始化
#define STA_NODISK      0x02    // 无磁盘
#define STA_PROTECT     0x04    // 写保护

/* 磁盘I/O函数接口 */
DSTATUS disk_initialize(BYTE drv);
DSTATUS disk_status(BYTE drv);
DRESULT disk_read(BYTE drv, BYTE* buff, DWORD sector, UINT count);
DRESULT disk_write(BYTE drv, const BYTE* buff, DWORD sector, UINT count);
DRESULT disk_ioctl(BYTE drv, BYTE ctrl, void* buff);

/* 实际实现示例 */
DRESULT disk_read(BYTE drv, BYTE* buff, DWORD sector, UINT count)
{
    sd_error_enum status = SD_ERROR;

    if(NULL == buff) return RES_PARERR;
    if(!count) return RES_PARERR;

    if(0 == drv)
    {
        if(1 == count)
        {
            // 单块读取
            uint32_t byte_addr = sector * 512;
            status = sd_block_read((uint32_t *)(&buff[0]), byte_addr, BLOCKSIZE);
        }
        else
        {
            // 多块读取
            uint32_t byte_addr = sector * 512;
            status = sd_multiblocks_read((uint32_t *)(&buff[0]), byte_addr, BLOCKSIZE, (uint32_t)count);
        }
    }

    return (SD_OK == status) ? RES_OK : RES_ERROR;
}

DRESULT disk_write(BYTE drv, const BYTE* buff, DWORD sector, UINT count)
{
    sd_error_enum status = SD_ERROR;

    if(0 == drv)
    {
        if(1 == count)
        {
            uint32_t byte_addr = sector * 512;
            status = sd_block_write((uint32_t *)buff, byte_addr, BLOCKSIZE);

            // 写保护错误自动重试机制
            if (status == 5) // SD_WP_VIOLATION
            {
                uint32_t erase_start = (byte_addr / 512) * 512;
                uint32_t erase_end = erase_start + 511;
                sd_error_enum erase_status = sd_erase(erase_start, erase_end);

                if (erase_status == SD_OK)
                {
                    status = sd_block_write((uint32_t *)buff, byte_addr, BLOCKSIZE);
                }
            }
        }
        else
        {
            uint32_t byte_addr = sector * 512;
            status = sd_multiblocks_write((uint32_t *)buff, byte_addr, BLOCKSIZE, (uint32_t)count);
        }
    }

    return (SD_OK == status) ? RES_OK : RES_ERROR;
}
```

---

## 基本文件操作

### 1. 文件系统挂载

```c
/*!
    \brief      挂载文件系统 (基于试题代码实现)
    \param[in]  none
    \param[out] none
    \retval     0-成功，其他-失败
*/
int mount_filesystem(void)
{
    FRESULT mount_result;
    DSTATUS stat;
    static FATFS fs;

    /* 初始化磁盘 */
    stat = disk_initialize(0);
    printf("SD Card disk_initialize: %d\r\n", stat);

    /* 获取并打印SD卡详细信息 */
    card_info_get();

    /* 检查写保护状态 */
    uint32_t cardstate;
    sd_error_enum status = sd_cardstatus_get(&cardstate);
    if (status == SD_OK) {
        printf("SD Card state: 0x%08X (Write Protect: %s)\r\n",
               cardstate, (cardstate & 0x02000000) ? "Yes" : "No");
    } else {
        printf("Failed to get SD card status, error: %d\r\n", status);
    }

    /* 挂载文件系统 - FATFS R0.09版本的参数顺序 */
    mount_result = f_mount(0, &fs);
    printf("SD Card f_mount: %d\r\n", mount_result);

    /* 检查初始化和挂载是否成功 */
    if(RES_OK == stat && FR_OK == mount_result)
    {
        printf("\r\nSD Card Initialize and Mount Success!\r\n");
        return 0;
    }
    else
    {
        printf("SD Card Initialize or Mount Failed!\r\n");
        return -1;
    }
}

/*!
    \brief      获取并打印SD卡详细信息
    \param[in]  none
    \param[out] none
    \retval     none
*/
void card_info_get(void)
{
    sd_card_info_struct sd_cardinfo;
    sd_error_enum status;
    uint32_t block_count, block_size;

    /* 获取SD卡信息 */
    status = sd_card_information_get(&sd_cardinfo);

    if(SD_OK == status)
    {
        printf("\r\n*** SD Card Info ***\r\n");
        printf("Card Type: ");
        if(sd_cardinfo.card_type == SDIO_STD_CAPACITY_SD_CARD_V1_1)
            printf("SDSC V1.1\r\n");
        else if(sd_cardinfo.card_type == SDIO_STD_CAPACITY_SD_CARD_V2_0)
            printf("SDSC V2.0\r\n");
        else if(sd_cardinfo.card_type == SDIO_HIGH_CAPACITY_SD_CARD)
            printf("SDHC\r\n");
        else
            printf("Unknown\r\n");

        block_count = sd_cardinfo.card_capacity / sd_cardinfo.card_blocksize;
        block_size = sd_cardinfo.card_blocksize;

        printf("Card Capacity: %lu MB\r\n", sd_cardinfo.card_capacity >> 20);
        printf("Block Size: %lu bytes\r\n", block_size);
        printf("Block Count: %lu\r\n", block_count);
        printf("RCA: 0x%04X\r\n", sd_cardinfo.card_rca);
        printf("*** End of SD Card Info ***\r\n");
    }
    else
    {
        printf("Failed to get SD card information, error: %d\r\n", status);
    }
}
```

### 2. 文件读写操作

```c
/*!
    \brief      写入文件
    \param[in]  filename: 文件名
    \param[in]  data: 数据指针
    \param[in]  length: 数据长度
    \param[out] none
    \retval     0-成功，其他-失败
*/
int write_file(const char *filename, const char *data, uint32_t length)
{
    FIL file;
    FRESULT result;
    UINT bytes_written;
    
    /* 打开文件 */
    result = f_open(&file, filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (result != FR_OK)
    {
        printf("打开文件失败：%d\r\n", result);
        return -1;
    }
    
    /* 写入数据 */
    result = f_write(&file, data, length, &bytes_written);
    if (result != FR_OK || bytes_written != length)
    {
        printf("写入文件失败：%d\r\n", result);
        f_close(&file);
        return -2;
    }
    
    /* 关闭文件 */
    f_close(&file);
    
    printf("文件写入成功，写入%d字节\r\n", bytes_written);
    return 0;
}

/*!
    \brief      读取文件
    \param[in]  filename: 文件名
    \param[out] buffer: 数据缓冲区
    \param[in]  buffer_size: 缓冲区大小
    \param[out] bytes_read: 实际读取字节数
    \retval     0-成功，其他-失败
*/
int read_file(const char *filename, char *buffer, uint32_t buffer_size, uint32_t *bytes_read)
{
    FIL file;
    FRESULT result;
    UINT br;
    
    /* 打开文件 */
    result = f_open(&file, filename, FA_READ);
    if (result != FR_OK)
    {
        printf("打开文件失败：%d\r\n", result);
        return -1;
    }
    
    /* 读取数据 */
    result = f_read(&file, buffer, buffer_size - 1, &br);
    if (result != FR_OK)
    {
        printf("读取文件失败：%d\r\n", result);
        f_close(&file);
        return -2;
    }
    
    /* 添加字符串结束符 */
    buffer[br] = '\0';
    *bytes_read = br;
    
    /* 关闭文件 */
    f_close(&file);
    
    printf("文件读取成功，读取%d字节\r\n", br);
    return 0;
}
```

### 3. 目录操作

```c
/*!
    \brief      列出目录内容
    \param[in]  path: 目录路径
    \param[out] none
    \retval     0-成功，其他-失败
*/
int list_directory(const char *path)
{
    DIR dir;
    FILINFO fno;
    FRESULT result;
    
    /* 打开目录 */
    result = f_opendir(&dir, path);
    if (result != FR_OK)
    {
        printf("打开目录失败：%d\r\n", result);
        return -1;
    }
    
    printf("目录内容：%s\r\n", path);
    printf("----------------------------------------\r\n");
    
    /* 读取目录项 */
    while (1)
    {
        result = f_readdir(&dir, &fno);
        if (result != FR_OK || fno.fname[0] == 0)
            break;  // 读取完毕或出错
        
        if (fno.fattrib & AM_DIR)
        {
            printf("[DIR]  %s\r\n", fno.fname);
        }
        else
        {
            printf("[FILE] %s (%lu bytes)\r\n", fno.fname, fno.fsize);
        }
    }
    
    /* 关闭目录 */
    f_closedir(&dir);
    
    printf("----------------------------------------\r\n");
    return 0;
}

/*!
    \brief      创建目录
    \param[in]  path: 目录路径
    \param[out] none
    \retval     0-成功，其他-失败
*/
int create_directory(const char *path)
{
    FRESULT result;
    
    result = f_mkdir(path);
    if (result == FR_OK)
    {
        printf("目录创建成功：%s\r\n", path);
        return 0;
    }
    else if (result == FR_EXIST)
    {
        printf("目录已存在：%s\r\n", path);
        return 0;
    }
    else
    {
        printf("目录创建失败：%d\r\n", result);
        return -1;
    }
}
```

### 4. 文件信息获取

```c
/*!
    \brief      获取文件信息
    \param[in]  filename: 文件名
    \param[out] none
    \retval     0-成功，其他-失败
*/
int get_file_info(const char *filename)
{
    FILINFO fno;
    FRESULT result;
    
    result = f_stat(filename, &fno);
    if (result != FR_OK)
    {
        printf("获取文件信息失败：%d\r\n", result);
        return -1;
    }
    
    printf("文件信息：%s\r\n", filename);
    printf("大小：%lu 字节\r\n", fno.fsize);
    printf("属性：");
    if (fno.fattrib & AM_DIR) printf("目录 ");
    if (fno.fattrib & AM_RDO) printf("只读 ");
    if (fno.fattrib & AM_HID) printf("隐藏 ");
    if (fno.fattrib & AM_SYS) printf("系统 ");
    if (fno.fattrib & AM_ARC) printf("存档 ");
    printf("\r\n");
    
    printf("修改时间：%04d/%02d/%02d %02d:%02d:%02d\r\n",
           (fno.fdate >> 9) + 1980,    // 年
           (fno.fdate >> 5) & 15,      // 月
           fno.fdate & 31,             // 日
           fno.ftime >> 11,            // 时
           (fno.ftime >> 5) & 63,      // 分
           (fno.ftime & 31) * 2);      // 秒
    
    return 0;
}
```

---

## 高级功能实现

### 1. 磁盘空间管理

```c
/*!
    \brief      获取磁盘空间信息
    \param[in]  none
    \param[out] none
    \retval     0-成功，其他-失败
*/
int get_disk_space(void)
{
    FATFS *fs;
    DWORD free_clusters, total_sectors, free_sectors;
    FRESULT result;
    
    /* 获取空闲簇数量 */
    result = f_getfree("0:", &free_clusters, &fs);
    if (result != FR_OK)
    {
        printf("获取磁盘空间失败：%d\r\n", result);
        return -1;
    }
    
    /* 计算扇区数量 */
    total_sectors = (fs->n_fatent - 2) * fs->csize;
    free_sectors = free_clusters * fs->csize;
    
    printf("磁盘空间信息：\r\n");
    printf("总容量：%lu KB (%lu MB)\r\n", 
           total_sectors / 2, total_sectors / 2048);
    printf("可用空间：%lu KB (%lu MB)\r\n", 
           free_sectors / 2, free_sectors / 2048);
    printf("已用空间：%lu KB (%lu MB)\r\n", 
           (total_sectors - free_sectors) / 2, 
           (total_sectors - free_sectors) / 2048);
    
    return 0;
}
```

### 2. 文件系统格式化

```c
/*!
    \brief      格式化SD卡
    \param[in]  none
    \param[out] none
    \retval     0-成功，其他-失败
*/
int format_sd_card(void)
{
    FRESULT result;
    BYTE work[FF_MAX_SS];  // 工作缓冲区
    
    printf("警告：这将擦除SD卡上的所有数据！\r\n");
    printf("正在将SD卡格式化为FAT32...\r\n");
    
    /* 创建FAT32卷 */
    result = f_mkfs("0:", 0, work, sizeof(work));
    
    if (result == FR_OK)
    {
        printf("SD卡格式化成功\r\n");
        
        /* 重新挂载文件系统 */
        if (mount_filesystem() == 0)
        {
            printf("格式化的文件系统挂载成功\r\n");
            return 0;
        }
        else
        {
            printf("挂载格式化的文件系统失败\r\n");
            return -2;
        }
    }
    else
    {
        printf("SD卡格式化失败，错误：%d\r\n", result);
        return -1;
    }
}
```

### 3. 数据记录功能

```c
/*!
    \brief      记录数据到SD卡
    \param[in]  voltage_mv: 电压值(mV)
    \param[in]  timestamp: 时间戳
    \param[out] none
    \retval     0-成功，其他-失败
*/
int record_data_to_sd(uint32_t voltage_mv, uint32_t timestamp)
{
    FIL file;
    FRESULT result;
    UINT bytes_written;
    char data_line[128];
    char filename[32];
    
    /* 生成文件名（按日期） */
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);
    
    sprintf(filename, "0:/data_%02d%02d%02d.csv", 
            bcd_to_decimal(rtc_time.year),
            bcd_to_decimal(rtc_time.month),
            bcd_to_decimal(rtc_time.date));
    
    /* 生成数据行 */
    sprintf(data_line, "%02d:%02d:%02d,%lu.%02lu,%lu\r\n",
            bcd_to_decimal(rtc_time.hour),
            bcd_to_decimal(rtc_time.minute),
            bcd_to_decimal(rtc_time.second),
            voltage_mv/1000, (voltage_mv%1000)/10,
            timestamp);
    
    /* 以追加模式打开文件 */
    result = f_open(&file, filename, FA_OPEN_ALWAYS | FA_WRITE);
    if (result != FR_OK)
    {
        printf("打开数据文件失败：%d\r\n", result);
        return -1;
    }
    
    /* 移动到文件末尾 */
    f_lseek(&file, f_size(&file));
    
    /* 写入数据 */
    result = f_write(&file, data_line, strlen(data_line), &bytes_written);
    if (result != FR_OK)
    {
        printf("写入数据失败：%d\r\n", result);
        f_close(&file);
        return -2;
    }
    
    /* 同步数据到SD卡 */
    f_sync(&file);
    
    /* 关闭文件 */
    f_close(&file);
    
    printf("数据记录成功：%s\r\n", data_line);
    return 0;
}
```

---

## 应用层实现

### 1. 应用层头文件 (sd_app.h)

```c
#ifndef __SD_APP_H__
#define __SD_APP_H__

#include "stdint.h"
#include "ff.h"

#ifdef __cplusplus
extern "C" {
#endif

/* SD卡状态 */
extern uint8_t sd_card_ready;

/* 函数声明 */
void sd_task(void);
int sd_init_filesystem(void);
int sd_write_file(const char *filename, const char *data, uint32_t length);
int sd_read_file(const char *filename, char *buffer, uint32_t buffer_size, uint32_t *bytes_read);
int sd_append_file(const char *filename, const char *data);
int sd_delete_file(const char *filename);
void sd_get_info(void);

#ifdef __cplusplus
}
#endif

#endif
```

### 2. 应用层实现文件 (sd_app.c)

基于试题代码的实际实现：

```c
#include "mcu_cmic_gd32f470vet6.h"
#include "sd_app.h"
#include <string.h>
#include <stdio.h>

/* 全局变量 */
uint8_t sd_card_ready = 0;
static FATFS fs;

/*!
    \brief      SD卡FATFS初始化函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sd_fatfs_init(void)
{
    nvic_irq_enable(SDIO_IRQn, 0, 0);  // SDIO使能中断
}

/*!
    \brief      SD卡任务函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sd_task(void)
{
    static uint32_t last_check_time = 0;
    uint32_t current_time = get_system_ms();

    /* 每10秒检查一次SD卡状态 */
    if (current_time - last_check_time >= 10000)
    {
        last_check_time = current_time;

        /* 检查SD卡状态 */
        DSTATUS status = disk_status(0);
        if (status & STA_NODISK)
        {
            if (sd_card_ready)
            {
                printf("SD卡已移除\r\n");
                sd_card_ready = 0;
            }
        }
        else if (status & STA_NOINIT)
        {
            if (!sd_card_ready)
            {
                printf("检测到SD卡，尝试初始化...\r\n");
                if (mount_filesystem() == 0)
                {
                    sd_card_ready = 1;
                    printf("SD卡初始化成功\r\n");
                }
            }
        }
        else
        {
            if (!sd_card_ready)
            {
                sd_card_ready = 1;
                printf("SD卡就绪\r\n");
            }
        }
    }
}

/*!
    \brief      基于试题代码的文件写入函数
    \param[in]  filename: 文件名
    \param[in]  data: 数据
    \param[in]  length: 数据长度
    \param[out] none
    \retval     0-成功，其他-失败
*/
int sd_write_file(const char *filename, const char *data, uint32_t length)
{
    FIL file;
    FRESULT result;
    UINT bytes_written;

    if (!sd_card_ready)
    {
        printf("SD卡未就绪\r\n");
        return -1;
    }

    /* 打开文件进行写入 */
    result = f_open(&file, filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (result != FR_OK)
    {
        printf("打开文件失败：%d\r\n", result);
        return -2;
    }

    /* 写入数据 */
    result = f_write(&file, data, length, &bytes_written);
    if (result != FR_OK || bytes_written != length)
    {
        printf("写入文件失败：%d，写入字节：%d\r\n", result, bytes_written);
        f_close(&file);
        return -3;
    }

    /* 关闭文件 */
    result = f_close(&file);
    if (result != FR_OK)
    {
        printf("关闭文件失败：%d\r\n", result);
        return -4;
    }

    printf("文件写入成功：%s (%d字节)\r\n", filename, bytes_written);
    return 0;
}

/*!
    \brief      基于试题代码的文件读取函数
    \param[in]  filename: 文件名
    \param[out] buffer: 数据缓冲区
    \param[in]  buffer_size: 缓冲区大小
    \param[out] bytes_read: 实际读取字节数
    \param[out] none
    \retval     0-成功，其他-失败
*/
int sd_read_file(const char *filename, char *buffer, uint32_t buffer_size, uint32_t *bytes_read)
{
    FIL file;
    FRESULT result;
    UINT br;

    if (!sd_card_ready)
    {
        printf("SD卡未就绪\r\n");
        return -1;
    }

    /* 打开文件进行读取 */
    result = f_open(&file, filename, FA_READ);
    if (result != FR_OK)
    {
        printf("打开文件失败：%d\r\n", result);
        return -2;
    }

    /* 读取数据 */
    result = f_read(&file, buffer, buffer_size - 1, &br);
    if (result != FR_OK)
    {
        printf("读取文件失败：%d\r\n", result);
        f_close(&file);
        return -3;
    }

    /* 添加字符串结束符 */
    buffer[br] = '\0';
    *bytes_read = br;

    /* 关闭文件 */
    f_close(&file);

    printf("文件读取成功：%s (%d字节)\r\n", filename, br);
    return 0;
}

/*!
    \brief      写入文件到SD卡
    \param[in]  filename: 文件名
    \param[in]  data: 数据
    \param[in]  length: 数据长度
    \param[out] none
    \retval     0-成功，其他-失败
*/
int sd_write_file(const char *filename, const char *data, uint32_t length)
{
    FIL file;
    FRESULT result;
    UINT bytes_written;
    
    if (!sd_card_ready)
    {
        printf("SD卡未就绪\r\n");
        return -1;
    }
    
    result = f_open(&file, filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (result != FR_OK)
    {
        printf("打开文件失败：%d\r\n", result);
        return -2;
    }
    
    result = f_write(&file, data, length, &bytes_written);
    if (result != FR_OK || bytes_written != length)
    {
        printf("写入文件失败：%d\r\n", result);
        f_close(&file);
        return -3;
    }
    
    f_close(&file);
    printf("文件写入成功：%s (%d字节)\r\n", filename, bytes_written);
    return 0;
}

/*!
    \brief      追加数据到文件
    \param[in]  filename: 文件名
    \param[in]  data: 数据
    \param[out] none
    \retval     0-成功，其他-失败
*/
int sd_append_file(const char *filename, const char *data)
{
    FIL file;
    FRESULT result;
    UINT bytes_written;
    
    if (!sd_card_ready)
    {
        return -1;
    }
    
    result = f_open(&file, filename, FA_OPEN_ALWAYS | FA_WRITE);
    if (result != FR_OK)
    {
        return -2;
    }
    
    /* 移动到文件末尾 */
    f_lseek(&file, f_size(&file));
    
    result = f_write(&file, data, strlen(data), &bytes_written);
    if (result != FR_OK)
    {
        f_close(&file);
        return -3;
    }
    
    f_sync(&file);  // 同步到SD卡
    f_close(&file);
    
    return 0;
}

/*!
    \brief      获取SD卡信息
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sd_get_info(void)
{
    if (!sd_card_ready)
    {
        printf("SD卡未就绪\r\n");
        return;
    }
    
    /* 获取磁盘空间信息 */
    get_disk_space();
    
    /* 列出根目录 */
    list_directory("0:/");
}
```

---

## 使用示例

### 1. 基本初始化和使用 (基于试题代码)
```c
int main(void)
{
    // 系统初始化
    system_init();

    // SD卡硬件初始化
    bsp_sd_init();

    // FATFS初始化
    sd_fatfs_init();

    // 挂载文件系统
    if (mount_filesystem() == 0)
    {
        printf("SD卡文件系统挂载成功\r\n");
        sd_card_ready = 1;

        // 测试文件操作
        test_sd_operations();
    }
    else
    {
        printf("SD卡初始化失败\r\n");
    }

    // 主循环
    while(1)
    {
        sd_task();  // SD卡状态检查任务
        delay_ms(10);
    }
}

/*!
    \brief      SD卡操作测试函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void test_sd_operations(void)
{
    char test_data[] = "Hello, SD Card! This is a test file.";
    char read_buffer[100];
    uint32_t bytes_read;

    printf("\r\n=== SD卡文件操作测试 ===\r\n");

    // 写入测试文件
    if (sd_write_file("test.txt", test_data, strlen(test_data)) == 0)
    {
        printf("文件写入测试通过\r\n");

        // 读取测试文件
        if (sd_read_file("test.txt", read_buffer, sizeof(read_buffer), &bytes_read) == 0)
        {
            printf("文件读取测试通过\r\n");
            printf("读取内容：%s\r\n", read_buffer);
        }
        else
        {
            printf("文件读取测试失败\r\n");
        }
    }
    else
    {
        printf("文件写入测试失败\r\n");
    }

    printf("=== 测试完成 ===\r\n");
}
```

### 2. 数据记录示例
```c
void data_logging_example(void)
{
    char log_data[256];
    uint32_t voltage = get_voltage_mv();
    uint32_t timestamp = get_system_ms();
    
    /* 格式化日志数据 */
    sprintf(log_data, "%lu,%lu.%02lu,%lu\r\n",
            timestamp,
            voltage/1000, (voltage%1000)/10,
            get_system_ms()/1000);
    
    /* 追加到日志文件 */
    if (sd_append_file("0:/datalog.csv", log_data) == 0)
    {
        printf("数据记录成功\r\n");
    }
    else
    {
        printf("数据记录失败\r\n");
    }
}
```

### 3. 配置文件管理
```c
void config_file_example(void)
{
    char config_data[512];
    char read_buffer[512];
    uint32_t bytes_read;
    
    /* 生成配置数据 */
    sprintf(config_data, 
            "sampling_period=%d\r\n"
            "voltage_ratio=%d\r\n"
            "voltage_limit=%d\r\n"
            "hide_mode=%d\r\n",
            g_system_status.sampling_period,
            g_system_status.voltage_ratio,
            g_system_status.voltage_limit,
            g_system_status.hide_mode);
    
    /* 保存配置文件 */
    if (sd_write_file("0:/config.txt", config_data, strlen(config_data)) == 0)
    {
        printf("配置保存成功\r\n");
    }
    
    /* 读取配置文件 */
    if (sd_read_file("0:/config.txt", read_buffer, sizeof(read_buffer), &bytes_read) == 0)
    {
        printf("配置读取成功：\r\n%s\r\n", read_buffer);
    }
}
```

### 4. 文件管理示例
```c
void file_management_example(void)
{
    /* 创建目录 */
    create_directory("0:/logs");
    create_directory("0:/config");
    
    /* 列出根目录 */
    list_directory("0:/");
    
    /* 获取文件信息 */
    get_file_info("0:/datalog.csv");
    
    /* 删除文件 */
    if (f_unlink("0:/temp.txt") == FR_OK)
    {
        printf("临时文件删除成功\r\n");
    }
}
```

### 5. 在调度器中使用
```c
// 在scheduler.c中添加SD卡任务
static task_t scheduler_task[] =
{
     {led_task,   1,     0}      // 1ms周期执行LED任务
    ,{sd_task,    10000, 0}      // 10s周期执行SD卡任务
    ,{adc_task,   100,   0}      // 100ms周期执行ADC任务
    ,{other_task, 500,   0}      // 其他任务
};
```

---

## 常见问题与解决方案

### 1. SD卡无法识别
**可能原因**：
- SDIO配置错误
- SD卡格式不支持
- 硬件连接问题

**解决方案**：
```c
// 检查SD卡状态
DSTATUS status = disk_status(0);
if (status & STA_NOINIT)
    printf("SD卡未初始化\r\n");
if (status & STA_NODISK)
    printf("未检测到SD卡\r\n");
if (status & STA_PROTECT)
    printf("SD卡写保护\r\n");

// 检查SD卡信息
sd_card_info_struct cardinfo;
if (sd_card_information_get(&cardinfo) == SD_OK)
{
    printf("SD卡容量：%lu MB\r\n", cardinfo.card_capacity >> 20);
    printf("SD卡类型：%d\r\n", cardinfo.card_type);
}
```

### 2. 文件系统挂载失败
**可能原因**：
- SD卡未格式化
- 文件系统损坏
- 分区表错误

**解决方案**：
```c
// 尝试格式化SD卡
if (mount_filesystem() != 0)
{
    printf("尝试格式化SD卡...\r\n");
    if (format_sd_card() == 0)
    {
        printf("格式化成功，重新挂载\r\n");
        mount_filesystem();
    }
}
```

### 3. 文件操作失败
**可能原因**：
- 磁盘空间不足
- 文件名非法
- 权限问题

**解决方案**：
```c
// 检查磁盘空间
FATFS *fs;
DWORD free_clusters;
if (f_getfree("0:", &free_clusters, &fs) == FR_OK)
{
    DWORD free_kb = free_clusters * fs->csize / 2;
    if (free_kb < 100)  // 少于100KB
    {
        printf("磁盘空间不足：%lu KB\r\n", free_kb);
    }
}

// 检查文件名
int check_filename(const char *filename)
{
    // 检查非法字符
    const char *invalid_chars = "\\/:*?\"<>|";
    if (strpbrk(filename, invalid_chars) != NULL)
    {
        printf("文件名包含非法字符\r\n");
        return -1;
    }
    return 0;
}
```

### 4. 数据丢失
**可能原因**：
- 写入过程中断电
- SD卡突然移除
- 文件未正确关闭

**解决方案**：
```c
// 安全写入函数
int safe_write_file(const char *filename, const char *data, uint32_t length)
{
    FIL file;
    FRESULT result;
    UINT bytes_written;
    char temp_filename[64];
    
    /* 先写入临时文件 */
    sprintf(temp_filename, "%s.tmp", filename);
    
    result = f_open(&file, temp_filename, FA_CREATE_ALWAYS | FA_WRITE);
    if (result != FR_OK) return -1;
    
    result = f_write(&file, data, length, &bytes_written);
    if (result != FR_OK || bytes_written != length)
    {
        f_close(&file);
        f_unlink(temp_filename);
        return -2;
    }
    
    /* 同步数据 */
    f_sync(&file);
    f_close(&file);
    
    /* 删除原文件并重命名 */
    f_unlink(filename);
    result = f_rename(temp_filename, filename);
    
    return (result == FR_OK) ? 0 : -3;
}
```

### 5. 性能问题
**可能原因**：
- 频繁的小文件写入
- 未使用缓冲
- SD卡速度慢

**解决方案**：
```c
// 使用缓冲写入
#define WRITE_BUFFER_SIZE 512
static char write_buffer[WRITE_BUFFER_SIZE];
static uint16_t buffer_pos = 0;

int buffered_write(const char *data)
{
    uint16_t data_len = strlen(data);
    
    if (buffer_pos + data_len >= WRITE_BUFFER_SIZE)
    {
        /* 缓冲区满，写入文件 */
        flush_write_buffer();
    }
    
    /* 添加到缓冲区 */
    strcpy(&write_buffer[buffer_pos], data);
    buffer_pos += data_len;
    
    return 0;
}

int flush_write_buffer(void)
{
    if (buffer_pos > 0)
    {
        sd_append_file("0:/datalog.csv", write_buffer);
        buffer_pos = 0;
        memset(write_buffer, 0, sizeof(write_buffer));
    }
    return 0;
}
```

---

## 总结

本文档详细介绍了SD卡模块的完整配置和实现过程，包括：

1. **硬件层面**: SDIO接口连接、引脚配置、电路设计
2. **配置层面**: CubeMX配置、SDIO参数设置、DMA配置
3. **驱动层面**: BSP底层驱动、SDIO通信协议
4. **文件系统**: FATFS配置、磁盘I/O接口
5. **功能层面**: 文件读写、目录操作、磁盘管理
6. **应用层面**: 数据记录、配置管理、文件管理
7. **实践层面**: 各种使用示例和问题解决

通过本文档，您可以：
- 完全掌握SD卡模块的配置方法
- 理解SDIO通信和FATFS文件系统
- 实现可靠的文件存储功能
- 处理各种SD卡操作问题
- 构建完整的数据记录系统

SD卡存储是嵌入式系统中重要的大容量存储解决方案，正确使用SD卡可以实现数据记录、日志存储、配置备份等重要功能。建议在实际使用时注意数据安全和错误处理。
