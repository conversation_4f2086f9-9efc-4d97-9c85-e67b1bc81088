#include "cmic_gd32f470vet6.h"
#include "usart_app.h"

// 按键状态变量
uint8_t key_val, key_old, key_down, key_up;

extern uint8_t ucLed[6];

// 按键读取函数
uint8_t key_read(void)
{
	uint8_t temp = 0;

	// GPIO读取函数，注意按键按下时为低电平
	if(KEY1_READ == RESET) temp = 1;
	if(KEY2_READ == RESET) temp = 2;
	if(KEY3_READ == RESET) temp = 3;
	if(KEY4_READ == RESET) temp = 4;
	if(KEY5_READ == RESET) temp = 5;
	if(KEY6_READ == RESET) temp = 6;

	return temp;
}

// 按键处理函数
void key_task(void)
{
	key_val = key_read();
	key_down = key_val & (key_old ^ key_val);
	key_up = ~key_val & (key_old ^ key_val);
	key_old = key_val;

	// 按键1：切换采样状态
	if(key_down == 1)
	{
		toggle_sampling();
	}
	// 按键2：设置采样周期为5秒
	if(key_down == 2)
	{
		set_sampling_period(5);
	}
	// 按键3：设置采样周期为10秒
	if(key_down == 3)
	{
		set_sampling_period(10);
	}
	// 按键4：设置采样周期为15秒
	if(key_down == 4)
	{
		set_sampling_period(15);
	}
}


// 按键任务函数，调用内部的key_task
void btn_task(void)
{
    key_task();
}
