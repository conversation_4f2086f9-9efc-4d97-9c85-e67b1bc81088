# 综合工程实现保姆级文档

## 目录
1. [工程概述](#工程概述)
2. [系统架构设计](#系统架构设计)
3. [模块集成方案](#模块集成方案)
4. [工程配置步骤](#工程配置步骤)
5. [代码组织结构](#代码组织结构)
6. [系统初始化流程](#系统初始化流程)
7. [主要功能实现](#主要功能实现)
8. [调试与测试](#调试与测试)
9. [性能优化](#性能优化)
10. [项目扩展指南](#项目扩展指南)

---

## 工程概述

### 1. 项目背景
基于GD32F470VET6微控制器的综合嵌入式系统，集成了LED控制、按键检测、串口通信、ADC采集、OLED显示、RTC时钟、Flash存储、SD卡读写等多个功能模块，采用调度器实现多任务协作。

### 2. 系统特性
- **多模块集成**: 8个主要功能模块协同工作
- **调度器管理**: 基于时间片的任务调度系统
- **实时数据采集**: ADC电压采集与处理
- **人机交互**: OLED显示(基础字符显示) + 按键操作(基础扫描)
- **数据存储**: Flash参数存储 + SD卡数据记录
- **通信接口**: 串口命令行交互
- **时间管理**: RTC实时时钟

### 3. 应用场景
- 数据采集系统
- 环境监测设备
- 工业控制终端
- 教学实验平台
- 产品原型开发

### 4. 技术指标
- **主控**: GD32F470VET6 (ARM Cortex-M4, 168MHz)
- **存储**: 512KB Flash + 256KB SRAM
- **显示**: 128×64 OLED (I2C)
- **采集精度**: 12位ADC (0-3.3V)
- **存储扩展**: SD卡支持 (FATFS长文件名)
- **通信**: UART (115200bps)
- **实时性**: 1ms系统时钟

---

## 系统架构设计

### 1. 硬件架构

```
                    GD32F470VET6
                   ┌─────────────┐
    LED1-6    ←────┤ PD0,1,3,4,5 │
                   │ PB6         │
    KEY1-6    ────→│ PD8-13      │
                   │ PA0(KEYW)   │
    OLED      ←────┤ PB8,9(I2C0) │
                   │             │
    UART      ←────┤ PA9,10      │
                   │ (USART0)    │
    ADC       ────→│ PC0(ADC0)   │
                   │             │
    SD Card   ←────┤ PC8-12      │
                   │ (SDIO)      │
    Flash     ←────┤ Internal    │
                   │ Flash       │
                   └─────────────┘
```

### 2. 软件架构

```
┌─────────────────────────────────────────────────────┐
│                   Application Layer                 │
├─────────────────────────────────────────────────────┤
│  LED_APP │ BTN_APP │ OLED_APP │ UART_APP │ ADC_APP  │
├─────────────────────────────────────────────────────┤
│                  Scheduler Layer                    │
├─────────────────────────────────────────────────────┤
│                    BSP Layer                        │
├─────────────────────────────────────────────────────┤
│                  Hardware Layer                     │
└─────────────────────────────────────────────────────┘
```

### 3. 数据流图

```
ADC采集 → 滤波处理 → 电压转换 → OLED显示
   ↓           ↓          ↓         ↓
Flash存储 ← 参数配置 ← 串口命令 ← 按键输入
   ↓           ↓          ↓         ↓
SD卡记录 ← RTC时间戳 ← 系统状态 ← LED指示
```

---

## 模块集成方案

### 1. 模块依赖关系

```
调度器 (核心)
├── LED模块 (独立)
├── 按键模块 (独立)
├── 串口模块 (独立)
├── ADC模块 (依赖: DMA)
├── OLED模块 (依赖: I2C, 字体库)
├── RTC模块 (独立)
├── Flash模块 (独立)
└── SD卡模块 (依赖: FATFS, SDIO)
```

### 2. 资源分配表

| 资源类型 | 分配情况 | 使用模块 |
|----------|----------|----------|
| GPIO | PD0-1,3-5,8-13, PB6,8-9, PA0,9-10, PC0,8-12 | 全部模块 |
| DMA | DMA1_CH0(ADC), DMA1_CH2(UART), DMA0_CH6(I2C) | ADC, UART, OLED |
| 定时器 | SysTick(1ms) | 调度器 |
| 中断 | USART0, ADC0, SDIO, RTC | 串口, ADC, SD卡, RTC |
| 存储 | Flash(参数), SD卡(数据) | 配置, 日志 |

### 3. 任务调度表

| 任务名称 | 执行周期 | 优先级 | 功能描述 |
|----------|----------|--------|----------|
| led_task | 1ms | 最高 | LED状态更新 |
| btn_task | 5ms | 高 | 按键扫描处理 |
| uart_task | 5ms | 高 | 串口数据处理 |
| adc_task | 100ms | 中 | ADC数据采集 |
| oled_task | 500ms | 中 | OLED显示更新 |
| rtc_task | 500ms | 低 | RTC时间处理 |
| storage_task | 1000ms | 低 | 数据存储处理 |

---

## 工程配置步骤

### 1. CubeMX配置清单

#### 系统配置
```
RCC:
  - HSE: 25MHz Crystal
  - PLL: 168MHz
  - AHB: 168MHz
  - APB1: 42MHz
  - APB2: 84MHz

SYS:
  - Debug: Serial Wire
  - Timebase: SysTick
```

#### 外设配置
```
GPIO:
  - PD0,1,3,4,5: GPIO_Output (LED)
  - PB6: GPIO_Output (LED)
  - PD8-13: GPIO_Input (KEY)
  - PA0: GPIO_Input (KEYW)
  - PB8,9: I2C0_SCL/SDA (OLED)
  - PA9,10: USART0_TX/RX (UART)
  - PC0: ADC0_IN10 (ADC)
  - PC8-12: SDIO (SD Card)

USART0:
  - Baud Rate: 115200
  - Word Length: 8 Bits
  - Parity: None
  - Stop Bits: 1
  - DMA: RX Enable

I2C0:
  - Speed: 400kHz
  - Mode: Fast Mode
  - DMA: TX Enable

ADC0:
  - Resolution: 12 bits
  - Continuous Mode: Enable
  - DMA: Enable

SDIO:
  - Clock: 24MHz
  - Bus Width: 4-bit
  - DMA: Enable

RTC:
  - Clock Source: LSE
  - Format: BCD
  - Alarm: Enable
```

### 2. 工程文件结构

```
Project/
├── Core/
│   ├── Inc/
│   │   ├── main.h
│   │   ├── stm32f4xx_hal_conf.h
│   │   └── stm32f4xx_it.h
│   └── Src/
│       ├── main.c
│       ├── stm32f4xx_hal_msp.c
│       └── stm32f4xx_it.c
├── Components/
│   ├── bsp/
│   │   ├── mcu_cmic_gd32f470vet6.h
│   │   └── mcu_cmic_gd32f470vet6.c
│   ├── oled/
│   │   ├── oled.h
│   │   ├── oled.c
│   │   └── font.h
│   ├── fatfs/
│   │   ├── ff.h
│   │   ├── ff.c
│   │   ├── ffconf.h
│   │   ├── diskio.h
│   │   └── diskio.c
│   └── sd_card/
│       ├── sd_card.h
│       └── sd_card.c
├── APP/
│   ├── scheduler.h
│   ├── scheduler.c
│   ├── led_app.h
│   ├── led_app.c
│   ├── btn_app.h
│   ├── btn_app.c
│   ├── uart_app.h
│   ├── uart_app.c
│   ├── adc_app.h
│   ├── adc_app.c
│   ├── oled_app.h
│   ├── oled_app.c
│   ├── rtc_app.h
│   ├── rtc_app.c
│   ├── flash_app.h
│   ├── flash_app.c
│   ├── sd_app.h
│   └── sd_app.c
└── Drivers/
    └── GD32F4xx_HAL_Driver/
```

---

## 代码组织结构

### 1. 主函数框架

```c
#include "main.h"
#include "scheduler.h"
#include "mcu_cmic_gd32f470vet6.h"

int main(void)
{
    /* 系统初始化 */
    system_init();
    
    /* 硬件初始化 */
    hardware_init();
    
    /* 应用初始化 */
    application_init();
    
    /* 调度器初始化 */
    scheduler_init();
    
    /* 启动信息 */
    startup_message();
    
    /* 主循环 */
    while(1)
    {
        scheduler_run();
        
        /* 低功耗处理 */
        power_management();
    }
}
```

### 2. 系统初始化函数

```c
void system_init(void)
{
    /* HAL库初始化 */
    HAL_Init();
    
    /* 系统时钟配置 */
    SystemClock_Config();
    
    /* SysTick配置 */
    systick_config();
    
    /* 中断优先级分组 */
    NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);
}

void hardware_init(void)
{
    /* GPIO初始化 */
    bsp_led_init();
    bsp_btn_init();
    
    /* 通信接口初始化 */
    bsp_usart_init();
    bsp_oled_init();
    
    /* 采集接口初始化 */
    bsp_adc_init();
    
    /* 存储接口初始化 */
    bsp_flash_init();
    bsp_sd_init();
    
    /* 时钟初始化 */
    bsp_rtc_init();
}

void application_init(void)
{
    /* 加载配置参数 */
    load_system_config();
    
    /* 初始化应用变量 */
    init_application_variables();
    
    /* 显示启动画面 */
    oled_display_startup();
}
```

### 3. 全局变量定义

```c
/* 系统状态变量 */
typedef struct {
    uint8_t sampling_enabled;      // 采样使能
    uint8_t hide_mode;             // 加密模式
    uint16_t sampling_period;      // 采样周期(秒)
    uint16_t voltage_ratio;        // 电压比例(×100)
    uint16_t voltage_limit;        // 电压限制(×100)
    uint32_t sample_count;         // 采样计数
    uint32_t error_count;          // 错误计数
} system_status_t;

system_status_t g_system_status = {
    .sampling_enabled = 0,
    .hide_mode = 0,
    .sampling_period = 5,
    .voltage_ratio = 199,
    .voltage_limit = 1011,
    .sample_count = 0,
    .error_count = 0
};

/* 数据缓冲区 */
uint8_t ucLed[6] = {0,0,0,0,0,0};
uint16_t adc_value[1] = {0};
uint8_t uart_dma_buffer[256] = {0};
volatile uint8_t rx_flag = 0;

/* 按键状态变量 */
uint8_t key_val = 0, key_old = 0, key_down = 0, key_up = 0;
```

---

## 系统初始化流程

### 1. 启动流程图

```
系统上电
    ↓
硬件初始化
    ↓
时钟配置
    ↓
外设初始化
    ↓
参数加载
    ↓
调度器启动
    ↓
主循环运行
```

### 2. 详细初始化代码

```c
void SystemClock_Config(void)
{
    RCC_OscInitTypeDef RCC_OscInitStruct = {0};
    RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

    /* 配置主振荡器 */
    RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
    RCC_OscInitStruct.HSEState = RCC_HSE_ON;
    RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
    RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
    RCC_OscInitStruct.PLL.PLLM = 25;
    RCC_OscInitStruct.PLL.PLLN = 336;
    RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
    RCC_OscInitStruct.PLL.PLLQ = 7;
    HAL_RCC_OscConfig(&RCC_OscInitStruct);

    /* 配置系统时钟 */
    RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                                |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
    RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
    RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
    RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
    RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;
    HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5);
}

void load_system_config(void)
{
    /* 从Flash读取配置参数 */
    if(flash_read_config(&g_system_status) != 0)
    {
        /* 使用默认配置 */
        g_system_status.sampling_enabled = 0;
        g_system_status.hide_mode = 0;
        g_system_status.sampling_period = 5;
        g_system_status.voltage_ratio = 199;
        g_system_status.voltage_limit = 1011;
        
        /* 保存默认配置到Flash */
        flash_save_config(&g_system_status);
    }
}

void startup_message(void)
{
    /* 串口启动信息 */
    printf("====system init====\r\n");
    printf("Device_ID:2025-CMIC-2025916750\r\n");
    printf("Firmware Version: V1.0.0\r\n");
    printf("Build Date: %s %s\r\n", __DATE__, __TIME__);
    printf("====system ready====\r\n");
    
    /* OLED启动画面 */
    OLED_Clear();
    oled_printf(0, 0, "GD32F470VET6");
    oled_printf(0, 1, "System Ready");
    oled_printf(0, 2, "Version 1.0");
    oled_printf(0, 3, "2025-01-01");
    
    delay_ms(2000);
    OLED_Clear();
}
```

---

## 主要功能实现

### 1. 数据采集功能

```c
void adc_task(void)
{
    static uint32_t last_sample_time = 0;
    uint32_t current_time = get_system_ms();
    
    /* 处理ADC数据 */
    uint16_t filtered_adc = adc_moving_average_filter(adc_value[0]);
    uint32_t voltage_mv = adc_to_actual_voltage_mv(filtered_adc, g_system_status.voltage_ratio);
    
    /* 检查采样条件 */
    if(g_system_status.sampling_enabled)
    {
        if(current_time - last_sample_time >= g_system_status.sampling_period * 1000)
        {
            last_sample_time = current_time;
            
            /* 记录数据 */
            record_sample_data(voltage_mv);
            g_system_status.sample_count++;
            
            /* 串口输出 */
            if(g_system_status.hide_mode)
            {
                /* 加密输出 */
                output_encrypted_data(voltage_mv);
            }
            else
            {
                /* 正常输出 */
                printf("Voltage: %lu.%02luV\r\n", voltage_mv/1000, (voltage_mv%1000)/10);
            }
            
            /* 检查限制 */
            if(voltage_mv > g_system_status.voltage_limit * 10)
            {
                printf("WARNING: Voltage limit exceeded!\r\n");
                ucLed[1] = 1;  // 点亮报警LED
            }
            else
            {
                ucLed[1] = 0;
            }
        }
    }
}
```

### 2. 人机交互功能

```c
void btn_task(void)
{
    /* 按键扫描 */
    key_val = key_read();
    key_down = key_val & (key_old ^ key_val);
    key_up = ~key_val & (key_old ^ key_val);
    key_old = key_val;
    
    /* 按键处理 */
    if(key_down == 1)  // KEY1: 切换采样状态
    {
        g_system_status.sampling_enabled ^= 1;
        printf("Sampling %s\r\n", g_system_status.sampling_enabled ? "started" : "stopped");
        ucLed[0] = g_system_status.sampling_enabled;
    }
    else if(key_down == 2)  // KEY2: 设置采样周期5秒
    {
        g_system_status.sampling_period = 5;
        printf("Sampling period set to 5 seconds\r\n");
    }
    else if(key_down == 3)  // KEY3: 设置采样周期10秒
    {
        g_system_status.sampling_period = 10;
        printf("Sampling period set to 10 seconds\r\n");
    }
    else if(key_down == 4)  // KEY4: 设置采样周期15秒
    {
        g_system_status.sampling_period = 15;
        printf("Sampling period set to 15 seconds\r\n");
    }
    else if(key_down == 5)  // KEY5: 保存配置
    {
        flash_save_config(&g_system_status);
        printf("Configuration saved\r\n");
    }
    else if(key_down == 6)  // KEY6: 系统复位
    {
        printf("System reset...\r\n");
        delay_ms(100);
        NVIC_SystemReset();
    }
}

void oled_task(void)
{
    static uint32_t display_mode = 0;
    
    if(g_system_status.sampling_enabled)
    {
        /* 采样模式显示 */
        rtc_parameter_struct rtc_time;
        rtc_current_time_get(&rtc_time);
        
        uint8_t hour = bcd_to_decimal(rtc_time.hour);
        uint8_t minute = bcd_to_decimal(rtc_time.minute);
        uint8_t second = bcd_to_decimal(rtc_time.second);
        
        oled_printf(0, 0, "%02d:%02d:%02d", hour, minute, second);
        
        uint32_t voltage_mv = adc_to_actual_voltage_mv(adc_value[0], g_system_status.voltage_ratio);
        oled_printf(0, 1, "%lu.%02luV      ", voltage_mv/1000, (voltage_mv%1000)/10);
        
        oled_printf(0, 2, "Period:%ds", g_system_status.sampling_period);
        oled_printf(0, 3, "Count:%lu", g_system_status.sample_count);
    }
    else
    {
        /* 空闲模式显示 */
        switch(display_mode % 3)
        {
            case 0:
                oled_printf(0, 0, "System Idle");
                oled_printf(0, 1, "Press KEY1");
                oled_printf(0, 2, "to start");
                oled_printf(0, 3, "sampling");
                break;
            case 1:
                oled_printf(0, 0, "Config:");
                oled_printf(0, 1, "Ratio:%d.%02d", g_system_status.voltage_ratio/100, g_system_status.voltage_ratio%100);
                oled_printf(0, 2, "Limit:%d.%02dV", g_system_status.voltage_limit/100, g_system_status.voltage_limit%100);
                oled_printf(0, 3, "Hide:%s", g_system_status.hide_mode ? "ON" : "OFF");
                break;
            case 2:
                oled_printf(0, 0, "Statistics:");
                oled_printf(0, 1, "Samples:%lu", g_system_status.sample_count);
                oled_printf(0, 2, "Errors:%lu", g_system_status.error_count);
                oled_printf(0, 3, "Uptime:%lus", get_system_ms()/1000);
                break;
        }
        display_mode++;
    }
}
```

### 3. 数据存储功能

```c
void storage_task(void)
{
    static uint32_t last_save_time = 0;
    uint32_t current_time = get_system_ms();
    
    /* 每分钟保存一次数据 */
    if(current_time - last_save_time >= 60000)
    {
        last_save_time = current_time;
        
        if(g_system_status.sampling_enabled)
        {
            /* 保存采样数据到SD卡 */
            save_data_to_sd();
        }
        
        /* 保存系统状态到Flash */
        flash_save_config(&g_system_status);
    }
}

void save_data_to_sd(void)
{
    char filename[32];
    char data_line[128];
    rtc_parameter_struct rtc_time;
    
    /* 获取当前时间 */
    rtc_current_time_get(&rtc_time);
    
    /* 生成文件名 */
    sprintf(filename, "data_%02d%02d%02d.csv", 
            bcd_to_decimal(rtc_time.year),
            bcd_to_decimal(rtc_time.month),
            bcd_to_decimal(rtc_time.date));
    
    /* 生成数据行 */
    uint32_t voltage_mv = adc_to_actual_voltage_mv(adc_value[0], g_system_status.voltage_ratio);
    sprintf(data_line, "%02d:%02d:%02d,%lu.%02lu\r\n",
            bcd_to_decimal(rtc_time.hour),
            bcd_to_decimal(rtc_time.minute),
            bcd_to_decimal(rtc_time.second),
            voltage_mv/1000, (voltage_mv%1000)/10);
    
    /* 写入SD卡 */
    if(sd_write_file(filename, data_line, strlen(data_line)) == 0)
    {
        printf("Data saved to SD card\r\n");
    }
    else
    {
        printf("SD card write error\r\n");
        g_system_status.error_count++;
    }
}
```

---

## 调试与测试

### 1. 调试配置

```c
/* 调试宏定义 */
#define DEBUG_ENABLE    1

#if DEBUG_ENABLE
    #define DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt, ##__VA_ARGS__)
    #define DEBUG_TASK_TIME 1
#else
    #define DEBUG_PRINT(fmt, ...)
    #define DEBUG_TASK_TIME 0
#endif

/* 任务执行时间测量 */
#if DEBUG_TASK_TIME
void measure_task_time(void (*task_func)(void), const char* task_name)
{
    uint32_t start_time = get_system_ms();
    task_func();
    uint32_t end_time = get_system_ms();
    
    if(end_time - start_time > 1)  // 只记录超过1ms的任务
    {
        DEBUG_PRINT("%s execution time: %lu ms\r\n", task_name, end_time - start_time);
    }
}
#endif
```

### 2. 系统监控

```c
void system_monitor_task(void)
{
    static uint32_t last_monitor_time = 0;
    uint32_t current_time = get_system_ms();
    
    if(current_time - last_monitor_time >= 10000)  // 每10秒监控一次
    {
        last_monitor_time = current_time;
        
        /* 内存使用监控 */
        uint32_t stack_usage = get_stack_usage();
        uint32_t heap_usage = get_heap_usage();
        
        DEBUG_PRINT("Stack usage: %lu bytes\r\n", stack_usage);
        DEBUG_PRINT("Heap usage: %lu bytes\r\n", heap_usage);
        
        /* 任务执行统计 */
        DEBUG_PRINT("System uptime: %lu seconds\r\n", current_time/1000);
        DEBUG_PRINT("Sample count: %lu\r\n", g_system_status.sample_count);
        DEBUG_PRINT("Error count: %lu\r\n", g_system_status.error_count);
        
        /* 硬件状态检查 */
        if(!check_sd_card_status())
        {
            DEBUG_PRINT("SD card error detected\r\n");
            g_system_status.error_count++;
        }
        
        if(!check_rtc_status())
        {
            DEBUG_PRINT("RTC error detected\r\n");
            g_system_status.error_count++;
        }
    }
}
```

### 3. 测试用例

```c
void run_system_tests(void)
{
    printf("Starting system tests...\r\n");
    
    /* LED测试 */
    test_led_functionality();
    
    /* 按键测试 */
    test_button_functionality();
    
    /* ADC测试 */
    test_adc_functionality();
    
    /* OLED测试 */
    test_oled_functionality();
    
    /* 串口测试 */
    test_uart_functionality();
    
    /* 存储测试 */
    test_storage_functionality();
    
    printf("System tests completed\r\n");
}

void test_adc_functionality(void)
{
    printf("Testing ADC...\r\n");
    
    for(int i = 0; i < 10; i++)
    {
        uint16_t adc_val = adc_value[0];
        uint32_t voltage = adc_to_voltage_mv(adc_val);
        
        printf("ADC[%d]: %d -> %lu.%02luV\r\n", 
               i, adc_val, voltage/1000, (voltage%1000)/10);
        
        delay_ms(100);
    }
    
    printf("ADC test completed\r\n");
}
```

---

## 性能优化

### 1. 内存优化

```c
/* 使用const修饰只读数据 */
const uint8_t led_pin_map[6] = {
    GPIO_PIN_0, GPIO_PIN_1, GPIO_PIN_6, 
    GPIO_PIN_3, GPIO_PIN_4, GPIO_PIN_5
};

/* 使用位域减少结构体大小 */
typedef struct {
    uint8_t sampling_enabled : 1;
    uint8_t hide_mode : 1;
    uint8_t reserved : 6;
    uint16_t sampling_period;
    uint16_t voltage_ratio;
    uint16_t voltage_limit;
} compact_config_t;

/* 使用栈变量减少全局变量 */
void process_data(void)
{
    uint8_t local_buffer[64];  // 使用栈变量
    // 处理完成后自动释放
}
```

### 2. 执行效率优化

```c
/* 使用查表法替代计算 */
const uint16_t voltage_table[4096] = {
    // 预计算的电压值表
};

uint32_t adc_to_voltage_fast(uint16_t adc_val)
{
    if(adc_val < 4096)
        return voltage_table[adc_val];
    else
        return 3300;
}

/* 减少浮点运算 */
uint32_t calculate_voltage_fixed_point(uint16_t adc_val, uint16_t ratio)
{
    // 使用定点数运算替代浮点运算
    uint64_t temp = (uint64_t)adc_val * ratio * 3300;
    return (uint32_t)(temp / (4095 * 100));
}
```

### 3. 功耗优化

```c
void power_management(void)
{
    static uint32_t last_activity = 0;
    uint32_t current_time = get_system_ms();
    
    /* 检查系统活动 */
    if(is_system_idle() && (current_time - last_activity > 5000))
    {
        /* 进入低功耗模式 */
        enter_sleep_mode();
        last_activity = current_time;
    }
}

void enter_sleep_mode(void)
{
    /* 关闭不必要的外设 */
    // HAL_ADC_Stop_DMA(&hadc1);
    
    /* 进入STOP模式 */
    // HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
    
    /* 唤醒后恢复外设 */
    // HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_value, 1);
}
```

---

## 项目扩展指南

### 1. 添加新模块

```c
/* 1. 在scheduler.c中添加任务 */
static task_t scheduler_task[] =
{
    // 现有任务...
    {new_module_task, 200, 0}  // 新模块任务，200ms周期
};

/* 2. 实现新模块任务函数 */
void new_module_task(void)
{
    // 新模块功能实现
}

/* 3. 在main.c中添加初始化 */
void hardware_init(void)
{
    // 现有初始化...
    bsp_new_module_init();  // 新模块初始化
}
```

### 2. 扩展通信协议

```c
/* 扩展串口命令 */
void process_uart_command(char *cmd)
{
    // 现有命令处理...
    
    if(strncmp(cmd, "new_cmd", 7) == 0)
    {
        handle_new_command(cmd);
    }
}

void handle_new_command(char *cmd)
{
    // 新命令处理逻辑
    printf("New command executed\r\n");
}
```

### 3. 增加传感器支持

```c
/* 添加新传感器 */
typedef struct {
    uint16_t temperature;
    uint16_t humidity;
    uint16_t pressure;
} sensor_data_t;

void sensor_task(void)
{
    static sensor_data_t sensor_data;
    
    /* 读取传感器数据 */
    sensor_data.temperature = read_temperature();
    sensor_data.humidity = read_humidity();
    sensor_data.pressure = read_pressure();
    
    /* 处理传感器数据 */
    process_sensor_data(&sensor_data);
}
```

### 4. 网络功能扩展

```c
/* 添加WiFi/以太网支持 */
void network_task(void)
{
    static uint8_t network_state = 0;
    
    switch(network_state)
    {
        case 0:  // 初始化网络
            if(init_network() == 0)
                network_state = 1;
            break;
            
        case 1:  // 连接服务器
            if(connect_server() == 0)
                network_state = 2;
            break;
            
        case 2:  // 发送数据
            send_data_to_server();
            break;
    }
}
```

---

## 总结

本综合工程文档详细介绍了基于GD32F470VET6的多模块嵌入式系统的完整实现过程，包括：

1. **系统设计**: 硬件架构、软件架构、模块集成方案
2. **工程配置**: CubeMX配置、文件结构、代码组织
3. **功能实现**: 数据采集、人机交互、数据存储
4. **质量保证**: 调试方法、测试用例、性能监控
5. **优化策略**: 内存优化、执行效率、功耗管理
6. **扩展指南**: 新模块添加、协议扩展、功能增强

通过本文档，您可以：
- 理解复杂嵌入式系统的设计思路和实现方法
- 掌握多模块集成的关键技术和注意事项
- 学会使用调度器管理多任务系统
- 获得完整的工程开发和调试经验
- 具备独立开发类似系统的能力

这个综合工程为嵌入式系统开发提供了一个完整的参考模板，可以根据具体需求进行修改和扩展，适用于各种实际应用场景。
