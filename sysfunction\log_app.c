
#include "cmic_gd32f470vet6.h"
#include "log_app.h"
#include "sd_app.h"
#include "rtc_app.h"
#include "usart_app.h"
#include <stdio.h>
#include <string.h>
#include <stdarg.h>

// Log configuration storage in Flash
typedef struct {
    uint32_t boot_count;     // Boot counter (starts from 0)
    uint8_t initialized;     // Initialization flag (0=not initialized, 1=initialized)
    uint8_t reserved[3];     // Reserved for alignment
    uint32_t magic;          // Data validity magic number
} log_config_t;

#define LOG_CONFIG_FLASH_ADDR 0x00040000    // Flash address for log config
#define LOG_CONFIG_MAGIC 0xABCDEF12         // Magic number for validation

// Log file management
static FIL log_file;
static uint8_t log_file_open = 0;
static log_config_t log_config;
static char log_filename[64];
static uint32_t sync_counter = 0;  // Counter for periodic sync
static uint32_t current_open_log_number = 0;  // Track currently open log file number

/**
 * @brief Read log configuration from Flash
 *
 * @param config Pointer to log_config_t structure to fill
 * @return uint8_t 1 if valid config found, 0 if not found or invalid
 */
uint8_t read_log_config_from_flash(log_config_t* config)
{
    // Read log config data from Flash
    spi_flash_buffer_read((uint8_t*)config, LOG_CONFIG_FLASH_ADDR, sizeof(log_config_t));

    // Verify magic number
    if(config->magic == LOG_CONFIG_MAGIC)
    {
        return 1;  // Valid config found
    }

    // Initialize default config for first use
    config->boot_count = 0;
    config->initialized = 0;
    config->magic = LOG_CONFIG_MAGIC;
    return 0;  // No valid config found
}

/**
 * @brief Write log configuration to Flash
 *
 * @param config Pointer to log_config_t structure to write
 * @return uint8_t 1 if success, 0 if failed
 */
uint8_t write_log_config_to_flash(log_config_t* config)
{
    // Erase the sector first
    spi_flash_sector_erase(LOG_CONFIG_FLASH_ADDR);

    // Write log config data to Flash
    spi_flash_buffer_write((uint8_t*)config, LOG_CONFIG_FLASH_ADDR, sizeof(log_config_t));

    // Verify write operation
    log_config_t verify_data;
    spi_flash_buffer_read((uint8_t*)&verify_data, LOG_CONFIG_FLASH_ADDR, sizeof(log_config_t));

    return (verify_data.magic == LOG_CONFIG_MAGIC &&
            verify_data.boot_count == config->boot_count &&
            verify_data.initialized == config->initialized) ? 1 : 0;
}

/**
 * @brief Get current boot count
 *
 * @return uint32_t Current boot count
 */
uint32_t get_boot_count(void)
{
    return log_config.boot_count;
}

/**
 * @brief Check if log system is initialized
 *
 * @return uint8_t 1 if initialized, 0 if not
 */
uint8_t is_log_initialized(void)
{
    return log_config.initialized;
}

/**
 * @brief Set log system as initialized and save to Flash
 *
 * @return uint8_t 1 if success, 0 if failed
 */
uint8_t set_log_initialized(void)
{
    log_config.initialized = 1;
    return write_log_config_to_flash(&log_config);
}

/**
 * @brief Check if log file is open
 *
 * @return uint8_t 1 if open, 0 if not
 */
uint8_t is_log_file_open(void)
{
    return log_file_open;
}

/**
 * @brief Set boot count and save to Flash
 *
 * @param count Boot count value to set
 * @return uint8_t 1 if success, 0 if failed
 */
uint8_t set_boot_count_and_save(uint32_t count)
{
    log_config.boot_count = count;
    return write_log_config_to_flash(&log_config);
}

/**
 * @brief Reset log system to uninitialized state
 *
 * @return uint8_t 1 if success, 0 if failed
 */
uint8_t reset_log_system(void)
{
    log_config.boot_count = 0;
    log_config.initialized = 0;
    return write_log_config_to_flash(&log_config);
}

/**
 * @brief Increment boot count and save to Flash
 *
 * @return uint8_t 1 if success, 0 if failed
 */
uint8_t increment_boot_count(void)
{
    // Create a temporary config with incremented boot count
    log_config_t temp_config = log_config;
    temp_config.boot_count++;

    // Try to write to Flash first
    if(write_log_config_to_flash(&temp_config))
    {
        // Flash write successful, update memory
        log_config.boot_count++;
        return 1;
    }
    else
    {
        // Flash write failed, keep current boot_count
        return 0;
    }
}

/**
 * @brief Initialize log system
 */
void log_init(void)
{
    // Initialize log file state - always start with closed state
    log_file_open = 0;

    // Force close any potentially open file handles from previous session
    f_close(&log_file);

    // Read log configuration from Flash
    read_log_config_from_flash(&log_config);

    // Manual control mode - do not auto-create log files
    // Log files will be created only through manual commands:
    // - log0 close: creates and completes log0.txt
    // - log[N] open: opens log[N].txt for operation
    if(log_config.initialized)
    {
        // Try to auto-mount TF card but do NOT start any session automatically
        if(init_filesystem())
        {
            // File system is ready, but wait for manual commands
            // No automatic log file creation
        }
        else
        {
            // TF card not available, will try again when manual command is issued
        }
    }
    // If not initialized, wait for user to run 'test' command
}

/**
 * @brief Smart recovery check - analyze last session and determine recovery action
 *
 * @return uint8_t Recovery action:
 *         0 = Normal power-on, create next log file
 *         1 = Previous session incomplete, continue with same boot_count
 *         2 = File system corruption, start fresh
 */
uint8_t smart_recovery_check(void)
{
    char current_log_filename[64];
    snprintf(current_log_filename, sizeof(current_log_filename), "0:/log/log%lu.txt", log_config.boot_count);

    FIL test_file;
    FRESULT test_result = f_open(&test_file, current_log_filename, FA_OPEN_EXISTING | FA_READ);

    if(test_result == FR_OK)
    {
        // File exists, check if it's complete
        FSIZE_t file_size = f_size(&test_file);
        f_close(&test_file);

        if(file_size > 0)
        {
            // File exists and has content, this is normal power-on after previous session
            // Create next log file (increment boot_count)
            return 0;
        }
        else
        {
            // File exists but empty, previous session was interrupted
            // Continue with same boot_count (overwrite empty file)
            return 1;
        }
    }
    else
    {
        // File doesn't exist, check if this is expected
        if(log_config.boot_count == 0)
        {
            // This shouldn't happen if system is initialized
            // File system might be corrupted
            return 2;
        }
        else
        {
            // Previous file doesn't exist, continue with current boot_count
            return 1;
        }
    }
}

/**
 * @brief Write message to log file
 *
 * @param message Message to write
 */
void log_write(const char* message)
{
    if(!log_file_open)
    {
        return;  // No log file open, ignore
    }

    // Write directly to file with timestamp
    UINT bytes_written;
    char timestamped_msg[256];
    char current_time[32];

    rtc_get_time_string(current_time, sizeof(current_time));
    snprintf(timestamped_msg, sizeof(timestamped_msg), "%s %s\r\n", current_time, message);

    FRESULT result = f_write(&log_file, timestamped_msg, strlen(timestamped_msg), &bytes_written);
    if(result == FR_OK)
    {
        f_sync(&log_file);  // Force sync to ensure data is written
    }
}

/**
 * @brief Write raw message to log file without adding timestamp
 *
 * @param message Message to write (should already include timestamp and \r\n)
 */
void log_write_raw(const char* message)
{
    if(!log_file_open)
    {
        return;  // No log file open, ignore
    }

    UINT bytes_written;
    FRESULT result = f_write(&log_file, message, strlen(message), &bytes_written);
    if(result == FR_OK)
    {
        f_write(&log_file, "\r\n", 2, &bytes_written);
        f_sync(&log_file);  // Force sync to ensure data is written
    }
}

/**
 * @brief Write formatted message to log
 *
 * @param format Format string
 * @param ... Arguments
 */
void log_write_formatted(const char* format, ...)
{
    char message[256];
    va_list args;

    va_start(args, format);
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);

    // Check if we should use pre-test buffer (declared in usart_app.h)
    if(pre_test_logging)
    {
        add_to_pre_test_buffer(message);
    }
    else
    {
        log_write(message);
    }
}

/**
 * @brief Start logging session (create log file and start recording)
 *
 * @return uint8_t 1 if success, 0 if failed
 */
uint8_t log_start_session(void)
{
    // Force close any existing file first
    if(log_file_open)
    {
        f_sync(&log_file);
        f_close(&log_file);
        log_file_open = 0;
    }

    // Generate log filename: log{boot_count}.txt
    snprintf(log_filename, sizeof(log_filename), "0:/log/log%lu.txt", log_config.boot_count);

    // Try to create and open log file
    FRESULT result = f_open(&log_file, log_filename, FA_CREATE_ALWAYS | FA_WRITE);
    if(result != FR_OK)
    {
        return 0;  // Failed to create log file
    }

    log_file_open = 1;

    // Write session start message
    log_write_formatted("=== Boot #%lu - Session Start ===", log_config.boot_count);

    return 1;
}

/**
 * @brief Close log file
 */
/**
 * @brief Periodic sync task - call every 100ms to ensure data integrity
 */
void log_periodic_sync(void)
{
    if(log_file_open)
    {
        sync_counter++;
        // Sync every 10 calls (1 second)
        if(sync_counter >= 10)
        {
            f_sync(&log_file);
            sync_counter = 0;
        }
    }
}

void log_close(void)
{
    if(log_file_open)
    {
        f_sync(&log_file);
        f_close(&log_file);
        log_file_open = 0;
        sync_counter = 0;
        current_open_log_number = 0;  // Clear current open log number
    }
}

/**
 * @brief Get current log file number based on actually opened file
 *
 * @return uint32_t Current log file number
 */
uint32_t get_current_log_number(void)
{
    return current_open_log_number;
}

/**
 * @brief Close log file by specific number with validation
 *
 * @param log_number Log file number to close (0-3)
 * @return uint8_t 1 if success, 0 if failed
 */
uint8_t close_log_file_by_number(uint32_t log_number)
{
    // Validate log number range
    if(log_number > 3)
    {
        my_printf(DEBUG_USART, "Invalid log number. Use log0-log3\r\n");
        return 0;
    }

    // Check if any log file is currently open
    if(!log_file_open)
    {
        my_printf(DEBUG_USART, "No log file is currently open\r\n");
        return 0;
    }

    // Check if the requested log number matches current open file
    uint32_t current_log_num = get_current_log_number();
    if(current_log_num != log_number)
    {
        my_printf(DEBUG_USART, "Current open file is log%lu.txt, not log%lu.txt\r\n",
                 current_log_num, log_number);
        return 0;
    }

    // Write closing marker to log file
    log_write_formatted("=== log%lu.txt manually closed ===", log_number);

    // Close the file
    log_close();

    // Output confirmation message
    my_printf(DEBUG_USART, "log%lu.txt closed successfully\r\n", log_number);

    return 1;
}

/**
 * @brief Open log file by specific number
 *
 * @param log_number Log file number to open (0-3)
 * @return uint8_t 1 if success, 0 if failed
 */
uint8_t open_log_file_by_number(uint32_t log_number)
{
    // Validate log number range
    if(log_number > 3)
    {
        my_printf(DEBUG_USART, "Invalid log number. Use log0-log3\r\n");
        return 0;
    }

    // Check if a file is already open
    if(log_file_open)
    {
        uint32_t current_log_num = get_current_log_number();
        if(current_log_num == log_number)
        {
            my_printf(DEBUG_USART, "log%lu.txt is already open\r\n", log_number);
            return 1;
        }
        else
        {
            my_printf(DEBUG_USART, "log%lu.txt is currently open. Close it first.\r\n", current_log_num);
            return 0;
        }
    }

    // Ensure TF card is mounted
    if(!init_filesystem())
    {
        my_printf(DEBUG_USART, "TF card not available\r\n");
        return 0;
    }

    // Set boot_count to the requested log number and open the file
    log_config.boot_count = log_number;

    // Try to open the log file
    if(log_start_session())
    {
        // Set the current open log number
        current_open_log_number = log_number;

        my_printf(DEBUG_USART, "log%lu.txt opened successfully\r\n", log_number);

        // Special handling for log0 - write pre-test buffer data
        if(log_number == 0)
        {
            extern void flush_pre_test_buffer_to_log(void);
            flush_pre_test_buffer_to_log();
            my_printf(DEBUG_USART, "Pre-test data written to log0.txt\r\n");
        }

        return 1;
    }
    else
    {
        my_printf(DEBUG_USART, "Failed to open log%lu.txt\r\n", log_number);
        return 0;
    }
}
