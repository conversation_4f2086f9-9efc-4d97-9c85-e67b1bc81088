/*-----------------------------------------------------------------------*/
/* Low level disk I/O module skeleton for FatFs     (C)ChaN, 2007        */
/*-----------------------------------------------------------------------*/
/* This is a stub disk I/O module that acts as front end of the existing */
/* disk I/O modules and attach it to Fat<PERSON>s module with common interface. */
/*-----------------------------------------------------------------------*/

#include "diskio.h"
#include "sdio_sdcard.h"
#include "cmic_gd32f470vet6.h"
#include <stdio.h>

// DEBUG_USART is already defined in cmic_gd32f470vet6.h

/*-----------------------------------------------------------------------*/
/* Correspondence between physical drive number and physical drive.      */
/*-----------------------------------------------------------------------*/
#define BLOCKSIZE   512
// #define BUSMODE_4BIT  // Disabled for stability - use 1-bit mode instead
// #define DMA_MODE      // Disabled for stability - use polling mode instead

/*-----------------------------------------------------------------------*/
/* Inidialize a Drive                                                    */
/*-----------------------------------------------------------------------*/

DSTATUS disk_initialize (
    BYTE drv                /* Physical drive nmuber (0..) */
)
{
    sd_error_enum status;
    sd_card_info_struct sd_cardinfo;
    uint32_t cardstate = 0;

    if(0 == drv){
        // Initialize SD card
        status = sd_init();
        if(SD_OK != status){
            // Debug: SD init failed
            return STA_NOINIT;
        }

        // Get card information
        status = sd_card_information_get(&sd_cardinfo);
        if(SD_OK != status){
            // Debug: Failed to get card info
            return STA_NOINIT;
        }

        // Select card
        status = sd_card_select_deselect(sd_cardinfo.card_rca);
        if(SD_OK != status){
            // Debug: Failed to select card
            return STA_NOINIT;
        }

        // Get card status
        status = sd_cardstatus_get(&cardstate);
        if(cardstate & 0x02000000){
            // Card is locked - this should not happen in normal operation
            return STA_NOINIT;
        }

        // Configure bus mode
        if(SD_OK == status){
#ifdef BUSMODE_4BIT
            status = sd_bus_mode_config(SDIO_BUSMODE_4BIT);
#else
            status = sd_bus_mode_config(SDIO_BUSMODE_1BIT);
#endif /* BUSMODE_4BIT */
        }
        if(SD_OK != status){
            // Debug: Bus mode config failed
            return STA_NOINIT;
        }

        // Configure transfer mode
#ifdef DMA_MODE
        status = sd_transfer_mode_config(SD_DMA_MODE);
#else
        status = sd_transfer_mode_config(SD_POLLING_MODE);
#endif /* DMA_MODE */

        if(SD_OK == status){
            return 0;  // Success
        }else{
            // Debug: Transfer mode config failed
            return STA_NOINIT;
        }
    }else{
        return STA_NOINIT;  // Invalid drive number
    }
}



/*-----------------------------------------------------------------------*/
/* Return Disk Status                                                    */
/*-----------------------------------------------------------------------*/

DSTATUS disk_status (
    BYTE drv         /* Physical drive nmuber (0..) */
)
{
    if(0 == drv){
        // Check if SD card is present and initialized
        // For now, assume no write protection (most TF cards don't have physical WP switch)
        return 0;  /* Return 0 for success (no error flags) */
    }

    return STA_NOINIT;
}



/*-----------------------------------------------------------------------*/
/* Read Sector(s)                                                        */
/*-----------------------------------------------------------------------*/

DRESULT disk_read (
    BYTE pdrv,         /* Physical drive nmuber (0..) */
    BYTE *buff,        /* Data buffer to store read data */
    LBA_t sector,      /* Sector address (LBA) */
    UINT count         /* Number of sectors to read (1..128) */
)
{
    sd_error_enum status = SD_ERROR;
    if(NULL == buff){
        return RES_PARERR;
    }
    if(!count){
        return RES_PARERR;
    }

    if(0 == pdrv){
        if(1 == count){
            // SD card driver expects byte address, will convert internally
            uint32_t byte_addr = sector * 512;
            status = sd_block_read((uint32_t *)(&buff[0]), byte_addr, BLOCKSIZE);
            // Debug info removed
        }else{
            uint32_t byte_addr = sector * 512;
            status = sd_multiblocks_read((uint32_t *)(&buff[0]), byte_addr, BLOCKSIZE, count);
            // Debug info removed
        }
    }
    if(SD_OK == status){
        return RES_OK;
    }
    return RES_ERROR;
}



/*-----------------------------------------------------------------------*/
/* Write Sector(s)                                                       */
/*-----------------------------------------------------------------------*/
/* The FatFs module will issue multiple sector transfer request
/  (count > 1) to the disk I/O layer. The disk function should process
/  the multiple sector transfer properly Do. not translate it into
/  multiple single sector transfers to the media, or the data read/write
/  performance may be drasticaly decreased. */

#if !FF_FS_READONLY
DRESULT disk_write (
    BYTE pdrv,           /* Physical drive nmuber (0..) */
    const BYTE *buff,    /* Data to be written */
    LBA_t sector,        /* Sector address (LBA) */
    UINT count           /* Number of sectors to write (1..128) */
)
{
    sd_error_enum status = SD_ERROR;
    if(NULL == buff){
        return RES_PARERR;
    }
    if(!count){
        return RES_PARERR;
    }

    if(0 == pdrv){
        if(1 == count){
            // SD card driver expects byte address, will convert internally
            uint32_t byte_addr = sector * 512;
            status = sd_block_write((uint32_t *)buff, byte_addr, BLOCKSIZE);
            // Auto-retry mechanism for write protection errors
            if (status == 5) { // SD_WP_VIOLATION
                // Calculate erase addresses (sector aligned)
                uint32_t erase_start = (byte_addr / 512) * 512;
                uint32_t erase_end = erase_start + 511;
                sd_error_enum erase_status = sd_erase(erase_start, erase_end);

                if (erase_status == SD_OK) {
                    // Retry write after erase
                    status = sd_block_write((uint32_t *)buff, byte_addr, BLOCKSIZE);
                }
            }
        }else{
            uint32_t byte_addr = sector * 512;
            status = sd_multiblocks_write((uint32_t *)buff, byte_addr, BLOCKSIZE, count);
            // Debug info removed
        }
    }
    if(SD_OK == status){
        return RES_OK;
    }
    // Silent error handling - errors are often recoverable
    return RES_ERROR;
}
#endif /* !FF_FS_READONLY */



/*-----------------------------------------------------------------------*/
/* Miscellaneous Functions                                               */
/*-----------------------------------------------------------------------*/

DRESULT disk_ioctl (
    BYTE pdrv,        /* Physical drive nmuber (0..) */
    BYTE cmd,         /* Control code */
    void *buff        /* Buffer to send/receive control data */
)
{
    DRESULT res = RES_ERROR;

    // Debug info removed for cleaner output

    if (pdrv == 0) {
        switch (cmd) {
            case CTRL_SYNC:         /* Make sure that no pending write process */
                // CTRL_SYNC - ensure no pending write operations
                res = RES_OK;
                break;

            case GET_SECTOR_COUNT:  /* Get number of sectors on the disk (DWORD) */
                {
                    uint32_t capacity_kb = sd_card_capacity_get();
                    uint32_t sector_count = capacity_kb * 2;  /* capacity(KB) * 2 = sectors(512B) */
                    *(DWORD*)buff = sector_count;
                    // Debug info removed
                    res = RES_OK;
                }
                break;

            case GET_SECTOR_SIZE:   /* Get R/W sector size (WORD) */
                *(WORD*)buff = 512;
                // Debug info removed
                res = RES_OK;
                break;

            case GET_BLOCK_SIZE:    /* Get erase block size in unit of sector (DWORD) */
                *(DWORD*)buff = 1;
                // Debug info removed
                res = RES_OK;
                break;

            case CTRL_TRIM: /* Force erased a block of sectors */
                // Debug info removed
                res = RES_OK;  /* Even if not implemented, return OK */
                break;

            default:
                res = RES_PARERR;
                break;
        }
    } else {
        res = RES_PARERR;
    }

    return res;
}
 
/*-----------------------------------------------------------------------*/
/* Get current time                                                      */
/*-----------------------------------------------------------------------*/ 
DWORD get_fattime(void)
{

  return 0;

}



